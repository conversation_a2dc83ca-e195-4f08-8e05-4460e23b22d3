// 查重页面状态
let plagiarismState = {
    isChecking: false,
    currentTab: 'text',
    textContent: '',
    uploadedFile: null,
    checkResult: null
};

// DOM 元素
let plagiarismElements = {};

// 初始化查重页面
document.addEventListener('DOMContentLoaded', function() {
    initializePlagiarismElements();
    initializeLucideIcons();
    bindPlagiarismEvents();
});

// 初始化DOM元素引用
function initializePlagiarismElements() {
    plagiarismElements = {
        textInput: document.getElementById('text-input'),
        charCount: document.getElementById('char-count'),
        fileInput: document.getElementById('file-input'),
        uploadArea: document.getElementById('upload-area'),
        checkBtn: document.getElementById('check-btn'),
        progressContainer: document.getElementById('progress-container'),
        progressFill: document.getElementById('progress-fill'),
        progressText: document.getElementById('progress-text'),
        progressDetail: document.getElementById('progress-detail'),
        resultContainer: document.getElementById('result-container'),
        detailsContainer: document.getElementById('details-container'),
        similarityRate: document.getElementById('similarity-rate'),
        similarityBadge: document.getElementById('similarity-badge'),
        totalWords: document.getElementById('total-words'),
        duplicateWords: document.getElementById('duplicate-words'),
        sourcesList: document.getElementById('sources-list'),
        detailsList: document.getElementById('details-list'),
        tabButtons: document.querySelectorAll('.tab-button'),
        tabContents: document.querySelectorAll('.tab-content'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn')
    };
}

// 绑定查重页面事件
function bindPlagiarismEvents() {
    // 文本输入事件
    if (plagiarismElements.textInput) {
        plagiarismElements.textInput.addEventListener('input', handleTextInput);
    }
    
    // 标签页切换
    plagiarismElements.tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 文件拖拽上传
    if (plagiarismElements.uploadArea) {
        plagiarismElements.uploadArea.addEventListener('dragover', handleDragOver);
        plagiarismElements.uploadArea.addEventListener('drop', handleDrop);
        plagiarismElements.uploadArea.addEventListener('dragleave', handleDragLeave);
    }
    
    // 移动端菜单
    if (plagiarismElements.mobileMenuBtn) {
        plagiarismElements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            plagiarismElements.sidebar && 
            !plagiarismElements.sidebar.contains(e.target) && 
            plagiarismElements.mobileMenuBtn &&
            !plagiarismElements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 处理文本输入
function handleTextInput() {
    const text = plagiarismElements.textInput.value;
    plagiarismState.textContent = text;
    
    // 更新字数统计
    if (plagiarismElements.charCount) {
        plagiarismElements.charCount.textContent = text.length;
    }
    
    // 更新检测按钮状态
    updateCheckButtonState();
}

// 更新检测按钮状态
function updateCheckButtonState() {
    const hasContent = plagiarismState.currentTab === 'text' 
        ? plagiarismState.textContent.trim().length > 0
        : plagiarismState.uploadedFile !== null;
    
    if (plagiarismElements.checkBtn) {
        plagiarismElements.checkBtn.disabled = !hasContent || plagiarismState.isChecking;
    }
}

// 切换标签页
function switchTab(tab) {
    plagiarismState.currentTab = tab;
    
    // 更新标签页样式
    plagiarismElements.tabButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('border-blue-500', 'text-blue-600', 'active');
            button.classList.remove('border-transparent', 'text-gray-500');
        } else {
            button.classList.remove('border-blue-500', 'text-blue-600', 'active');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });
    
    // 切换内容区域
    plagiarismElements.tabContents.forEach(content => {
        if (content.id === `${tab}-tab`) {
            content.classList.remove('hidden');
        } else {
            content.classList.add('hidden');
        }
    });
    
    updateCheckButtonState();
}

// 处理文件拖拽
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    plagiarismElements.uploadArea.classList.add('border-blue-500', 'bg-blue-100');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    plagiarismElements.uploadArea.classList.remove('border-blue-500', 'bg-blue-100');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    plagiarismElements.uploadArea.classList.remove('border-blue-500', 'bg-blue-100');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelection(files[0]);
    }
}

// 处理文件上传
function handleFileUpload(input) {
    if (input.files && input.files[0]) {
        handleFileSelection(input.files[0]);
    }
}

// 处理文件选择
function handleFileSelection(file) {
    // 验证文件类型
    const allowedTypes = ['.doc', '.docx', '.pdf', '.txt'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showToast('不支持的文件格式，请上传 Word、PDF 或文本文件', 'error');
        return;
    }
    
    // 验证文件大小
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
        showToast('文件大小超过限制，请上传小于20MB的文件', 'error');
        return;
    }
    
    plagiarismState.uploadedFile = file;
    
    // 更新UI显示
    updateUploadAreaUI(file);
    updateCheckButtonState();
}

// 更新上传区域UI
function updateUploadAreaUI(file) {
    const uploadArea = plagiarismElements.uploadArea;
    if (!uploadArea) return;
    
    uploadArea.innerHTML = `
        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <i data-lucide="file-check" class="w-8 h-8 text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-gray-900 mb-2">文件已选择</h3>
        <p class="text-gray-600 mb-2">${file.name}</p>
        <p class="text-sm text-gray-500 mb-4">大小: ${formatFileSize(file.size)}</p>
        <button class="btn btn-secondary" onclick="clearUploadedFile()">
            <i data-lucide="x" class="w-4 h-4 mr-2"></i>
            重新选择
        </button>
    `;
    
    initializeLucideIcons();
}

// 清除上传的文件
function clearUploadedFile() {
    plagiarismState.uploadedFile = null;
    
    // 重置上传区域UI
    const uploadArea = plagiarismElements.uploadArea;
    if (uploadArea) {
        uploadArea.innerHTML = `
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <i data-lucide="upload" class="w-10 h-10 text-white"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">上传论文文件</h3>
            <p class="text-gray-600 mb-2">拖拽文件到此处或点击上传</p>
            <p class="text-sm text-gray-500 mb-6">支持 Word(.doc, .docx)、PDF(.pdf)、文本(.txt) 格式，最大20MB</p>
            <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                <i data-lucide="upload" class="w-5 h-5 mr-2"></i>
                选择文件上传
            </button>
        `;
        initializeLucideIcons();
    }
    
    // 重置文件输入
    if (plagiarismElements.fileInput) {
        plagiarismElements.fileInput.value = '';
    }
    
    updateCheckButtonState();
}

// 开始查重检测
async function startPlagiarismCheck() {
    if (plagiarismState.isChecking) return;
    
    // 验证输入
    if (plagiarismState.currentTab === 'text' && !plagiarismState.textContent.trim()) {
        showToast('请输入要检测的文本内容', 'error');
        return;
    }
    
    if (plagiarismState.currentTab === 'file' && !plagiarismState.uploadedFile) {
        showToast('请选择要检测的文件', 'error');
        return;
    }
    
    plagiarismState.isChecking = true;
    
    // 更新UI状态
    updateCheckingUI(true);
    
    try {
        // 模拟查重过程
        await simulatePlagiarismCheck();
        
        // 生成模拟结果
        const result = generateMockResult();
        plagiarismState.checkResult = result;
        
        // 显示结果
        displayCheckResult(result);
        
        showToast('查重检测完成', 'success');
        
    } catch (error) {
        showToast('检测失败，请重试', 'error');
    } finally {
        plagiarismState.isChecking = false;
        updateCheckingUI(false);
    }
}

// 模拟查重过程
async function simulatePlagiarismCheck() {
    const steps = [
        { text: '正在分析文档结构...', progress: 10 },
        { text: '正在与知网数据库对比...', progress: 30 },
        { text: '正在与万方数据库对比...', progress: 50 },
        { text: '正在与维普数据库对比...', progress: 70 },
        { text: '正在检测互联网资源...', progress: 85 },
        { text: '正在生成检测报告...', progress: 100 }
    ];
    
    for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800));
        updateProgress(step.text, step.progress);
    }
}

// 更新进度
function updateProgress(text, progress) {
    if (plagiarismElements.progressText) {
        plagiarismElements.progressText.textContent = text;
    }
    if (plagiarismElements.progressFill) {
        plagiarismElements.progressFill.style.width = `${progress}%`;
    }
    if (plagiarismElements.progressDetail) {
        plagiarismElements.progressDetail.textContent = text;
    }
}

// 更新检测中的UI状态
function updateCheckingUI(isChecking) {
    if (plagiarismElements.checkBtn) {
        plagiarismElements.checkBtn.disabled = isChecking;
        plagiarismElements.checkBtn.innerHTML = isChecking 
            ? '<div class="spinner mr-2"></div>AI检测中...'
            : '<i data-lucide="check-circle" class="w-5 h-5 mr-2"></i>开始智能查重';
    }
    
    if (plagiarismElements.progressContainer) {
        if (isChecking) {
            plagiarismElements.progressContainer.classList.remove('hidden');
        } else {
            plagiarismElements.progressContainer.classList.add('hidden');
        }
    }
    
    if (!isChecking) {
        initializeLucideIcons();
    }
}

// 生成模拟结果
function generateMockResult() {
    const similarity = Math.random() * 25; // 0-25% 相似度
    const wordCount = plagiarismState.currentTab === 'text' 
        ? plagiarismState.textContent.length 
        : Math.floor(Math.random() * 10000) + 5000;
    
    return {
        similarity: parseFloat(similarity.toFixed(1)),
        wordCount: wordCount,
        duplicateWords: Math.floor(wordCount * similarity / 100),
        sources: [
            {
                source: "知网数据库",
                similarity: parseFloat((similarity * 0.5).toFixed(1)),
                matches: Math.floor(Math.random() * 15) + 5,
            },
            {
                source: "万方数据库",
                similarity: parseFloat((similarity * 0.3).toFixed(1)),
                matches: Math.floor(Math.random() * 10) + 3,
            },
            {
                source: "维普数据库",
                similarity: parseFloat((similarity * 0.2).toFixed(1)),
                matches: Math.floor(Math.random() * 8) + 2,
            },
        ],
        details: [
            {
                text: "人工智能技术在现代社会中发挥着越来越重要的作用",
                similarity: 85,
                source: "《人工智能发展报告2023》",
            },
            {
                text: "深度学习算法的发展为图像识别技术带来了革命性的变化",
                similarity: 78,
                source: "《计算机视觉与模式识别》期刊",
            },
        ],
    };
}

// 显示检测结果
function displayCheckResult(result) {
    // 显示相似度
    if (plagiarismElements.similarityRate) {
        plagiarismElements.similarityRate.textContent = `${result.similarity}%`;
        plagiarismElements.similarityRate.className = `text-4xl font-bold ${getSimilarityColor(result.similarity)}`;
    }
    
    // 显示状态徽章
    if (plagiarismElements.similarityBadge) {
        const badgeInfo = getSimilarityBadge(result.similarity);
        plagiarismElements.similarityBadge.textContent = badgeInfo.text;
        plagiarismElements.similarityBadge.className = `badge ${badgeInfo.class}`;
    }
    
    // 显示字数统计
    if (plagiarismElements.totalWords) {
        plagiarismElements.totalWords.textContent = result.wordCount.toLocaleString();
    }
    if (plagiarismElements.duplicateWords) {
        plagiarismElements.duplicateWords.textContent = result.duplicateWords.toLocaleString();
    }
    
    // 显示数据库对比结果
    displaySources(result.sources);
    
    // 显示重复内容详情
    displayDetails(result.details);
    
    // 显示结果容器
    if (plagiarismElements.resultContainer) {
        plagiarismElements.resultContainer.classList.remove('hidden');
    }
    if (plagiarismElements.detailsContainer) {
        plagiarismElements.detailsContainer.classList.remove('hidden');
    }
}

// 显示数据库对比结果
function displaySources(sources) {
    if (!plagiarismElements.sourcesList) return;
    
    const html = sources.map(source => `
        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
            <span class="text-sm">${source.source}</span>
            <div class="text-right">
                <div class="text-sm font-semibold">${source.similarity}%</div>
                <div class="text-xs text-gray-500">${source.matches}处重复</div>
            </div>
        </div>
    `).join('');
    
    plagiarismElements.sourcesList.innerHTML = html;
}

// 显示重复内容详情
function displayDetails(details) {
    if (!plagiarismElements.detailsList) return;
    
    const html = details.map(detail => `
        <div class="p-3 border border-red-200 bg-red-50 rounded-lg">
            <p class="text-sm mb-2">${detail.text}</p>
            <div class="flex justify-between items-center text-xs">
                <span class="text-gray-600">来源: ${detail.source}</span>
                <span class="badge badge-danger">${detail.similarity}%</span>
            </div>
        </div>
    `).join('');
    
    plagiarismElements.detailsList.innerHTML = html;
}

// 获取相似度颜色
function getSimilarityColor(similarity) {
    if (similarity < 10) return "text-green-600";
    if (similarity < 20) return "text-yellow-600";
    return "text-red-600";
}

// 获取相似度徽章
function getSimilarityBadge(similarity) {
    if (similarity < 10) return { text: "通过", class: "badge-success" };
    if (similarity < 20) return { text: "警告", class: "badge-warning" };
    return { text: "不通过", class: "badge-danger" };
}

// 显示详细报告
function showDetailedReport() {
    if (!plagiarismState.checkResult) return;
    
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900">详细检测报告</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold ${getSimilarityColor(plagiarismState.checkResult.similarity)}">
                            ${plagiarismState.checkResult.similarity}%
                        </div>
                        <div class="text-sm text-gray-500">总相似度</div>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-gray-900">
                            ${plagiarismState.checkResult.wordCount.toLocaleString()}
                        </div>
                        <div class="text-sm text-gray-500">总字数</div>
                    </div>
                </div>
                
                <h4 class="font-semibold mb-4">重复内容详情</h4>
                <div class="space-y-3">
                    ${plagiarismState.checkResult.details.map(detail => `
                        <div class="p-4 border border-red-200 bg-red-50 rounded-lg">
                            <p class="mb-2">${detail.text}</p>
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">来源: ${detail.source}</span>
                                <span class="badge badge-danger">${detail.similarity}%</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end space-x-4">
                <button onclick="this.closest('.fixed').remove()" class="btn btn-secondary">关闭</button>
                <button onclick="downloadReport()" class="btn btn-primary">下载报告</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// 下载报告
function downloadReport() {
    showToast('报告下载功能开发中...', 'info');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 移动端菜单控制
function toggleMobileMenu() {
    if (plagiarismElements.sidebar) {
        plagiarismElements.sidebar.classList.toggle('-translate-x-full');
    }
}

function closeMobileMenu() {
    if (plagiarismElements.sidebar) {
        plagiarismElements.sidebar.classList.add('-translate-x-full');
    }
}

// 工具函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
