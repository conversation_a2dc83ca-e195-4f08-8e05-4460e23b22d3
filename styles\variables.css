/**
 * CSS变量定义
 * 集中管理所有设计令牌
 */

:root {
    /* 颜色系统 */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;

    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    --color-success-50: #ecfdf5;
    --color-success-500: #10b981;
    --color-success-600: #059669;

    --color-warning-50: #fffbeb;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;

    --color-danger-50: #fef2f2;
    --color-danger-500: #ef4444;
    --color-danger-600: #dc2626;

    --color-info-50: #f0f9ff;
    --color-info-500: #06b6d4;
    --color-info-600: #0891b2;

    /* 渐变系统 */
    --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, #8b5cf6 100%);
    --gradient-primary-hover: linear-gradient(135deg, var(--color-primary-600) 0%, #7c3aed 100%);
    --gradient-success: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
    --gradient-warning: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
    --gradient-danger: linear-gradient(135deg, var(--color-danger-500) 0%, var(--color-danger-600) 100%);
    --gradient-info: linear-gradient(135deg, var(--color-info-500) 0%, var(--color-info-600) 100%);

    /* 背景渐变 */
    --gradient-bg-light: linear-gradient(135deg, #ffffff 0%, var(--color-gray-50) 100%);
    --gradient-bg-blue: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
    --gradient-bg-gray: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* 特殊阴影 */
    --shadow-button: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
    --shadow-button-hover: 0 8px 25px 0 rgba(59, 130, 246, 0.39);
    --shadow-card: var(--shadow-lg);
    --shadow-card-hover: var(--shadow-xl);

    /* 边框半径 */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.25rem;
    --radius-lg: 0.375rem;
    --radius-xl: 0.5rem;
    --radius-2xl: 0.75rem;
    --radius-3xl: 1rem;
    --radius-full: 9999px;

    /* 间距系统 */
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* 字体系统 */
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', monospace;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* 布局系统 */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;

    --header-height: 4rem;
    --sidebar-width: 16rem;
    --footer-height: 4rem;

    /* Z-index系统 */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;

    /* 过渡系统 */
    --transition-duration-75: 75ms;
    --transition-duration-100: 100ms;
    --transition-duration-150: 150ms;
    --transition-duration-200: 200ms;
    --transition-duration-300: 300ms;
    --transition-duration-500: 500ms;
    --transition-duration-700: 700ms;
    --transition-duration-1000: 1000ms;

    --transition-timing-linear: linear;
    --transition-timing-in: cubic-bezier(0.4, 0, 1, 1);
    --transition-timing-out: cubic-bezier(0, 0, 0.2, 1);
    --transition-timing-in-out: cubic-bezier(0.4, 0, 0.2, 1);

    /* 常用过渡 */
    --transition-all: all var(--transition-duration-150) var(--transition-timing-in-out);
    --transition-colors: color var(--transition-duration-150) var(--transition-timing-in-out),
                        background-color var(--transition-duration-150) var(--transition-timing-in-out),
                        border-color var(--transition-duration-150) var(--transition-timing-in-out);
    --transition-opacity: opacity var(--transition-duration-150) var(--transition-timing-in-out);
    --transition-shadow: box-shadow var(--transition-duration-150) var(--transition-timing-in-out);
    --transition-transform: transform var(--transition-duration-150) var(--transition-timing-in-out);

    /* 边框系统 */
    --border-width-0: 0;
    --border-width-1: 1px;
    --border-width-2: 2px;
    --border-width-4: 4px;
    --border-width-8: 8px;

    --border-color-gray: var(--color-gray-200);
    --border-color-primary: var(--color-primary-500);
    --border-color-success: var(--color-success-500);
    --border-color-warning: var(--color-warning-500);
    --border-color-danger: var(--color-danger-500);

    /* 焦点环 */
    --ring-width-0: 0;
    --ring-width-1: 1px;
    --ring-width-2: 2px;
    --ring-width-4: 4px;
    --ring-width-8: 8px;

    --ring-color-primary: rgba(59, 130, 246, 0.5);
    --ring-color-success: rgba(16, 185, 129, 0.5);
    --ring-color-warning: rgba(245, 158, 11, 0.5);
    --ring-color-danger: rgba(239, 68, 68, 0.5);

    /* 不透明度 */
    --opacity-0: 0;
    --opacity-5: 0.05;
    --opacity-10: 0.1;
    --opacity-20: 0.2;
    --opacity-25: 0.25;
    --opacity-30: 0.3;
    --opacity-40: 0.4;
    --opacity-50: 0.5;
    --opacity-60: 0.6;
    --opacity-70: 0.7;
    --opacity-75: 0.75;
    --opacity-80: 0.8;
    --opacity-90: 0.9;
    --opacity-95: 0.95;
    --opacity-100: 1;

    /* 模糊效果 */
    --blur-none: 0;
    --blur-sm: 4px;
    --blur-md: 8px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --blur-3xl: 64px;

    /* 背景模糊 */
    --backdrop-blur-sm: blur(4px);
    --backdrop-blur-md: blur(8px);
    --backdrop-blur-lg: blur(16px);
    --backdrop-blur-xl: blur(24px);
}

/* 暗色模式变量 */
@media (prefers-color-scheme: dark) {
    :root {
        --color-gray-50: #1f2937;
        --color-gray-100: #374151;
        --color-gray-200: #4b5563;
        --color-gray-300: #6b7280;
        --color-gray-400: #9ca3af;
        --color-gray-500: #d1d5db;
        --color-gray-600: #e5e7eb;
        --color-gray-700: #f3f4f6;
        --color-gray-800: #f9fafb;
        --color-gray-900: #ffffff;

        --gradient-bg-light: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        --gradient-bg-gray: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --color-primary-500: #0000ff;
        --color-success-500: #008000;
        --color-warning-500: #ff8c00;
        --color-danger-500: #ff0000;
        
        --border-width-1: 2px;
        --border-width-2: 3px;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-duration-75: 0ms;
        --transition-duration-100: 0ms;
        --transition-duration-150: 0ms;
        --transition-duration-200: 0ms;
        --transition-duration-300: 0ms;
        --transition-duration-500: 0ms;
        --transition-duration-700: 0ms;
        --transition-duration-1000: 0ms;
    }
}
