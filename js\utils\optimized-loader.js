/**
 * 优化的资源加载器
 * 实现关键资源内联、非关键资源延迟加载、预加载等性能优化策略
 */

class OptimizedLoader {
    constructor() {
        this.loadedResources = new Set();
        this.loadingPromises = new Map();
        this.criticalCSS = null;
        this.observer = null;
        this.preloadQueue = [];
        
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.preloadCriticalResources();
        this.setupDeferredLoading();
        this.optimizeExternalResources();
    }

    // 设置Intersection Observer用于懒加载
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadDeferredResource(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: 0.1
            });
        }
    }

    // 预加载关键资源
    async preloadCriticalResources() {
        // 预加载关键CSS
        await this.inlineCriticalCSS();
        
        // 预加载关键JavaScript模块
        this.preloadModule('js/core/app.js');
        this.preloadModule('js/components.js');
        
        // 预加载下一个可能访问的页面
        this.preloadNextPages();
        
        // DNS预解析
        this.setupDNSPrefetch();
    }

    // 内联关键CSS
    async inlineCriticalCSS() {
        if (document.querySelector('style[data-critical]')) {
            return; // 已经内联
        }

        try {
            const response = await fetch('/styles/critical.css');
            const css = await response.text();
            
            const style = document.createElement('style');
            style.setAttribute('data-critical', 'true');
            style.textContent = css;
            
            // 插入到head的最前面
            document.head.insertBefore(style, document.head.firstChild);
            
            this.criticalCSS = css;
            this.loadedResources.add('critical-css');
            
            console.log('✅ Critical CSS inlined');
        } catch (error) {
            console.error('❌ Failed to inline critical CSS:', error);
        }
    }

    // 预加载模块
    preloadModule(src) {
        if (this.loadedResources.has(src)) return;

        const link = document.createElement('link');
        link.rel = 'modulepreload';
        link.href = src;
        document.head.appendChild(link);
        
        this.loadedResources.add(src);
    }

    // 预加载下一个可能访问的页面
    preloadNextPages() {
        const currentPage = this.getCurrentPage();
        const nextPages = this.getPredictedNextPages(currentPage);
        
        nextPages.forEach(page => {
            this.preloadPage(page);
        });
    }

    // 预加载页面
    preloadPage(href) {
        if (this.loadedResources.has(`page-${href}`)) return;

        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;
        document.head.appendChild(link);
        
        this.loadedResources.add(`page-${href}`);
    }

    // DNS预解析
    setupDNSPrefetch() {
        const domains = [
            'cdn.tailwindcss.com',
            'unpkg.com'
        ];

        domains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = `//${domain}`;
            document.head.appendChild(link);
        });
    }

    // 设置延迟加载
    setupDeferredLoading() {
        // 延迟加载非关键CSS
        this.deferNonCriticalCSS();
        
        // 延迟加载非关键JavaScript
        this.deferNonCriticalJS();
        
        // 设置图片懒加载
        this.setupImageLazyLoading();
    }

    // 延迟加载非关键CSS
    deferNonCriticalCSS() {
        const nonCriticalCSS = [
            'styles/components.css',
            'styles/animations.css'
        ];

        // 在页面加载完成后加载
        if (document.readyState === 'complete') {
            this.loadNonCriticalCSS(nonCriticalCSS);
        } else {
            window.addEventListener('load', () => {
                this.loadNonCriticalCSS(nonCriticalCSS);
            });
        }
    }

    // 加载非关键CSS
    loadNonCriticalCSS(cssFiles) {
        cssFiles.forEach((href, index) => {
            setTimeout(() => {
                this.loadCSS(href, 'low');
            }, index * 100); // 错开加载时间
        });
    }

    // 加载CSS文件
    loadCSS(href, priority = 'normal') {
        if (this.loadedResources.has(href)) return Promise.resolve();

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = priority === 'low' ? 'print' : 'all';
            
            link.onload = () => {
                if (link.media === 'print') {
                    link.media = 'all';
                }
                this.loadedResources.add(href);
                resolve();
            };

            link.onerror = () => {
                reject(new Error(`Failed to load CSS: ${href}`));
            };

            document.head.appendChild(link);
        });
    }

    // 延迟加载非关键JavaScript
    deferNonCriticalJS() {
        const nonCriticalJS = [
            'js/utils/analytics.js',
            'js/utils/performance-monitor.js'
        ];

        // 使用requestIdleCallback延迟加载
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.loadNonCriticalJS(nonCriticalJS);
            });
        } else {
            setTimeout(() => {
                this.loadNonCriticalJS(nonCriticalJS);
            }, 2000);
        }
    }

    // 加载非关键JavaScript
    loadNonCriticalJS(jsFiles) {
        jsFiles.forEach((src, index) => {
            setTimeout(() => {
                this.loadScript(src);
            }, index * 200);
        });
    }

    // 加载JavaScript文件
    loadScript(src) {
        if (this.loadedResources.has(src)) return Promise.resolve();

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => {
                this.loadedResources.add(src);
                resolve();
            };

            script.onerror = () => {
                reject(new Error(`Failed to load script: ${src}`));
            };

            document.head.appendChild(script);
        });
    }

    // 设置图片懒加载
    setupImageLazyLoading() {
        // 使用原生懒加载（如果支持）
        if ('loading' in HTMLImageElement.prototype) {
            const images = document.querySelectorAll('img[data-src]');
            images.forEach(img => {
                img.loading = 'lazy';
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        } else {
            // 使用Intersection Observer
            const images = document.querySelectorAll('img[data-src]');
            images.forEach(img => {
                if (this.observer) {
                    this.observer.observe(img);
                }
            });
        }
    }

    // 加载延迟资源
    loadDeferredResource(element) {
        if (element.tagName === 'IMG' && element.dataset.src) {
            element.src = element.dataset.src;
            element.removeAttribute('data-src');
        }
    }

    // 优化外部资源
    optimizeExternalResources() {
        // 优化Tailwind CSS加载
        this.optimizeTailwindCSS();
        
        // 优化图标库加载
        this.optimizeIconLibrary();
    }

    // 优化Tailwind CSS加载
    optimizeTailwindCSS() {
        const tailwindScript = document.querySelector('script[src*="tailwindcss"]');
        if (tailwindScript) {
            // 添加预连接
            const preconnect = document.createElement('link');
            preconnect.rel = 'preconnect';
            preconnect.href = 'https://cdn.tailwindcss.com';
            document.head.appendChild(preconnect);
        }
    }

    // 优化图标库加载
    optimizeIconLibrary() {
        const iconScript = document.querySelector('script[src*="lucide"]');
        if (iconScript) {
            // 延迟加载图标库
            iconScript.defer = true;
        }
    }

    // 获取当前页面
    getCurrentPage() {
        const path = window.location.pathname;
        return path.split('/').pop().replace('.html', '') || 'index';
    }

    // 预测下一个可能访问的页面
    getPredictedNextPages(currentPage) {
        const pageFlow = {
            'index': ['history', 'plagiarism'],
            'history': ['index', 'plagiarism'],
            'plagiarism': ['aigc', 'history'],
            'aigc': ['plagiarism', 'history'],
            'billing': ['profile'],
            'profile': ['billing']
        };

        return pageFlow[currentPage] || [];
    }

    // 获取加载状态
    getLoadingStatus() {
        return {
            loaded: Array.from(this.loadedResources),
            loading: Array.from(this.loadingPromises.keys()),
            total: this.loadedResources.size + this.loadingPromises.size
        };
    }
}

// 导出单例
export const optimizedLoader = new OptimizedLoader();
export default OptimizedLoader;
