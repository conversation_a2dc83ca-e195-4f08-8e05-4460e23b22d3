<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件测试页面</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <h1 class="text-3xl font-bold text-gray-900 mb-6">组件测试页面</h1>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">测试结果</h2>
                    <div id="test-results">
                        <p class="text-gray-600">正在加载组件...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/components.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            
            // 测试组件加载
            if (typeof UIComponents !== 'undefined') {
                UIComponents.initializePage('test');
                
                results.innerHTML = `
                    <div class="space-y-2">
                        <p class="text-green-600">✅ UIComponents 已加载</p>
                        <p class="text-green-600">✅ 头部组件已初始化</p>
                        <p class="text-green-600">✅ 侧边栏组件已初始化</p>
                        <p class="text-green-600">✅ 移动端导航已初始化</p>
                    </div>
                `;
                
                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            } else {
                results.innerHTML = `
                    <p class="text-red-600">❌ UIComponents 未加载</p>
                `;
            }
        });
    </script>
</body>
</html>
