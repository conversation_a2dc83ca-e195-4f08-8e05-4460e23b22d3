<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博晓文 - 高质量原创AI论文写作平台（优化版测试）</title>
    <meta name="description" content="高质量原创AI论文写作平台，一键万字生成，无限改稿！">
    
    <!-- 关键CSS内联 -->
    <style>
        :root {
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --gray-50: #f9fafb;
            --gray-700: #374151;
        }
        
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-700);
            line-height: 1.6;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gray-50);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid var(--primary-500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .features {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }
        
        .feature-tag {
            padding: 0.5rem 1rem;
            background: #dbeafe;
            color: #1e40af;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .essay-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 3rem;
        }
        
        .essay-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .essay-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-500);
        }
        
        .essay-card.active {
            border-color: var(--primary-500);
            background: #dbeafe;
        }
        
        .essay-icon {
            width: 3rem;
            height: 3rem;
            background: var(--primary-500);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .essay-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 0.5rem;
        }
        
        .essay-desc {
            font-size: 0.875rem;
            color: #6b7280;
            text-align: center;
        }
        
        .form-container {
            background: white;
            border-radius: 0.75rem;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .form-container.show {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 1rem;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn {
            background: var(--primary-500);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: var(--primary-600);
        }
        
        .performance-info {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-size: 0.875rem;
            max-width: 300px;
        }
    </style>
    
    <!-- Tailwind CSS - 异步加载 -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p style="margin-top: 1rem; color: #6b7280;">正在加载应用...</p>
        </div>
    </div>

    <!-- 应用容器 -->
    <div id="app" style="display: none;">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1 class="title">智能AI论文写作平台</h1>
                <p class="subtitle">选择您需要的论文类型，AI将为您智能生成专业、原创的学术内容 ✨</p>
                
                <div class="features">
                    <span class="feature-tag">🚀 3秒生成大纲</span>
                    <span class="feature-tag">✅ 100%原创保证</span>
                    <span class="feature-tag">🔄 无限次修改</span>
                </div>
            </div>

            <!-- 论文类型选择 -->
            <div class="essay-types" id="essay-types">
                <!-- 论文类型将通过JavaScript生成 -->
            </div>

            <!-- 表单容器 -->
            <div class="form-container" id="form-container">
                <h3 style="margin-bottom: 1.5rem; font-size: 1.25rem; font-weight: bold;">填写论文要求</h3>
                
                <div class="form-group">
                    <label class="form-label">论文标题</label>
                    <input type="text" class="form-input" placeholder="请输入论文标题">
                </div>
                
                <div class="form-group">
                    <label class="form-label">字数要求</label>
                    <select class="form-input">
                        <option>8000-10000字</option>
                        <option>10000-15000字</option>
                        <option>15000-20000字</option>
                        <option>20000字以上</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">学科领域</label>
                    <select class="form-input">
                        <option>计算机科学</option>
                        <option>工程技术</option>
                        <option>商业管理</option>
                        <option>教育学</option>
                        <option>其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">内容描述</label>
                    <textarea class="form-input" rows="4" placeholder="请详细描述论文的主要内容和要求"></textarea>
                </div>
                
                <button class="btn" onclick="generateEssay()">🚀 一键生成论文</button>
            </div>
        </div>
    </div>

    <!-- 性能信息 -->
    <div id="performance-info" class="performance-info" style="display: none;">
        <h4 style="font-weight: bold; margin-bottom: 0.5rem;">性能信息</h4>
        <div id="performance-metrics"></div>
    </div>

    <script>
        // 论文类型数据
        const essayTypes = [
            { id: 'graduation', name: '毕业论文', icon: '🎓', desc: '本科、硕士、博士毕业论文写作' },
            { id: 'literature', name: '文献综述', icon: '📚', desc: '系统性文献回顾与分析' },
            { id: 'proposal', name: '开题报告', icon: '📋', desc: '研究提案、项目申请、学术规划' },
            { id: 'journal', name: '期刊论文', icon: '📰', desc: '学术期刊投稿论文写作' },
            { id: 'term', name: '期末论文', icon: '✏️', desc: '期末课程论文、学期论文' },
            { id: 'course', name: '课程论文', icon: '📝', desc: '课程作业论文、专业课论文' }
        ];

        let selectedType = null;
        let startTime = performance.now();

        // 渲染论文类型
        function renderEssayTypes() {
            const container = document.getElementById('essay-types');
            container.innerHTML = essayTypes.map(type => `
                <div class="essay-card" onclick="selectEssayType('${type.id}')">
                    <div class="essay-icon">${type.icon}</div>
                    <div class="essay-title">${type.name}</div>
                    <div class="essay-desc">${type.desc}</div>
                </div>
            `).join('');
        }

        // 选择论文类型
        function selectEssayType(typeId) {
            selectedType = typeId;
            
            // 更新UI
            document.querySelectorAll('.essay-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.essay-card').classList.add('active');
            
            // 显示表单
            document.getElementById('form-container').classList.add('show');
            
            // 滚动到表单
            document.getElementById('form-container').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        // 生成论文
        function generateEssay() {
            if (!selectedType) {
                alert('请先选择论文类型');
                return;
            }
            
            alert('AI正在为您生成论文，请稍候...');
        }

        // 记录性能指标
        function recordPerformance() {
            const now = performance.now();
            const loadTime = now - startTime;
            
            const metrics = {
                '页面加载时间': `${loadTime.toFixed(2)}ms`,
                '内存使用': performance.memory ? `${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB` : '不支持',
                '连接类型': navigator.connection ? navigator.connection.effectiveType : '未知'
            };
            
            const metricsHtml = Object.entries(metrics)
                .map(([key, value]) => `<div>${key}: ${value}</div>`)
                .join('');
            
            document.getElementById('performance-metrics').innerHTML = metricsHtml;
            document.getElementById('performance-info').style.display = 'block';
        }

        // 初始化应用
        function initApp() {
            try {
                // 隐藏加载屏幕
                document.getElementById('loading-screen').style.display = 'none';
                
                // 显示应用
                const app = document.getElementById('app');
                app.style.display = 'block';
                app.classList.add('fade-in');
                
                // 渲染论文类型
                renderEssayTypes();
                
                // 记录性能
                setTimeout(recordPerformance, 1000);
                
                console.log('✅ 应用初始化成功');
                
            } catch (error) {
                console.error('❌ 应用初始化失败:', error);
                alert('应用加载失败，请刷新页面重试');
            }
        }

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initApp);
        } else {
            initApp();
        }
    </script>
</body>
</html>
