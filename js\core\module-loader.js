/**
 * 模块加载器
 * 实现动态模块加载、依赖管理和代码分割
 */

export class ModuleLoader {
    constructor() {
        this.modules = new Map();
        this.loadingPromises = new Map();
        this.dependencies = new Map();
        this.loadOrder = [];
        this.cache = new Map();
        this.config = {
            baseUrl: '/js/',
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000
        };
    }

    // 注册模块
    register(name, factory, dependencies = []) {
        this.modules.set(name, {
            factory,
            dependencies,
            instance: null,
            loaded: false,
            loading: false
        });

        this.dependencies.set(name, dependencies);
    }

    // 定义模块
    define(name, dependencies, factory) {
        if (typeof dependencies === 'function') {
            factory = dependencies;
            dependencies = [];
        }

        this.register(name, factory, dependencies);
    }

    // 加载模块
    async load(name) {
        if (this.loadingPromises.has(name)) {
            return this.loadingPromises.get(name);
        }

        const promise = this.loadModule(name);
        this.loadingPromises.set(name, promise);

        try {
            const module = await promise;
            this.loadingPromises.delete(name);
            return module;
        } catch (error) {
            this.loadingPromises.delete(name);
            throw error;
        }
    }

    // 加载模块的具体实现
    async loadModule(name) {
        const moduleInfo = this.modules.get(name);
        
        if (!moduleInfo) {
            // 尝试动态导入
            return this.dynamicImport(name);
        }

        if (moduleInfo.loaded) {
            return moduleInfo.instance;
        }

        if (moduleInfo.loading) {
            // 等待加载完成
            while (moduleInfo.loading) {
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            return moduleInfo.instance;
        }

        moduleInfo.loading = true;

        try {
            // 加载依赖
            const dependencies = await this.loadDependencies(moduleInfo.dependencies);
            
            // 执行模块工厂函数
            const instance = await this.executeFactory(moduleInfo.factory, dependencies);
            
            moduleInfo.instance = instance;
            moduleInfo.loaded = true;
            moduleInfo.loading = false;

            this.loadOrder.push(name);
            this.emit('moduleLoaded', { name, instance });

            return instance;
        } catch (error) {
            moduleInfo.loading = false;
            this.emit('moduleError', { name, error });
            throw error;
        }
    }

    // 动态导入模块
    async dynamicImport(name) {
        const cached = this.cache.get(name);
        if (cached) {
            return cached;
        }

        try {
            const modulePath = this.resolveModulePath(name);
            const module = await this.importWithRetry(modulePath);
            
            this.cache.set(name, module);
            this.emit('moduleLoaded', { name, instance: module });
            
            return module;
        } catch (error) {
            this.emit('moduleError', { name, error });
            throw error;
        }
    }

    // 带重试的导入
    async importWithRetry(path, attempt = 1) {
        try {
            return await import(path);
        } catch (error) {
            if (attempt < this.config.retryAttempts) {
                await new Promise(resolve => 
                    setTimeout(resolve, this.config.retryDelay * attempt)
                );
                return this.importWithRetry(path, attempt + 1);
            }
            throw error;
        }
    }

    // 解析模块路径
    resolveModulePath(name) {
        const moduleMap = {
            // 核心模块
            'config': 'config/app-config.js',
            'ui-config': 'config/ui-config.js',
            'performance-config': 'config/performance-config.js',
            
            // 工具模块
            'resource-loader': 'utils/resource-loader.js',
            'css-loader': 'utils/css-loader.js',
            'event-bus': 'utils/event-bus.js',
            'storage': 'utils/storage.js',
            'validator': 'utils/validator.js',
            
            // 组件模块
            'base-component': 'components/base-component.js',
            'button': 'components/button.js',
            'modal': 'components/modal.js',
            'form': 'components/form.js',
            'table': 'components/table.js',
            
            // 页面模块
            'essay-forms': 'essay-forms.js',
            'history': 'history.js',
            'plagiarism': 'plagiarism.js',
            'billing': 'billing.js',
            'profile': 'profile.js',
            'aigc': 'aigc.js',
            
            // 服务模块
            'auth': 'auth.js',
            'api': 'services/api.js',
            'analytics': 'services/analytics.js'
        };

        const path = moduleMap[name] || `${name}.js`;
        return this.config.baseUrl + path;
    }

    // 加载依赖
    async loadDependencies(dependencies) {
        if (!dependencies || dependencies.length === 0) {
            return [];
        }

        const loadPromises = dependencies.map(dep => this.load(dep));
        return Promise.all(loadPromises);
    }

    // 执行工厂函数
    async executeFactory(factory, dependencies) {
        if (typeof factory === 'function') {
            return factory(...dependencies);
        }
        return factory;
    }

    // 批量加载模块
    async loadBatch(names) {
        const loadPromises = names.map(name => this.load(name));
        return Promise.all(loadPromises);
    }

    // 预加载模块
    async preload(names) {
        const preloadPromises = names.map(name => {
            const path = this.resolveModulePath(name);
            const link = document.createElement('link');
            link.rel = 'modulepreload';
            link.href = path;
            document.head.appendChild(link);
            
            return new Promise((resolve, reject) => {
                link.onload = resolve;
                link.onerror = reject;
            });
        });

        return Promise.all(preloadPromises);
    }

    // 卸载模块
    unload(name) {
        const moduleInfo = this.modules.get(name);
        if (moduleInfo && moduleInfo.loaded) {
            // 调用模块的清理方法
            if (moduleInfo.instance && typeof moduleInfo.instance.destroy === 'function') {
                moduleInfo.instance.destroy();
            }

            moduleInfo.instance = null;
            moduleInfo.loaded = false;
            
            this.cache.delete(name);
            
            const index = this.loadOrder.indexOf(name);
            if (index > -1) {
                this.loadOrder.splice(index, 1);
            }

            this.emit('moduleUnloaded', { name });
        }
    }

    // 重新加载模块
    async reload(name) {
        this.unload(name);
        return this.load(name);
    }

    // 获取模块实例
    get(name) {
        const moduleInfo = this.modules.get(name);
        return moduleInfo?.instance || this.cache.get(name);
    }

    // 检查模块是否已加载
    isLoaded(name) {
        const moduleInfo = this.modules.get(name);
        return moduleInfo?.loaded || this.cache.has(name);
    }

    // 检查模块是否正在加载
    isLoading(name) {
        return this.loadingPromises.has(name) || 
               this.modules.get(name)?.loading;
    }

    // 获取加载顺序
    getLoadOrder() {
        return [...this.loadOrder];
    }

    // 获取依赖图
    getDependencyGraph() {
        const graph = {};
        for (const [name, deps] of this.dependencies) {
            graph[name] = deps;
        }
        return graph;
    }

    // 检查循环依赖
    checkCircularDependencies() {
        const visited = new Set();
        const recursionStack = new Set();
        const cycles = [];

        const dfs = (node, path = []) => {
            if (recursionStack.has(node)) {
                const cycleStart = path.indexOf(node);
                cycles.push(path.slice(cycleStart).concat(node));
                return;
            }

            if (visited.has(node)) {
                return;
            }

            visited.add(node);
            recursionStack.add(node);

            const dependencies = this.dependencies.get(node) || [];
            for (const dep of dependencies) {
                dfs(dep, path.concat(node));
            }

            recursionStack.delete(node);
        };

        for (const module of this.modules.keys()) {
            if (!visited.has(module)) {
                dfs(module);
            }
        }

        return cycles;
    }

    // 获取模块统计信息
    getStats() {
        const total = this.modules.size + this.cache.size;
        const loaded = Array.from(this.modules.values()).filter(m => m.loaded).length + this.cache.size;
        const loading = Array.from(this.modules.values()).filter(m => m.loading).length;

        return {
            total,
            loaded,
            loading,
            loadOrder: this.getLoadOrder(),
            dependencies: this.getDependencyGraph(),
            circularDependencies: this.checkCircularDependencies()
        };
    }

    // 清理所有模块
    clear() {
        // 卸载所有模块
        for (const name of this.modules.keys()) {
            this.unload(name);
        }

        this.modules.clear();
        this.loadingPromises.clear();
        this.dependencies.clear();
        this.loadOrder.length = 0;
        this.cache.clear();
    }

    // 事件发射器
    emit(event, data) {
        const customEvent = new CustomEvent(`moduleLoader:${event}`, {
            detail: data
        });
        window.dispatchEvent(customEvent);
    }

    // 监听事件
    on(event, handler) {
        window.addEventListener(`moduleLoader:${event}`, handler);
        return () => window.removeEventListener(`moduleLoader:${event}`, handler);
    }
}

// 全局模块加载器实例
export const moduleLoader = new ModuleLoader();

// 便捷方法
export const define = (name, dependencies, factory) => 
    moduleLoader.define(name, dependencies, factory);

export const require = (name) => moduleLoader.load(name);

export const requireBatch = (names) => moduleLoader.loadBatch(names);

// 初始化核心模块
export async function initializeCore() {
    try {
        // 预加载核心模块
        await moduleLoader.preload(['config', 'ui-config', 'performance-config']);
        
        // 加载核心模块
        await moduleLoader.loadBatch([
            'config',
            'ui-config', 
            'performance-config',
            'resource-loader',
            'css-loader'
        ]);

        console.log('Core modules initialized successfully');
        return true;
    } catch (error) {
        console.error('Failed to initialize core modules:', error);
        return false;
    }
}
