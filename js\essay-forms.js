// 表单处理相关功能

// 表单验证规则 - 参照原项目设计
const VALIDATION_RULES = {
    title: {
        required: true,
        minLength: 5,
        maxLength: 100,
        message: '标题长度应在5-100字符之间'
    },
    subject: {
        required: true,
        message: '请选择学科领域'
    },
    wordCount: {
        required: true,
        message: '请选择字数要求'
    },
    requirements: {
        required: true,
        minLength: 20,
        maxLength: 5000,
        message: '具体要求应在20-5000字符之间'
    },
    major: {
        required: false,
        maxLength: 50,
        message: '专业名称不能超过50字符'
    },
    outline: {
        required: false,
        maxLength: 2000,
        message: '大纲内容不能超过2000字符'
    },
    references: {
        required: false,
        maxLength: 1000,
        message: '参考文献要求不能超过1000字符'
    }
};

// 学科选项
const SUBJECTS = {
    computer: '计算机科学',
    engineering: '工程技术',
    business: '商业管理',
    education: '教育学',
    medicine: '医学',
    law: '法学',
    literature: '文学',
    economics: '经济学',
    psychology: '心理学',
    sociology: '社会学',
    physics: '物理学',
    chemistry: '化学',
    biology: '生物学',
    mathematics: '数学',
    other: '其他'
};

// 字数选项 - 参照原项目毕业论文要求
const WORD_COUNTS = {
    '8000-10000': '8000-10000字',
    '10000-15000': '10000-15000字',
    '15000-20000': '15000-20000字',
    '20000+': '20000字以上',
    '3000-5000': '3000-5000字（短篇）',
    '5000-8000': '5000-8000字（中篇）'
};

// 表单字段配置 - 完全参照原项目
const FORM_FIELDS = {
    graduation: {
        title: '毕业论文写作',
        description: '填写毕业论文要求，AI将为您生成专业的学术内容',
        fields: [
            'title', 'wordCount', 'subject', 'academicLevel', 'major', 'researchDirection', 'content'
        ],
        specificFields: {
            academicLevel: {
                type: 'select',
                label: '学历层次',
                options: {
                    undergraduate: '本科',
                    master: '硕士',
                    phd: '博士'
                },
                required: true
            },
            major: {
                type: 'text',
                label: '专业名称',
                placeholder: '如：计算机科学与技术、工商管理等',
                required: true
            },
            researchDirection: {
                type: 'text',
                label: '研究方向',
                placeholder: '如：深度学习在图像识别中的应用',
                required: false
            }
        }
    },
    literature: {
        title: '文献综述写作',
        description: '填写文献综述要求，AI将为您生成系统性的文献回顾',
        fields: [
            'title', 'wordCount', 'subject', 'reviewTopic', 'content', 'timeRange', 'literatureCount'
        ],
        specificFields: {
            reviewTopic: {
                type: 'text',
                label: '综述主题',
                placeholder: '请输入文献综述的主题',
                required: true
            },
            timeRange: {
                type: 'select',
                label: '时间范围',
                options: {
                    recent5: '近5年',
                    recent10: '近10年',
                    recent15: '近15年',
                    all: '不限'
                },
                required: false
            },
            literatureCount: {
                type: 'select',
                label: '文献数量',
                options: {
                    '30-50': '30-50篇',
                    '50-80': '50-80篇',
                    '80-100': '80-100篇',
                    '100+': '100篇以上'
                },
                required: false
            }
        }
    },
    proposal: {
        title: '开题报告写作',
        description: '填写开题报告要求，AI将为您生成规范的研究提案',
        fields: [
            'title', 'wordCount', 'subject', 'researchQuestion', 'content', 'methodology', 'timeline'
        ],
        specificFields: {
            researchQuestion: {
                type: 'text',
                label: '研究问题',
                placeholder: '请明确描述您的研究问题',
                required: true
            },
            methodology: {
                type: 'select',
                label: '研究方法',
                options: {
                    quantitative: '定量研究',
                    qualitative: '定性研究',
                    mixed: '混合方法',
                    theoretical: '理论研究',
                    empirical: '实证研究'
                },
                required: false
            },
            timeline: {
                type: 'select',
                label: '研究周期',
                options: {
                    '3months': '3个月',
                    '6months': '6个月',
                    '1year': '1年',
                    '2years': '2年',
                    'other': '其他'
                },
                required: false
            }
        }
    },
    task: {
        title: '任务书写作',
        description: '填写任务书要求，AI将为您生成详细的任务规划',
        fields: [
            'title', 'wordCount', 'subject', 'taskType', 'content', 'objectives', 'deliverables'
        ],
        specificFields: {
            taskType: {
                type: 'select',
                label: '任务类型',
                options: {
                    graduation: '毕业设计任务书',
                    course: '课程设计任务书',
                    project: '项目任务书',
                    other: '其他'
                },
                required: true
            },
            objectives: {
                type: 'textarea',
                label: '任务目标',
                placeholder: '请描述任务的具体目标和要求',
                required: false
            },
            deliverables: {
                type: 'textarea',
                label: '交付成果',
                placeholder: '请描述预期的交付成果',
                required: false
            }
        }
    },
    journal: {
        title: '期刊论文写作',
        description: '填写期刊论文要求，AI将为您生成高质量的学术投稿',
        fields: [
            'title', 'wordCount', 'subject', 'journalType', 'content', 'targetJournal', 'impactFactor'
        ],
        specificFields: {
            journalType: {
                type: 'select',
                label: '期刊类型',
                options: {
                    sci: 'SCI期刊',
                    ei: 'EI期刊',
                    cssci: 'CSSCI期刊',
                    core: '中文核心期刊',
                    other: '其他期刊'
                },
                required: false
            },
            targetJournal: {
                type: 'text',
                label: '目标期刊',
                placeholder: '请输入目标投稿期刊名称',
                required: false
            },
            impactFactor: {
                type: 'select',
                label: '影响因子要求',
                options: {
                    'low': '1-3',
                    'medium': '3-5',
                    'high': '5以上',
                    'none': '无要求'
                },
                required: false
            }
        }
    },
    bachelor: {
        title: '专升本论文写作',
        description: '填写专升本论文要求，AI将为您生成符合要求的学术内容',
        fields: [
            'title', 'wordCount', 'subject', 'major', 'content', 'originalEducation', 'targetEducation'
        ],
        specificFields: {
            major: {
                type: 'text',
                label: '专业名称',
                placeholder: '如：工商管理、计算机科学等',
                required: true
            },
            originalEducation: {
                type: 'select',
                label: '原学历',
                options: {
                    college: '专科',
                    adult: '成人教育',
                    self: '自考',
                    other: '其他'
                },
                required: false
            },
            targetEducation: {
                type: 'select',
                label: '目标学历',
                options: {
                    bachelor: '本科',
                    other: '其他'
                },
                required: false
            }
        }
    },
    term: {
        title: '期末论文写作',
        description: '填写期末论文要求，AI将为您生成高质量的课程论文',
        fields: [
            'title', 'wordCount', 'subject', 'courseName', 'content', 'professor', 'semester'
        ],
        specificFields: {
            courseName: {
                type: 'text',
                label: '课程名称',
                placeholder: '请输入课程名称',
                required: true
            },
            professor: {
                type: 'text',
                label: '任课教师',
                placeholder: '请输入任课教师姓名',
                required: false
            },
            semester: {
                type: 'select',
                label: '学期',
                options: {
                    'spring': '春季学期',
                    'summer': '夏季学期',
                    'fall': '秋季学期',
                    'winter': '冬季学期'
                },
                required: false
            }
        }
    },
    course: {
        title: '课程论文写作',
        description: '填写课程论文要求，AI将为您生成专业的学术作业',
        fields: [
            'title', 'wordCount', 'subject', 'courseName', 'content', 'assignmentType', 'dueDate'
        ],
        specificFields: {
            courseName: {
                type: 'text',
                label: '课程名称',
                placeholder: '请输入课程名称',
                required: true
            },
            assignmentType: {
                type: 'select',
                label: '作业类型',
                options: {
                    'essay': '论文作业',
                    'report': '报告作业',
                    'analysis': '分析作业',
                    'review': '综述作业',
                    'other': '其他'
                },
                required: false
            },
            dueDate: {
                type: 'date',
                label: '截止日期',
                placeholder: '请选择截止日期',
                required: false
            }
        }
    },
    practice: {
        title: '实习报告写作',
        description: '填写实习报告要求，AI将为您生成专业的实习总结',
        fields: [
            'title', 'wordCount', 'subject', 'company', 'position', 'content', 'duration', 'industry'
        ],
        specificFields: {
            company: {
                type: 'text',
                label: '实习单位',
                placeholder: '请输入实习单位名称',
                required: true
            },
            position: {
                type: 'text',
                label: '实习岗位',
                placeholder: '请输入实习岗位',
                required: true
            },
            duration: {
                type: 'select',
                label: '实习时长',
                options: {
                    '1month': '1个月',
                    '2months': '2个月',
                    '3months': '3个月',
                    '6months': '6个月',
                    'other': '其他'
                },
                required: false
            },
            industry: {
                type: 'select',
                label: '行业类型',
                options: {
                    tech: '科技互联网',
                    finance: '金融',
                    manufacturing: '制造业',
                    education: '教育',
                    healthcare: '医疗',
                    retail: '零售',
                    consulting: '咨询',
                    other: '其他'
                },
                required: false
            }
        }
    },
    business: {
        title: '创业策划写作',
        description: '填写创业策划要求，AI将为您生成专业的商业计划',
        fields: [
            'title', 'wordCount', 'subject', 'businessType', 'content', 'targetMarket', 'fundingAmount'
        ],
        specificFields: {
            businessType: {
                type: 'select',
                label: '创业类型',
                options: {
                    startup: '初创企业',
                    innovation: '创新项目',
                    social: '社会企业',
                    tech: '科技创业',
                    other: '其他'
                },
                required: false
            },
            targetMarket: {
                type: 'text',
                label: '目标市场',
                placeholder: '请描述目标市场和客户群体',
                required: false
            },
            fundingAmount: {
                type: 'select',
                label: '资金需求',
                options: {
                    '10万以下': '10万以下',
                    '10-50万': '10-50万',
                    '50-100万': '50-100万',
                    '100-500万': '100-500万',
                    '500万以上': '500万以上'
                },
                required: false
            }
        }
    },
    professional: {
        title: '职业规划写作',
        description: '填写职业规划要求，AI将为您生成专业的发展规划',
        fields: [
            'title', 'wordCount', 'subject', 'careerGoal', 'content', 'timeFrame', 'industry'
        ],
        specificFields: {
            careerGoal: {
                type: 'text',
                label: '职业目标',
                placeholder: '请描述您的职业发展目标',
                required: true
            },
            timeFrame: {
                type: 'select',
                label: '规划时间',
                options: {
                    '1year': '1年规划',
                    '3years': '3年规划',
                    '5years': '5年规划',
                    '10years': '10年规划'
                },
                required: false
            },
            industry: {
                type: 'select',
                label: '目标行业',
                options: {
                    tech: '科技互联网',
                    finance: '金融',
                    education: '教育',
                    healthcare: '医疗',
                    consulting: '咨询',
                    government: '政府机关',
                    other: '其他'
                },
                required: false
            }
        }
    },
    research: {
        title: '研究论文写作',
        description: '填写研究论文要求，AI将为您生成高质量的学术研究',
        fields: [
            'title', 'wordCount', 'subject', 'researchMethod', 'content', 'researchType', 'dataSource'
        ],
        specificFields: {
            researchMethod: {
                type: 'select',
                label: '研究方法',
                options: {
                    quantitative: '定量研究',
                    qualitative: '定性研究',
                    mixed: '混合方法',
                    theoretical: '理论研究',
                    empirical: '实证研究'
                },
                required: false
            },
            researchType: {
                type: 'select',
                label: '研究类型',
                options: {
                    basic: '基础研究',
                    applied: '应用研究',
                    development: '开发研究',
                    other: '其他'
                },
                required: false
            },
            dataSource: {
                type: 'select',
                label: '数据来源',
                options: {
                    primary: '一手数据',
                    secondary: '二手数据',
                    mixed: '混合数据',
                    simulation: '仿真数据'
                },
                required: false
            }
        }
    }
};

// 生成表单HTML的增强版本
function generateFormHTML(typeId) {
    console.log('generateFormHTML被调用，类型:', typeId);
    const config = FORM_FIELDS[typeId] || FORM_FIELDS.graduation;
    console.log('使用配置:', config);
    
    let html = '<form id="essay-form" class="space-y-6">';
    
    // 基础字段
    html += generateBasicFields();
    
    // 特定字段
    if (config.specificFields) {
        html += generateSpecificFields(config.specificFields);
    }
    
    // 操作按钮
    html += generateActionButtons();
    
    html += '</form>';
    
    console.log('生成的表单HTML:', html.substring(0, 200) + '...');
    return html;
}

// 确保函数在全局作用域可用
window.generateFormHTML = generateFormHTML;
window.bindFormEvents = bindFormEvents;
window.validateForm = validateForm;

// 生成基础字段 - 参照原项目设计
function generateBasicFields() {
    return `
        <div class="space-y-6">
            <!-- 第一行：标题和学科 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div class="form-group fade-in-up" style="animation-delay: 0.1s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="edit-3" class="w-4 h-4 text-blue-600"></i>
                        论文标题 *
                    </label>
                    <input type="text" name="title" class="form-input" placeholder="请输入论文标题..." required>
                    <div class="text-xs text-red-500 mt-1 hidden" id="title-error"></div>
                </div>
                
                <div class="form-group fade-in-up" style="animation-delay: 0.2s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="book-open" class="w-4 h-4 text-purple-600"></i>
                        学科领域 *
                    </label>
                    <select name="subject" class="form-input form-select" required>
                        <option value="">请选择学科领域</option>
                        ${Object.entries(SUBJECTS).map(([value, label]) =>
                            `<option value="${value}">${label}</option>`
                        ).join('')}
                    </select>
                    <div class="text-xs text-red-500 mt-1 hidden" id="subject-error"></div>
                </div>
            </div>

            <!-- 第二行：专业和字数 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div class="form-group fade-in-up" style="animation-delay: 0.3s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="graduation-cap" class="w-4 h-4 text-green-600"></i>
                        专业名称
                    </label>
                    <input type="text" name="major" class="form-input" placeholder="如：计算机科学与技术、工商管理等">
                    <div class="text-xs text-red-500 mt-1 hidden" id="major-error"></div>
                </div>

                <div class="form-group fade-in-up" style="animation-delay: 0.4s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="hash" class="w-4 h-4 text-orange-600"></i>
                        字数要求 *
                    </label>
                    <select name="wordCount" class="form-input form-select" required>
                        <option value="">请选择字数范围</option>
                        ${Object.entries(WORD_COUNTS).map(([value, label]) =>
                            `<option value="${value}">${label}</option>`
                        ).join('')}
                    </select>
                    <div class="text-xs text-red-500 mt-1 hidden" id="wordCount-error"></div>
                </div>
            </div>

            <!-- 第三行：大纲和参考文献 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div class="form-group fade-in-up" style="animation-delay: 0.5s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="list" class="w-4 h-4 text-cyan-600"></i>
                        论文大纲
                    </label>
                    <textarea name="outline" class="form-input form-textarea" rows="3" placeholder="请输入论文大纲或章节结构（可选）"></textarea>
                    <div class="text-xs text-gray-500 mt-1">如有具体大纲要求，请在此输入</div>
                </div>

                <div class="form-group fade-in-up" style="animation-delay: 0.6s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="bookmark" class="w-4 h-4 text-pink-600"></i>
                        参考文献要求
                    </label>
                    <textarea name="references" class="form-input form-textarea" rows="3" placeholder="请输入参考文献要求（可选）"></textarea>
                    <div class="text-xs text-gray-500 mt-1">如：需要SCI期刊、中文核心期刊等</div>
                </div>
            </div>

            <!-- 第四行：格式和截止时间 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div class="form-group fade-in-up" style="animation-delay: 0.7s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="file-type" class="w-4 h-4 text-indigo-600"></i>
                        格式要求
                    </label>
                    <select name="format" class="form-input form-select">
                        <option value="">请选择格式要求</option>
                        <option value="apa">APA格式</option>
                        <option value="mla">MLA格式</option>
                        <option value="chicago">Chicago格式</option>
                        <option value="ieee">IEEE格式</option>
                        <option value="gb7714">GB/T 7714格式</option>
                        <option value="school">学校要求格式</option>
                        <option value="custom">自定义格式</option>
                    </select>
                </div>

                <div class="form-group fade-in-up" style="animation-delay: 0.8s;">
                    <label class="form-label flex items-center gap-2">
                        <i data-lucide="calendar" class="w-4 h-4 text-red-600"></i>
                        截止时间
                    </label>
                    <input type="date" name="deadline" class="form-input">
                    <div class="text-xs text-gray-500 mt-1">选择论文提交截止时间</div>
                </div>
            </div>

            <!-- 第五行：具体要求 -->
            <div class="form-group fade-in-up" style="animation-delay: 0.9s;">
                <label class="form-label flex items-center gap-2">
                    <i data-lucide="file-text" class="w-4 h-4 text-teal-600"></i>
                    具体要求 *
                </label>
                <textarea name="requirements" class="form-input form-textarea" rows="4" placeholder="请详细描述论文的具体要求、研究方向、重点内容等..." required></textarea>
                <div class="text-xs text-red-500 mt-1 hidden" id="requirements-error"></div>
                <div class="text-xs text-gray-500 mt-1">详细描述有助于AI生成更准确的内容</div>
            </div>
        </div>
    `;
}

// 生成特定字段
function generateSpecificFields(specificFields) {
    let html = '<div class="space-y-6">';
    let animationDelay = 0.4;

    Object.entries(specificFields).forEach(([name, field]) => {
        const iconMap = {
            'researchDirection': 'target',
            'reviewTopic': 'search',
            'company': 'building',
            'position': 'briefcase',
            'businessType': 'trending-up',
            'researchMethod': 'microscope',
            'reportType': 'file-text',
            'researchQuestion': 'help-circle',
            'customType': 'settings'
        };

        const icon = iconMap[name] || 'circle';
        const colorMap = {
            'researchDirection': 'text-blue-600',
            'reviewTopic': 'text-green-600',
            'company': 'text-purple-600',
            'position': 'text-orange-600',
            'businessType': 'text-red-600',
            'researchMethod': 'text-cyan-600',
            'reportType': 'text-indigo-600',
            'researchQuestion': 'text-pink-600',
            'customType': 'text-gray-600'
        };

        const iconColor = colorMap[name] || 'text-gray-600';

        html += `<div class="form-group fade-in-up" style="animation-delay: ${animationDelay}s;">`;
        html += `<label class="form-label flex items-center gap-2">`;
        html += `<i data-lucide="${icon}" class="w-4 h-4 ${iconColor}"></i>`;
        html += `${field.label}${field.required ? ' *' : ''}`;
        html += `</label>`;

        if (field.type === 'text') {
            html += `<input type="text" name="${name}" class="form-input" placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>`;
        } else if (field.type === 'textarea') {
            html += `<textarea name="${name}" class="form-input form-textarea" placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}></textarea>`;
        } else if (field.type === 'select') {
            html += `<select name="${name}" class="form-input form-select" ${field.required ? 'required' : ''}>`;
            html += `<option value="">请选择${field.label}</option>`;
            Object.entries(field.options).forEach(([value, label]) => {
                html += `<option value="${value}">${label}</option>`;
            });
            html += `</select>`;
        }

        html += `<div class="text-xs text-red-500 mt-1 hidden" id="${name}-error"></div>`;
        html += `</div>`;

        animationDelay += 0.1;
    });

    // 内容描述字段
    html += `
        <div class="form-group fade-in-up" style="animation-delay: ${animationDelay}s;">
            <label class="form-label flex items-center gap-2">
                <i data-lucide="file-text" class="w-4 h-4 text-blue-600"></i>
                内容描述 *
            </label>
            <textarea name="content" class="form-input form-textarea" placeholder="请详细描述您的需求，包括研究背景、目标、方法等..." required></textarea>
            <div class="flex justify-between items-center mt-1">
                <div class="text-xs text-gray-500">
                    <span id="content-count">0</span> / 5000 字符
                </div>
                <div class="text-xs text-gray-400">
                    详细描述有助于生成更精准的内容
                </div>
            </div>
            <div class="text-xs text-red-500 mt-1 hidden" id="content-error"></div>
        </div>
    `;

    html += '</div>';
    return html;
}

// 生成操作按钮 - 动态支持不同论文类型
function generateActionButtons() {
    // 获取当前论文类型的配置
    const currentType = window.currentEssayType || 'graduation';
    const typeConfig = FORM_FIELDS[currentType] || FORM_FIELDS.graduation;
    
    // 论文类型映射表
    const typeIconMap = {
        graduation: 'graduation-cap',
        literature: 'book-open',
        proposal: 'file-text',
        task: 'clipboard-list',
        journal: 'newspaper',
        bachelor: 'book-marked',
        term: 'pen-tool',
        course: 'file-check',
        practice: 'settings',
        business: 'briefcase',
        research: 'search',
        professional: 'trending-up'
    };

    const typeEmojiMap = {
        graduation: '🎓',
        literature: '📚',
        proposal: '📝',
        task: '📋',
        journal: '📰',
        bachelor: '🎯',
        term: '✏️',
        course: '📖',
        practice: '🏢',
        business: '💼',
        research: '🔬',
        professional: '📈'
    };

    const icon = typeIconMap[currentType] || 'file-text';
    const emoji = typeEmojiMap[currentType] || '📝';
    const buttonText = `生成${typeConfig.title.replace('写作', '')}`;

    return `
        <div class="pt-8 border-t border-gray-200 fade-in-up" style="animation-delay: 0.8s;">
            <div class="flex flex-col sm:flex-row gap-4">
                <button type="button" onclick="generateOutline()" class="btn btn-secondary flex-1 btn-lg group hover:scale-105 transition-all duration-300">
                    <i data-lucide="list" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                    <span>生成大纲</span>
                    <div class="ml-auto text-xs opacity-70 hidden sm:block">预览结构</div>
                </button>
                <button type="button" onclick="generateEssay()" class="btn btn-primary flex-1 btn-lg group hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl" id="generate-btn">
                    <i data-lucide="${icon}" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                    <span>${buttonText}</span>
                    <div class="ml-auto text-xs opacity-90 hidden sm:block">${emoji} AI智能</div>
                </button>
            </div>

            <!-- 动态提示信息 -->
            <div class="mt-4 p-4 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-xl border border-blue-200 shadow-lg">
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <i data-lucide="${icon}" class="w-3 h-3 text-white"></i>
                    </div>
                    <div class="text-sm">
                        <h4 class="font-medium text-gray-900 mb-2 flex items-center gap-2">
                            ${emoji} ${typeConfig.title}生成提示
                        </h4>
                        <p class="text-gray-700 leading-relaxed">
                            ${typeConfig.description}建议先生成大纲预览整体结构，确认无误后再生成完整内容。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 增强的功能特色 -->
            <div class="mt-6 grid grid-cols-1 sm:grid-cols-4 gap-3 text-center">
                <div class="group p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <i data-lucide="shield-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div class="text-sm font-medium text-green-800">原创保证</div>
                    <div class="text-xs text-green-600">100%原创内容</div>
                </div>
                <div class="group p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl border border-blue-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <i data-lucide="zap" class="w-5 h-5 text-white"></i>
                    </div>
                    <div class="text-sm font-medium text-blue-800">快速生成</div>
                    <div class="text-xs text-blue-600">3分钟完成</div>
                </div>
                <div class="group p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <i data-lucide="edit-3" class="w-5 h-5 text-white"></i>
                    </div>
                    <div class="text-sm font-medium text-purple-800">无限修改</div>
                    <div class="text-xs text-purple-600">随时调整</div>
                </div>
                <div class="group p-4 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-xl border border-orange-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <i data-lucide="star" class="w-5 h-5 text-white"></i>
                    </div>
                    <div class="text-sm font-medium text-orange-800">专业质量</div>
                    <div class="text-xs text-orange-600">学术标准</div>
                </div>
            </div>

            <!-- 快捷键提示 -->
            <div class="mt-4 text-center">
                <p class="text-xs text-gray-500">
                    💡 快捷键：<kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl</kbd> + <kbd class="px-2 py-1 bg-gray-100 rounded text-xs">Enter</kbd> 快速生成
                </p>
            </div>
        </div>
    `;
}

// 增强的表单验证
function validateForm() {
    const form = document.getElementById('essay-form');
    if (!form) return false;
    
    let isValid = true;
    const formData = new FormData(form);
    
    // 清除之前的错误信息
    clearValidationErrors();
    
    // 验证每个字段
    for (const [name, value] of formData.entries()) {
        const rule = VALIDATION_RULES[name];
        if (!rule) continue;
        
        const field = form.querySelector(`[name="${name}"]`);
        const errorElement = document.getElementById(`${name}-error`);
        
        // 必填验证
        if (rule.required && !value.trim()) {
            showFieldError(field, errorElement, rule.message || `${name}为必填项`);
            isValid = false;
            continue;
        }
        
        // 长度验证
        if (value && rule.minLength && value.length < rule.minLength) {
            showFieldError(field, errorElement, rule.message);
            isValid = false;
            continue;
        }
        
        if (value && rule.maxLength && value.length > rule.maxLength) {
            showFieldError(field, errorElement, rule.message);
            isValid = false;
            continue;
        }
        
        // 清除错误状态
        clearFieldError(field, errorElement);
    }
    
    if (!isValid) {
        showToast('请检查并修正表单中的错误', 'error');
    }
    
    return isValid;
}

// 显示字段错误
function showFieldError(field, errorElement, message) {
    if (field) {
        field.classList.add('border-red-500');
    }
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
    }
}

// 清除字段错误
function clearFieldError(field, errorElement) {
    if (field) {
        field.classList.remove('border-red-500');
    }
    if (errorElement) {
        errorElement.classList.add('hidden');
    }
}

// 清除所有验证错误
function clearValidationErrors() {
    const form = document.getElementById('essay-form');
    if (!form) return;
    
    // 清除错误样式
    form.querySelectorAll('.border-red-500').forEach(field => {
        field.classList.remove('border-red-500');
    });
    
    // 隐藏错误信息
    form.querySelectorAll('[id$="-error"]').forEach(errorElement => {
        errorElement.classList.add('hidden');
    });
}

// 增强的表单事件绑定
function bindFormEvents() {
    const form = document.getElementById('essay-form');
    if (!form) return;
    
    // 实时验证和保存
    form.addEventListener('input', function(e) {
        const { name, value } = e.target;
        
        // 更新表单数据
        formData[name] = value;
        saveFormData();
        
        // 实时字符计数
        if (name === 'content') {
            updateContentCount(value);
        }
        
        // 实时验证
        validateField(e.target);
    });
    
    // 下拉选择事件
    form.addEventListener('change', function(e) {
        const { name, value } = e.target;
        formData[name] = value;
        saveFormData();
        
        // 处理自定义字数
        if (name === 'wordCount' && value === 'custom') {
            showCustomWordCountInput();
        }
        
        validateField(e.target);
    });
    
    // 重新初始化图标
    initializeLucideIcons();
}

// 验证单个字段
function validateField(field) {
    const name = field.name;
    const value = field.value;
    const rule = VALIDATION_RULES[name];
    
    if (!rule) return;
    
    const errorElement = document.getElementById(`${name}-error`);
    
    // 必填验证
    if (rule.required && !value.trim()) {
        showFieldError(field, errorElement, rule.message || `${name}为必填项`);
        return;
    }
    
    // 长度验证
    if (value && rule.minLength && value.length < rule.minLength) {
        showFieldError(field, errorElement, rule.message);
        return;
    }
    
    if (value && rule.maxLength && value.length > rule.maxLength) {
        showFieldError(field, errorElement, rule.message);
        return;
    }
    
    // 清除错误状态
    clearFieldError(field, errorElement);
}

// 更新内容字符计数
function updateContentCount(content) {
    const countElement = document.getElementById('content-count');
    if (countElement) {
        countElement.textContent = content.length;
        
        // 根据字符数改变颜色
        if (content.length > 4500) {
            countElement.className = 'text-red-500 font-medium';
        } else if (content.length > 4000) {
            countElement.className = 'text-yellow-500 font-medium';
        } else {
            countElement.className = 'text-gray-500';
        }
    }
}
