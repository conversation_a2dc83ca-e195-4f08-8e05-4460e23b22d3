<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化效果测试 - 博晓文</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f9fafb;
            color: #374151;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .title {
            font-size: 2rem;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 1rem;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        
        .test-title {
            font-weight: bold;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .test-result {
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .log {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 前端优化效果测试</h1>
            <p>测试优化后的页面性能和功能</p>
        </div>

        <!-- 性能指标 -->
        <div class="metrics" id="metrics">
            <div class="metric-card">
                <div class="metric-value" id="load-time">--</div>
                <div class="metric-label">页面加载时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="dom-time">--</div>
                <div class="metric-label">DOM就绪时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="memory-usage">--</div>
                <div class="metric-label">内存使用 (MB)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="resource-count">--</div>
                <div class="metric-label">资源数量</div>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="test-section">
            <div class="test-title">📋 功能测试</div>
            <div id="function-tests"></div>
            <button class="btn" onclick="runFunctionTests()">运行功能测试</button>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <div class="test-title">⚡ 性能测试</div>
            <div id="performance-tests"></div>
            <button class="btn" onclick="runPerformanceTests()">运行性能测试</button>
        </div>

        <!-- 兼容性测试 -->
        <div class="test-section">
            <div class="test-title">🔧 兼容性测试</div>
            <div id="compatibility-tests"></div>
            <button class="btn" onclick="runCompatibilityTests()">运行兼容性测试</button>
        </div>

        <!-- 日志输出 -->
        <div class="test-section">
            <div class="test-title">📝 测试日志</div>
            <div class="log" id="test-log"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <button class="btn" onclick="exportResults()">导出结果</button>
        </div>
    </div>

    <script>
        let testResults = {};
        let startTime = performance.now();

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        // 更新性能指标
        function updateMetrics() {
            const loadTime = performance.now() - startTime;
            document.getElementById('load-time').textContent = Math.round(loadTime);

            if (performance.timing) {
                const domTime = performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart;
                document.getElementById('dom-time').textContent = domTime;
            }

            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory-usage').textContent = memoryMB;
            }

            const resourceCount = performance.getEntriesByType('resource').length;
            document.getElementById('resource-count').textContent = resourceCount;
        }

        // 功能测试
        function runFunctionTests() {
            const testsContainer = document.getElementById('function-tests');
            testsContainer.innerHTML = '';
            
            const tests = [
                {
                    name: 'CSS加载器存在',
                    test: () => window.cssLoader !== undefined,
                },
                {
                    name: '性能监控器存在',
                    test: () => window.performanceMonitor !== undefined,
                },
                {
                    name: 'Tailwind CSS加载',
                    test: () => {
                        const testEl = document.createElement('div');
                        testEl.className = 'bg-blue-500';
                        document.body.appendChild(testEl);
                        const bgColor = getComputedStyle(testEl).backgroundColor;
                        document.body.removeChild(testEl);
                        return bgColor.includes('59, 130, 246') || bgColor.includes('rgb(59, 130, 246)');
                    }
                },
                {
                    name: '本地存储可用',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'test');
                            localStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    const resultEl = document.createElement('div');
                    resultEl.className = `test-result ${result ? 'success' : 'error'}`;
                    resultEl.textContent = `${test.name}: ${result ? '✅ 通过' : '❌ 失败'}`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = result;
                    log(`功能测试 - ${test.name}: ${result ? '通过' : '失败'}`, result ? 'success' : 'error');
                } catch (error) {
                    const resultEl = document.createElement('div');
                    resultEl.className = 'test-result error';
                    resultEl.textContent = `${test.name}: ❌ 错误 - ${error.message}`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = false;
                    log(`功能测试 - ${test.name}: 错误 - ${error.message}`, 'error');
                }
            });
        }

        // 性能测试
        function runPerformanceTests() {
            const testsContainer = document.getElementById('performance-tests');
            testsContainer.innerHTML = '';

            const tests = [
                {
                    name: '页面加载时间 < 3秒',
                    test: () => (performance.now() - startTime) < 3000,
                    value: () => Math.round(performance.now() - startTime) + 'ms'
                },
                {
                    name: '内存使用 < 50MB',
                    test: () => {
                        if (!performance.memory) return true;
                        return performance.memory.usedJSHeapSize < 50 * 1024 * 1024;
                    },
                    value: () => performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB' : 'N/A'
                },
                {
                    name: '资源数量 < 20个',
                    test: () => performance.getEntriesByType('resource').length < 20,
                    value: () => performance.getEntriesByType('resource').length + '个'
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    const value = test.value ? test.value() : '';
                    const resultEl = document.createElement('div');
                    resultEl.className = `test-result ${result ? 'success' : 'warning'}`;
                    resultEl.textContent = `${test.name}: ${result ? '✅ 通过' : '⚠️ 超标'} (${value})`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = { result, value };
                    log(`性能测试 - ${test.name}: ${result ? '通过' : '超标'} (${value})`, result ? 'success' : 'warning');
                } catch (error) {
                    const resultEl = document.createElement('div');
                    resultEl.className = 'test-result error';
                    resultEl.textContent = `${test.name}: ❌ 错误 - ${error.message}`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = { result: false, error: error.message };
                    log(`性能测试 - ${test.name}: 错误 - ${error.message}`, 'error');
                }
            });
        }

        // 兼容性测试
        function runCompatibilityTests() {
            const testsContainer = document.getElementById('compatibility-tests');
            testsContainer.innerHTML = '';

            const tests = [
                {
                    name: 'ES6支持',
                    test: () => {
                        try {
                            eval('const test = () => true; test()');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Promise支持',
                    test: () => typeof Promise !== 'undefined'
                },
                {
                    name: 'Fetch API支持',
                    test: () => typeof fetch !== 'undefined'
                },
                {
                    name: 'Performance API支持',
                    test: () => typeof performance !== 'undefined' && typeof performance.now === 'function'
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    const resultEl = document.createElement('div');
                    resultEl.className = `test-result ${result ? 'success' : 'warning'}`;
                    resultEl.textContent = `${test.name}: ${result ? '✅ 支持' : '⚠️ 不支持'}`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = result;
                    log(`兼容性测试 - ${test.name}: ${result ? '支持' : '不支持'}`, result ? 'success' : 'warning');
                } catch (error) {
                    const resultEl = document.createElement('div');
                    resultEl.className = 'test-result error';
                    resultEl.textContent = `${test.name}: ❌ 错误 - ${error.message}`;
                    testsContainer.appendChild(resultEl);
                    
                    testResults[test.name] = false;
                    log(`兼容性测试 - ${test.name}: 错误 - ${error.message}`, 'error');
                }
            });
        }

        // 清空日志
        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        // 导出结果
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                testResults: testResults,
                performance: {
                    loadTime: Math.round(performance.now() - startTime),
                    memory: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : null,
                    resourceCount: performance.getEntriesByType('resource').length
                }
            };

            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `optimization-test-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            log('测试结果已导出', 'success');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成', 'success');
            updateMetrics();
            
            // 定期更新指标
            setInterval(updateMetrics, 1000);
        });

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('开始自动测试...', 'info');
                runFunctionTests();
                runPerformanceTests();
                runCompatibilityTests();
            }, 1000);
        });
    </script>
</body>
</html>
