/**
 * 关键CSS - 首屏渲染必需的样式
 * 内联到HTML中以避免渲染阻塞
 */

/* CSS变量 - 核心颜色和尺寸 */
:root {
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-500: #6b7280;
    --gray-700: #374151;
    --gray-900: #111827;
    --white: #ffffff;
    
    /* 关键尺寸 */
    --header-height: 5rem;
    --sidebar-width: 16rem;
    --border-radius: 0.5rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 基础重置 */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 基础样式 - 性能优化 */
html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    /* 优化字体渲染 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 优化滚动性能 */
    scroll-behavior: smooth;
}

body {
    background-color: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
    min-height: 100vh;
    /* 防止水平滚动 */
    overflow-x: hidden;
    /* 优化渲染性能 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 优化触摸滚动 */
    -webkit-overflow-scrolling: touch;
}

/* 布局容器 */
.container {
    width: 100%;
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 头部样式 - 性能优化 */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 50;
    /* GPU加速 */
    will-change: transform;
    transform: translateZ(0);
    /* 优化合成层 */
    contain: layout style paint;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1.5rem;
}

/* 侧边栏样式 - 性能优化 */
.sidebar {
    position: fixed;
    left: 0;
    top: var(--header-height);
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 40;
    /* GPU加速 */
    will-change: transform;
    /* 优化合成层 */
    contain: layout style paint;
    /* 优化滚动 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.sidebar.open {
    transform: translateX(0);
}

/* 主内容区域 */
.main-content {
    padding-top: var(--header-height);
    padding-bottom: 4rem;
    transition: margin-left 0.3s ease;
}

/* 按钮基础样式 - 性能优化 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 2.5rem;
    /* 移动端优化 */
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    /* GPU加速 */
    will-change: transform;
    transform: translateZ(0);
}

.btn-primary {
    background: var(--primary-500);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
    background: var(--primary-600);
    transform: translateY(-1px) translateZ(0);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
    transform: translateY(0) translateZ(0);
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
}

/* 表单基础样式 */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    background: var(--white);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 卡片基础样式 */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

/* 加载状态 */
.loading {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 基础动画 */
.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式 */
@media (min-width: 1024px) {
    .sidebar {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: var(--sidebar-width);
    }
    
    .container {
        padding: 0 2rem;
    }
}

/* 移动端优化 */
@media (max-width: 640px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .btn {
        min-height: 2.75rem;
        padding: 0.75rem 1.5rem;
    }
    
    .form-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 0.75rem;
    }
}

/* 可访问性 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }
    
    .form-input {
        border: 2px solid #000;
    }
}

/* 暗色模式基础支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-500: #9ca3af;
        --gray-700: #f3f4f6;
        --gray-900: #ffffff;
        --white: #111827;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    header,
    .btn,
    .fab {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding-top: 0 !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
