// 首页专用显示修复脚本
(function() {
    'use strict';

    console.log('🏠 首页显示修复脚本加载...');

    // 首页显示修复器
    class IndexDisplayFixer {
        constructor() {
            this.fixAttempts = 0;
            this.maxAttempts = 5;
            this.isFixed = false;
        }

        // 强制显示所有首页内容
        forceShowIndexContent() {
            console.log('🔧 首页内容强制显示...');

            // 1. 确保页头、侧边栏、移动端导航可见
            const navigationElements = [
                '#header-container',
                '#sidebar-container', 
                '#mobile-nav-container',
                'header',
                'nav',
                '.sidebar'
            ];

            navigationElements.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.opacity = '1';
                    el.style.visibility = 'visible';
                    el.style.display = el.style.display === 'none' ? '' : el.style.display;
                });
            });

            // 2. 强制显示主要内容区域
            const mainContainers = [
                'main',
                '.container',
                '.max-w-7xl',
                '.essay-types-grid'
            ];

            mainContainers.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.opacity = '1';
                    el.style.visibility = 'visible';
                });
            });

            // 3. 特别处理卡片标题 - 这是最容易出问题的地方
            const cardTitles = document.querySelectorAll('.essay-type-card h3');
            cardTitles.forEach((h3, index) => {
                // 使用最强的样式覆盖
                h3.style.cssText = `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    color: #111827 !important;
                    font-size: 0.875rem !important;
                    line-height: 1.25rem !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    margin-bottom: 0.25rem !important;
                    -webkit-text-fill-color: #111827 !important;
                    text-fill-color: #111827 !important;
                `;
                
                // 添加类名确保样式生效
                h3.classList.add('force-visible');
                
                console.log(`✅ 卡片标题 ${index + 1}: "${h3.textContent}" 已强制显示`);
            });

            // 4. 确保所有文字内容可见
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, button, label');
            textElements.forEach(el => {
                if (el.style.opacity === '0' && !el.classList.contains('opacity-0')) {
                    el.style.opacity = '1';
                }
                if (el.style.visibility === 'hidden' && !el.classList.contains('invisible')) {
                    el.style.visibility = 'visible';
                }
            });

            // 5. 确保图标可见
            document.querySelectorAll('i[data-lucide], .lucide').forEach(icon => {
                icon.style.opacity = '1';
                icon.style.visibility = 'visible';
                icon.style.display = 'inline-block';
            });

            console.log('✅ 首页内容强制显示完成');
        }

        // 检查显示状态
        checkDisplayStatus() {
            const cardTitles = document.querySelectorAll('.essay-type-card h3');
            const visibleTitles = Array.from(cardTitles).filter(h3 => {
                const computed = window.getComputedStyle(h3);
                return computed.opacity !== '0' && 
                       computed.visibility !== 'hidden' && 
                       computed.display !== 'none';
            });

            const headerContainer = document.getElementById('header-container');
            const sidebarContainer = document.getElementById('sidebar-container');

            console.log(`📊 显示状态检查:
                - 卡片标题: ${visibleTitles.length}/${cardTitles.length} 可见
                - 头部容器: ${headerContainer && headerContainer.innerHTML.trim() ? '✅' : '❌'}
                - 侧边栏容器: ${sidebarContainer && sidebarContainer.innerHTML.trim() ? '✅' : '❌'}
            `);

            return {
                titlesVisible: visibleTitles.length === cardTitles.length,
                headerLoaded: headerContainer && headerContainer.innerHTML.trim(),
                sidebarLoaded: sidebarContainer && sidebarContainer.innerHTML.trim()
            };
        }

        // 修复循环
        fixLoop() {
            this.fixAttempts++;
            console.log(`🔄 第 ${this.fixAttempts} 次修复尝试...`);

            this.forceShowIndexContent();
            
            setTimeout(() => {
                const status = this.checkDisplayStatus();
                
                if (status.titlesVisible && status.headerLoaded && status.sidebarLoaded) {
                    console.log('🎉 首页显示修复成功！');
                    this.isFixed = true;
                } else if (this.fixAttempts < this.maxAttempts) {
                    console.log('⚠️ 仍有问题，继续修复...');
                    setTimeout(() => this.fixLoop(), 1000);
                } else {
                    console.log('⚠️ 达到最大修复次数，停止尝试');
                }
            }, 500);
        }

        // 初始化
        init() {
            console.log('🚀 首页显示修复器初始化...');
            
            // 立即执行一次
            this.forceShowIndexContent();
            
            // 开始修复循环
            setTimeout(() => this.fixLoop(), 500);
        }
    }

    // 创建修复器实例
    const indexFixer = new IndexDisplayFixer();

    // 页面加载时初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => indexFixer.init(), 200);
        });
    } else {
        setTimeout(() => indexFixer.init(), 200);
    }

    // 页面完全加载后再次修复
    window.addEventListener('load', () => {
        setTimeout(() => indexFixer.forceShowIndexContent(), 1000);
    });

    // 页面可见性变化时修复
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            setTimeout(() => indexFixer.forceShowIndexContent(), 100);
        }
    });

    // 导出到全局
    window.IndexDisplayFixer = indexFixer;

    console.log('✅ 首页显示修复脚本加载完成');
})();
