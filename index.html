<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博晓文 - 智能AI论文写作平台</title>
    <meta name="description" content="专业的AI论文写作平台，支持毕业论文、期刊论文、文献综述等多种类型，3秒生成大纲，100%原创保证">
    <meta name="keywords" content="AI论文写作,毕业论文,期刊论文,文献综述,智能写作,学术论文">
    
    <!-- DNS预解析和预连接 -->
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
    <link rel="preconnect" href="https://unpkg.com" crossorigin>

    <!-- 关键CSS内联 -->
    <style data-critical="true">
        /* 关键CSS将通过JavaScript动态注入 */
    </style>

    <!-- 紧急修复CSS - 强制显示所有内容 -->
    <style data-emergency="true">
        /* 紧急修复 - 强制显示所有内容 */
        * {
            opacity: 1 !important;
            visibility: visible !important;
        }

        .hidden.lg\:grid {
            display: grid !important;
        }

        .hidden.lg\:block {
            display: block !important;
        }

        .hidden.lg\:flex {
            display: flex !important;
        }

        #header-container,
        #sidebar-container,
        #mobile-nav-container,
        main,
        .container,
        .essay-type-card,
        .essay-type-card h3 {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        #essay-types-desktop {
            display: grid !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        .essay-type-card h3 {
            color: #111827 !important;
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
        }
    </style>

    <!-- 关键CSS立即加载 -->
    <link rel="stylesheet" href="styles/main.css">

    <!-- 非关键CSS延迟加载 -->
    <link rel="preload" href="styles/components.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="styles/animations.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="styles/components.css">
        <link rel="stylesheet" href="styles/animations.css">
    </noscript>

    <!-- 立即执行的紧急修复脚本 -->
    <script>
        // 立即修复显示问题
        (function() {
            console.log('🚨 立即修复脚本执行...');

            function immediateFixDisplay() {
                // 移除可能导致隐藏的类
                setTimeout(() => {
                    const hiddenElements = document.querySelectorAll('.hidden.lg\\:grid, .hidden.lg\\:block, .hidden.lg\\:flex');
                    hiddenElements.forEach(el => {
                        el.classList.remove('hidden');
                        console.log('✅ 移除隐藏类:', el);
                    });

                    // 强制显示主要容器
                    const containers = ['#essay-types-desktop', 'main', '.container'];
                    containers.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.cssText += 'opacity: 1 !important; visibility: visible !important; display: block !important;';
                            if (selector.includes('essay-types') || el.classList.contains('grid')) {
                                el.style.display = 'grid !important';
                            }
                        });
                    });

                    console.log('✅ 立即修复完成');
                }, 10);
            }

            // 立即执行
            immediateFixDisplay();

            // DOM变化时执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', immediateFixDisplay);
            }

            // 定期执行
            setInterval(immediateFixDisplay, 1000);
        })();
    </script>

    <!-- Tailwind CSS - 延迟加载 -->
    <script>
        // 延迟加载Tailwind CSS
        window.addEventListener('load', function() {
            const script = document.createElement('script');
            script.src = 'https://cdn.tailwindcss.com';
            script.async = true;
            document.head.appendChild(script);
        });
    </script>

    <!-- Lucide Icons - 延迟加载 -->
    <script>
        // 延迟加载图标库
        window.addEventListener('load', function() {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/lucide@latest/dist/umd/lucide.js';
            script.async = true;
            script.onload = function() {
                if (window.lucide) {
                    window.lucide.createIcons();
                }
            };
            document.head.appendChild(script);
        });
    </script>

    <!-- 优化的论文类型卡片样式和页面一致性样式 -->
    <style>
        /* 页面一致性样式 */
        .particles-bg {
            position: relative;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .gradient-text-green {
            background: linear-gradient(135deg, #10b981, #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 性能优化的卡片样式 */
        .essay-type-card {
            transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                        box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform;
            transform: translateZ(0);
        }

        .essay-type-card:hover {
            transform: translateY(-2px) translateZ(0);
        }

        /* 移除重复的样式定义，已在下方统一定义 */
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .essay-type-card:hover .icon-container {
            transform: scale(1.1);
        }

        /* 最终强制显示样式 - 解决所有显示问题 */
        .essay-type-card h3 {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
            color: #111827 !important;
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
            transition: color 0.2s ease !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            -webkit-text-fill-color: #111827 !important;
            text-fill-color: #111827 !important;
        }

        /* 确保所有主要内容都可见 */
        main,
        .container,
        .essay-types-grid,
        .essay-type-card {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 移除任何可能的隐藏效果 */
        * {
            -webkit-text-fill-color: unset !important;
        }

        .essay-type-card h3 * {
            opacity: 1 !important;
            visibility: visible !important;
            color: inherit !important;
        }

        /* 简化的悬停颜色变化 */
        .essay-type-card[data-type="literature"]:hover h3 { color: #dc2626; }
        .essay-type-card[data-type="proposal"]:hover h3 { color: #2563eb; }
        .essay-type-card[data-type="task"]:hover h3 { color: #0891b2; }
        .essay-type-card[data-type="journal"]:hover h3 { color: #16a34a; }
        .essay-type-card[data-type="bachelor"]:hover h3 { color: #65a30d; }
        .essay-type-card[data-type="term"]:hover h3 { color: #9333ea; }
        .essay-type-card[data-type="course"]:hover h3 { color: #4f46e5; }
        .essay-type-card[data-type="practice"]:hover h3 { color: #0d9488; }
        .essay-type-card[data-type="business"]:hover h3 { color: #2563eb; }
        .essay-type-card[data-type="research"]:hover h3 { color: #059669; }
        .essay-type-card[data-type="professional"]:hover h3 { color: #db2777; }

        /* 移除强制覆盖规则，避免冲突 */
    </style>

    <!-- JavaScript保护机制 - 确保文字永远不消失 -->
    <script>
        // 简化的显示确保函数 - 移除复杂逻辑
        function ensureAllContentVisible() {
            console.log('🔧 确保所有内容可见...');

            // 强制显示所有卡片标题
            document.querySelectorAll('.essay-type-card h3').forEach((h3, index) => {
                h3.style.cssText = `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    color: #111827 !important;
                    font-size: 0.875rem !important;
                    line-height: 1.25rem !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    margin-bottom: 0.25rem !important;
                    -webkit-text-fill-color: #111827 !important;
                `;
                console.log(`✅ 标题 ${index + 1} 已强制显示`);
            });

            // 确保主要容器可见
            const containers = ['main', '.container', '.essay-types-grid'];
            containers.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.opacity = '1';
                    el.style.visibility = 'visible';
                });
            });

            console.log('✅ 所有内容显示确保完成');
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(ensureAllContentVisible, 100);
        });

        // 页面完全加载后再次执行
        window.addEventListener('load', () => {
            setTimeout(ensureAllContentVisible, 500);
        });
    </script>
    
    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            500: '#64748b',
                            600: '#475569'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'slide-in-right': 'slideInRight 0.6s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
                        'gradient-shift': 'gradientShift 3s ease-in-out infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideInRight: {
                            '0%': { opacity: '0', transform: 'translateX(20px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.9)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        },
                        gradientShift: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Meta tags for better SEO -->
    <meta name="author" content="博晓文">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://bxw.com">
    
    <!-- Open Graph -->
    <meta property="og:title" content="博晓文 - 智能AI论文写作平台">
    <meta property="og:description" content="专业的AI论文写作平台，支持多种论文类型，3秒生成大纲，100%原创保证">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://bxw.com">
    
    <!-- Performance optimization -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="preload" href="styles/main.css" as="style">
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-4 sm:p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                
                <!-- Page Title Section - 与其他页面保持一致的风格 -->
                <div class="mb-8 lg:mb-12 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                AI论文写作平台
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                选择您需要的论文类型，AI将为您智能生成专业、原创的学术内容
                                <span class="inline-block ml-2 text-blue-600">✨</span>
                            </p>

                            <!-- 特色标签 -->
                            <div class="flex flex-wrap gap-2 mt-4">
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
                                    🚀 3秒生成大纲
                                </span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                    ✅ 100%原创保证
                                </span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                    🔄 无限次修改
                                </span>
                                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full font-medium">
                                    🛡️ 专业保障
                                </span>
                            </div>

                            <!-- CTA按钮 -->
                            <div class="flex flex-col sm:flex-row gap-4 mt-6">
                                <button class="btn btn-primary btn-lg group px-6 py-3 text-base font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" onclick="scrollToEssayTypes()">
                                    <i data-lucide="edit-3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    开始创作
                                    <i data-lucide="arrow-right" class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"></i>
                                </button>
                                <button class="btn btn-outline btn-lg px-6 py-3 text-base font-semibold border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 transition-all duration-300" onclick="showDemoModal()">
                                    <i data-lucide="play-circle" class="w-5 h-5 mr-2"></i>
                                    观看演示
                                </button>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="hidden lg:flex space-x-6 xl:space-x-8">
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="cpu" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold gradient-text-green">AI智能</div>
                                <div class="text-sm text-gray-500">写作助手</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-blue-600">3秒</div>
                                <div class="text-sm text-gray-500">生成大纲</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="infinity" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-purple-600">无限次</div>
                                <div class="text-sm text-gray-500">修改</div>
                            </div>
                        </div>
                    </div>

                    <!-- 移动端统计 -->
                    <div class="lg:hidden grid grid-cols-3 gap-3 mt-6">
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-green-600">AI智能</div>
                            <div class="text-xs text-gray-500">写作助手</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-blue-600">3秒</div>
                            <div class="text-xs text-gray-500">生成大纲</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-purple-600">无限次</div>
                            <div class="text-xs text-gray-500">修改</div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <!-- Left Column - Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Essay Type Selection -->
                        <section id="essay-types-section" class="mb-8">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="layers" class="w-5 h-5 text-white"></i>
                                    </div>
                                    选择论文类型
                                </h2>
                                <div class="hidden sm:flex items-center gap-2 text-sm text-gray-500">
                                    <i data-lucide="mouse-pointer-click" class="w-4 h-4"></i>
                                    点击选择
                                </div>
                            </div>

                            <!-- Essay Type Grid - Desktop -->
                            <div class="hidden lg:grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8" id="essay-types-desktop">
                                <!-- JavaScript将渲染所有12个论文类型 -->
                                <!-- 备用静态内容 -->
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 hover:-translate-y-1" data-type="graduation" onclick="selectEssayType('graduation')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 icon-container transition-transform duration-300">
                                            <i data-lucide="graduation-cap" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3 class="font-bold text-blue-600 mb-1 text-sm leading-tight transition-colors">毕业论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="literature" onclick="selectEssayType('literature')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600 icon-container transition-transform duration-300">
                                            <i data-lucide="book-open" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>文献综述</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="proposal" onclick="selectEssayType('proposal')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 icon-container transition-transform duration-300">
                                            <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>开题报告</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="task" onclick="selectEssayType('task')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-cyan-500 to-cyan-600 icon-container transition-transform duration-300">
                                            <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>任务书</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="journal" onclick="selectEssayType('journal')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-green-500 to-green-600 icon-container transition-transform duration-300">
                                            <i data-lucide="newspaper" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>期刊论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="bachelor" onclick="selectEssayType('bachelor')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-lime-500 to-lime-600 icon-container transition-transform duration-300">
                                            <i data-lucide="book-marked" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>专升本论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="term" onclick="selectEssayType('term')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-purple-500 to-purple-600 icon-container transition-transform duration-300">
                                            <i data-lucide="pen-tool" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>期末论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="course" onclick="selectEssayType('course')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-indigo-500 to-indigo-600 icon-container transition-transform duration-300">
                                            <i data-lucide="file-check" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>课程论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="practice" onclick="selectEssayType('practice')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-teal-500 to-teal-600 icon-container transition-transform duration-300">
                                            <i data-lucide="settings" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>实习报告</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="business" onclick="selectEssayType('business')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 icon-container transition-transform duration-300">
                                            <i data-lucide="briefcase" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>创业策划</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="research" onclick="selectEssayType('research')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-emerald-500 to-emerald-600 icon-container transition-transform duration-300">
                                            <i data-lucide="search" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>研究论文</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="professional" onclick="selectEssayType('professional')">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-pink-500 to-pink-600 icon-container transition-transform duration-300">
                                            <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <h3>职业规划</h3>
                                        <div class="text-xs text-gray-500">专业定制</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Essay Type Grid - Mobile -->
                            <div class="lg:hidden grid grid-cols-2 gap-4 mb-8" id="essay-types-mobile">
                                <!-- JavaScript将渲染所有12个论文类型 -->
                                <!-- 备用静态内容 -->
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 hover:-translate-y-1" data-type="graduation" onclick="selectEssayType('graduation')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 icon-container transition-transform duration-300">
                                            <i data-lucide="graduation-cap" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3 class="font-bold text-blue-600 text-sm leading-tight transition-colors">毕业论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="literature" onclick="selectEssayType('literature')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600 icon-container transition-transform duration-300">
                                            <i data-lucide="book-open" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>文献综述</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="proposal" onclick="selectEssayType('proposal')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 icon-container transition-transform duration-300">
                                            <i data-lucide="file-text" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>开题报告</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="task" onclick="selectEssayType('task')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-cyan-500 to-cyan-600 icon-container transition-transform duration-300">
                                            <i data-lucide="clipboard-list" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>任务书</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="journal" onclick="selectEssayType('journal')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-green-500 to-green-600 icon-container transition-transform duration-300">
                                            <i data-lucide="newspaper" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>期刊论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="bachelor" onclick="selectEssayType('bachelor')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-lime-500 to-lime-600 icon-container transition-transform duration-300">
                                            <i data-lucide="book-marked" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>专升本论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="term" onclick="selectEssayType('term')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-purple-500 to-purple-600 icon-container transition-transform duration-300">
                                            <i data-lucide="pen-tool" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>期末论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="course" onclick="selectEssayType('course')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-indigo-500 to-indigo-600 icon-container transition-transform duration-300">
                                            <i data-lucide="file-check" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>课程论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="practice" onclick="selectEssayType('practice')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-teal-500 to-teal-600 icon-container transition-transform duration-300">
                                            <i data-lucide="settings" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>实习报告</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="business" onclick="selectEssayType('business')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 icon-container transition-transform duration-300">
                                            <i data-lucide="briefcase" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>创业策划</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="research" onclick="selectEssayType('research')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-emerald-500 to-emerald-600 icon-container transition-transform duration-300">
                                            <i data-lucide="search" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>研究论文</h3>
                                    </div>
                                </div>
                                <div class="essay-type-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 hover:-translate-y-1" data-type="professional" onclick="selectEssayType('professional')">
                                    <div class="text-center">
                                        <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-pink-500 to-pink-600 icon-container transition-transform duration-300">
                                            <i data-lucide="trending-up" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <h3>职业规划</h3>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- Dynamic Form Container -->
                        <section id="form-section" class="hidden">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 overflow-hidden animate-scale-in">
                                <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 text-white p-6 lg:p-8">
                                    <div class="flex items-center gap-4">
                                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <i data-lucide="edit-3" class="w-6 h-6"></i>
                                        </div>
                                        <div>
                                            <h2 class="text-xl lg:text-2xl font-bold" id="form-title">AI论文写作</h2>
                                            <p class="text-blue-100 mt-1 text-sm lg:text-base" id="form-description">填写论文要求，AI将为您生成专业的学术内容</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-6 lg:p-8" id="form-container">
                                    <!-- Form content will be populated by JavaScript -->
                                </div>
                            </div>
                        </section>

                        <!-- Tips for mobile -->
                        <div class="lg:hidden bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 text-white p-6 rounded-2xl text-center mb-8 shadow-xl animate-fade-in" id="mobile-tip">
                            <h3 class="font-bold mb-3 flex items-center justify-center gap-2 text-lg">
                                <i data-lucide="lightbulb" class="w-5 h-5"></i>
                                智能提示
                            </h3>
                            <p class="text-blue-100">选择论文类型后，填写详细信息即可快速生成专业大纲和内容</p>
                        </div>
                    </div>

                    <!-- Right Column - Enhanced Sidebar -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Real-time Stats -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.1s;">
                            <div class="bg-gradient-to-r from-blue-500 to-cyan-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                    </div>
                                    今日统计
                                </h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="flex justify-between items-center group hover:bg-blue-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="file-text" class="w-4 h-4"></i>
                                        生成论文
                                    </span>
                                    <span class="font-bold text-blue-600 text-lg group-hover:scale-110 transition-transform">3篇</span>
                                </div>
                                <div class="flex justify-between items-center group hover:bg-green-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="type" class="w-4 h-4"></i>
                                        总字数
                                    </span>
                                    <span class="font-bold text-green-600 text-lg group-hover:scale-110 transition-transform">15,000字</span>
                                </div>
                                <div class="flex justify-between items-center group hover:bg-purple-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="clock" class="w-4 h-4"></i>
                                        剩余次数
                                    </span>
                                    <span class="font-bold text-purple-600 text-lg group-hover:scale-110 transition-transform">47次</span>
                                </div>
                                <div class="pt-4 border-t border-gray-200">
                                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                                        <span>本月使用进度</span>
                                        <span>53/100</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-1000" style="width: 53%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Records -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.2s;">
                            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="history" class="w-4 h-4"></i>
                                    </div>
                                    最近记录
                                </h3>
                            </div>
                            <div class="p-6 space-y-3" id="recent-records">
                                <!-- Recent records will be populated by JavaScript -->
                            </div>
                            <div class="p-6 border-t border-gray-200">
                                <button class="w-full text-center text-blue-600 hover:text-blue-700 transition-colors font-medium flex items-center justify-center gap-2 group" onclick="window.location.href='history.html'">
                                    查看全部记录
                                    <i data-lucide="arrow-right" class="w-4 h-4 group-hover:translate-x-1 transition-transform"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.3s;">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="zap" class="w-4 h-4"></i>
                                    </div>
                                    快捷功能
                                </h3>
                            </div>
                            <div class="p-6 space-y-3" id="quick-actions">
                                <!-- Quick actions will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Enhanced floating help button -->
    <button class="fixed bottom-20 right-4 lg:bottom-6 lg:right-6 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 z-50 group hover:scale-110 animate-bounce-gentle" id="fab-help" title="获取帮助">
        <i data-lucide="help-circle" class="w-6 h-6 mx-auto group-hover:scale-110 transition-transform"></i>
    </button>

    <!-- Enhanced help modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 hidden animate-fade-in">
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl max-w-lg w-full max-h-[80vh] overflow-y-auto shadow-2xl border border-white/20 animate-scale-in">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold flex items-center gap-3">
                        <i data-lucide="help-circle" class="w-6 h-6"></i>
                        使用帮助
                    </h3>
                    <button onclick="closeHelpModal()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 space-y-6">
                <div class="space-y-4">
                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">1</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">选择论文类型</h4>
                            <p class="text-gray-600 text-sm">从12种专业论文类型中选择最适合您需求的类型，每种类型都有专门优化的AI模板</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">2</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">填写详细信息</h4>
                            <p class="text-gray-600 text-sm">填写论文标题、字数要求、学科领域、参考文献等基本信息，信息越详细，生成效果越好</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">3</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">AI智能生成</h4>
                            <p class="text-gray-600 text-sm">点击生成按钮，AI将分析您的需求并创建专业的学术内容，通常在30秒内完成</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                    <h5 class="font-medium text-blue-900 mb-3 flex items-center gap-2">
                        <i data-lucide="lightbulb" class="w-5 h-5"></i>
                        智能提示
                    </h5>
                    <ul class="text-sm text-blue-800 space-y-2">
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            详细的内容描述有助于生成更精准的论文
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            建议先生成大纲预览整体结构
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            生成后可以无限次修改和优化
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            支持多种格式导出和下载
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Modal -->
    <div id="demo-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 hidden animate-fade-in">
        <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden shadow-2xl">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold flex items-center gap-3">
                        <i data-lucide="play-circle" class="w-6 h-6"></i>
                        产品演示
                    </h3>
                    <button onclick="closeDemoModal()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="aspect-video bg-gray-100 rounded-xl flex items-center justify-center">
                    <div class="text-center">
                        <i data-lucide="play-circle" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <p class="text-gray-600">演示视频即将上线...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced back to top button -->
    <button id="back-to-top" class="fixed bottom-32 right-4 lg:bottom-24 lg:right-6 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 opacity-0 pointer-events-none z-40 hover:scale-110">
        <i data-lucide="arrow-up" class="w-5 h-5 mx-auto"></i>
    </button>

    <!-- Enhanced loading overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="text-center">
            <div class="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-6"></div>
            <div class="space-y-2">
                <p class="text-gray-800 font-semibold text-lg">AI正在为您生成内容...</p>
                <p class="text-gray-600">请稍候，这通常需要几秒钟</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/emergency-fix.js"></script>
    <script src="js/global-fix.js"></script>
    <script src="js/index-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/essay-forms.js"></script>
    <script src="js/main.js"></script>

    <!-- 统一的页面初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 首页初始化开始...');

            // 1. 首先初始化页面组件
            if (typeof UIComponents !== 'undefined') {
                UIComponents.initializePage('index');
                console.log('✅ 页面组件初始化完成');
            } else {
                console.error('❌ UIComponents未加载');
            }

            // 2. 强制显示所有卡片标题
            setTimeout(() => {
                document.querySelectorAll('.essay-type-card h3').forEach((h3, index) => {
                    h3.style.cssText = `
                        opacity: 1 !important;
                        visibility: visible !important;
                        display: block !important;
                        color: #111827 !important;
                        font-size: 0.875rem !important;
                        line-height: 1.25rem !important;
                        font-weight: 700 !important;
                        text-align: center !important;
                        margin-bottom: 0.25rem !important;
                    `;
                    console.log(`✅ 卡片标题 ${index + 1} 已强制显示`);
                });
                console.log('✅ 所有卡片标题显示完成');
            }, 200);

            // 3. 初始化增强功能
            initializeEnhancedFeatures();
            console.log('✅ 增强功能初始化完成');
        });

        // 简化的增强功能初始化
        function initializeEnhancedFeatures() {
            // 确保所有主要内容可见
            setTimeout(() => {
                // 强制显示主要容器
                const containers = ['main', '.container', '.essay-types-grid'];
                containers.forEach(selector => {
                    document.querySelectorAll(selector).forEach(el => {
                        el.style.opacity = '1';
                        el.style.visibility = 'visible';
                    });
                });

                // 再次确保卡片标题可见
                document.querySelectorAll('.essay-type-card h3').forEach(h3 => {
                    h3.style.opacity = '1';
                    h3.style.visibility = 'visible';
                    h3.style.display = 'block';
                    h3.style.color = '#111827';
                });

                console.log('✅ 内容可见性二次确认完成');
            }, 1000);

            // Smooth scroll for CTA button
            window.scrollToEssayTypes = function() {
                document.getElementById('essay-types-section').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            };

            // Demo modal functions
            window.showDemoModal = function() {
                document.getElementById('demo-modal').classList.remove('hidden');
            };

            window.closeDemoModal = function() {
                document.getElementById('demo-modal').classList.add('hidden');
            };

            // Enhanced scroll animations
            setupScrollAnimations();
            
            // Performance monitoring
            setupPerformanceMonitoring();
        }

        // Enhanced scroll animations
        function setupScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.lg\\:col-span-1 > div').forEach(el => {
                observer.observe(el);
            });
        }

        // Performance monitoring
        function setupPerformanceMonitoring() {
            // Monitor Core Web Vitals
            if ('web-vitals' in window) {
                // This would be implemented with a proper web-vitals library
                console.log('Performance monitoring enabled');
            }

            // Monitor page load performance
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
            });
        }

        // Enhanced help modal
        document.getElementById('fab-help').addEventListener('click', function() {
            document.getElementById('help-modal').classList.remove('hidden');
        });

        function closeHelpModal() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // Enhanced back to top functionality
        const backToTopBtn = document.getElementById('back-to-top');
        let ticking = false;

        function updateBackToTop() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.add('opacity-100');
            } else {
                backToTopBtn.classList.add('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.remove('opacity-100');
            }
            ticking = false;
        }

        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateBackToTop);
                ticking = true;
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Enhanced keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Esc to close modals
            if (e.key === 'Escape') {
                closeHelpModal();
                closeDemoModal();
            }

            // Ctrl/Cmd + Enter for quick generation
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const generateBtn = document.getElementById('generate-btn');
                if (generateBtn && !generateBtn.disabled) {
                    generateBtn.click();
                }
            }

            // Alt + H for help
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                document.getElementById('fab-help').click();
            }
        });

        // 页面可见性变化时确保内容显示
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(ensureAllContentVisible, 100);
            }
        });

        // Preload critical resources
        function preloadCriticalResources() {
            const criticalPaths = [
                'js/essay-forms.js',
                'styles/components.css'
            ];

            criticalPaths.forEach(path => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = path;
                link.as = path.endsWith('.js') ? 'script' : 'style';
                document.head.appendChild(link);
            });
        }

        // Initialize preloading
        preloadCriticalResources();
    </script>

    <!-- Analytics and tracking (placeholder) -->
    <!-- 集成所有性能优化功能 -->
    <script type="module">
        import { optimizedLoader } from './js/utils/optimized-loader.js';
        import { pageTransition } from './js/utils/page-transition.js';
        import { cssOptimizer } from './js/utils/css-optimizer.js';
        import { jsOptimizer } from './js/utils/js-optimizer.js';
        import { mediaOptimizer } from './js/utils/media-optimizer.js';
        import { animationOptimizer } from './js/utils/animation-optimizer.js';
        import { mobileOptimizer } from './js/utils/mobile-optimizer.js';
        import { performanceDashboard } from './js/utils/performance-dashboard.js';

        // 初始化性能监控
        console.log('🚀 All performance optimizations loaded');

        // 监听页面加载完成
        window.addEventListener('load', () => {
            const loadTime = performance.now();

            console.log('📊 Complete performance metrics:', {
                loadTime: loadTime.toFixed(2) + 'ms',
                resources: optimizedLoader.getLoadingStatus(),
                cache: pageTransition.getCacheStatus(),
                css: cssOptimizer.generateUsageReport(),
                media: mediaOptimizer.getPerformanceReport(),
                animations: animationOptimizer.getPerformanceReport(),
                mobile: mobileOptimizer.getPerformanceReport()
            });

            // 显示性能提示
            if (loadTime < 1000) {
                console.log('🎉 Excellent performance! Page loaded in under 1 second.');
            } else if (loadTime < 2000) {
                console.log('✅ Good performance! Page loaded in under 2 seconds.');
            } else {
                console.log('⚠️ Performance could be improved. Consider optimizations.');
            }
        });

        // 添加性能仪表板快捷键提示
        console.log('💡 Press Ctrl+Shift+P (or Cmd+Shift+P) to open Performance Dashboard');

        // 最终保险 - 3秒后强制显示
        setTimeout(() => {
            console.log('🛡️ 执行最终保险...');

            // 强制显示所有卡片标题
            document.querySelectorAll('.essay-type-card h3').forEach((h3, index) => {
                h3.style.cssText = `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    color: #111827 !important;
                    font-size: 0.875rem !important;
                    line-height: 1.25rem !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    margin-bottom: 0.25rem !important;
                `;
            });

            // 强制显示主要容器
            document.querySelectorAll('main, .container, .essay-types-grid').forEach(el => {
                el.style.opacity = '1';
                el.style.visibility = 'visible';
            });

            console.log('🎉 最终保险执行完成');
        }, 3000);
    </script>

    <!-- Service Worker注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('✅ SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('❌ SW registration failed: ', registrationError);
                    });
            });
        }
    </script>

    <!-- 延迟加载的分析脚本 -->
    <script>
        // 使用requestIdleCallback延迟加载分析脚本
        if ('requestIdleCallback' in window) {
            requestIdleCallback(function() {
                console.log('📈 Analytics tracking initialized');
                // 这里可以添加实际的分析代码
            });
        } else {
            setTimeout(function() {
                console.log('📈 Analytics tracking initialized');
            }, 2000);
        }
    </script>
</body>
</html>