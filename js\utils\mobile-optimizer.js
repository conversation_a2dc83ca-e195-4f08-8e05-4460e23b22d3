/**
 * 移动端性能优化器
 * 针对移动设备优化触摸交互、滚动性能和电池消耗
 */

class MobileOptimizer {
    constructor() {
        this.isMobile = this.detectMobile();
        this.touchSupported = 'ontouchstart' in window;
        this.batteryAPI = null;
        this.networkInfo = null;
        this.performanceMode = 'auto';
        this.scrollOptimized = false;
        
        this.init();
    }

    init() {
        if (!this.isMobile) {
            console.log('📱 Not a mobile device, skipping mobile optimizations');
            return;
        }
        
        this.detectDeviceCapabilities();
        this.optimizeTouchInteractions();
        this.optimizeScrollPerformance();
        this.optimizeBatteryUsage();
        this.optimizeNetworkUsage();
        this.setupViewportOptimization();
        this.optimizeInputs();
    }

    // 检测移动设备
    detectMobile() {
        const userAgent = navigator.userAgent;
        const mobileRegex = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
        
        return mobileRegex.test(userAgent) || 
               window.innerWidth <= 768 ||
               ('ontouchstart' in window);
    }

    // 检测设备能力
    async detectDeviceCapabilities() {
        // 检测电池API
        if ('getBattery' in navigator) {
            try {
                this.batteryAPI = await navigator.getBattery();
                this.monitorBatteryStatus();
            } catch (error) {
                console.log('Battery API not available');
            }
        }
        
        // 检测网络信息
        if ('connection' in navigator) {
            this.networkInfo = navigator.connection;
            this.monitorNetworkStatus();
        }
        
        // 检测设备内存
        const deviceMemory = navigator.deviceMemory || 4;
        const hardwareConcurrency = navigator.hardwareConcurrency || 4;
        
        // 根据设备能力设置性能模式
        if (deviceMemory <= 2 || hardwareConcurrency <= 2) {
            this.performanceMode = 'low';
        } else if (deviceMemory >= 6 && hardwareConcurrency >= 6) {
            this.performanceMode = 'high';
        } else {
            this.performanceMode = 'medium';
        }
        
        console.log('📱 Mobile performance mode:', this.performanceMode);
        this.applyPerformanceMode();
    }

    // 应用性能模式
    applyPerformanceMode() {
        const root = document.documentElement;
        
        switch (this.performanceMode) {
            case 'low':
                root.style.setProperty('--mobile-animation-duration', '0.2s');
                root.style.setProperty('--mobile-transition-duration', '0.15s');
                root.style.setProperty('--mobile-scroll-behavior', 'auto');
                break;
            case 'medium':
                root.style.setProperty('--mobile-animation-duration', '0.3s');
                root.style.setProperty('--mobile-transition-duration', '0.2s');
                root.style.setProperty('--mobile-scroll-behavior', 'smooth');
                break;
            case 'high':
                root.style.setProperty('--mobile-animation-duration', '0.4s');
                root.style.setProperty('--mobile-transition-duration', '0.3s');
                root.style.setProperty('--mobile-scroll-behavior', 'smooth');
                break;
        }
    }

    // 优化触摸交互
    optimizeTouchInteractions() {
        if (!this.touchSupported) return;
        
        // 移除300ms点击延迟
        this.removeTapDelay();
        
        // 优化触摸事件
        this.optimizeTouchEvents();
        
        // 添加触摸反馈
        this.addTouchFeedback();
        
        // 优化滑动手势
        this.optimizeSwipeGestures();
    }

    // 移除点击延迟
    removeTapDelay() {
        // 添加touch-action CSS属性
        const style = document.createElement('style');
        style.textContent = `
            * {
                touch-action: manipulation;
            }
            
            /* 确保按钮有足够的触摸目标 */
            button, .btn, a, input, select, textarea {
                min-height: 44px;
                min-width: 44px;
                touch-action: manipulation;
            }
            
            /* 优化滚动区域 */
            .scroll-container {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }
        `;
        document.head.appendChild(style);
    }

    // 优化触摸事件
    optimizeTouchEvents() {
        // 使用passive事件监听器
        const passiveEvents = ['touchstart', 'touchmove', 'touchend'];
        
        passiveEvents.forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                this.handleTouchEvent(e);
            }, { passive: true });
        });
        
        // 防止意外的触摸行为
        document.addEventListener('touchstart', (e) => {
            // 防止双指缩放（如果不需要）
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
    }

    // 处理触摸事件
    handleTouchEvent(event) {
        const touch = event.touches[0] || event.changedTouches[0];
        if (!touch) return;
        
        // 记录触摸位置用于手势识别
        if (event.type === 'touchstart') {
            this.touchStartX = touch.clientX;
            this.touchStartY = touch.clientY;
            this.touchStartTime = Date.now();
        }
        
        // 优化触摸移动
        if (event.type === 'touchmove') {
            this.optimizeTouchMove(event, touch);
        }
        
        // 处理触摸结束
        if (event.type === 'touchend') {
            this.handleTouchEnd(event, touch);
        }
    }

    // 优化触摸移动
    optimizeTouchMove(event, touch) {
        // 使用requestAnimationFrame优化触摸移动
        if (!this.touchMoveRAF) {
            this.touchMoveRAF = requestAnimationFrame(() => {
                // 处理触摸移动逻辑
                this.processTouchMove(touch);
                this.touchMoveRAF = null;
            });
        }
    }

    // 处理触摸移动
    processTouchMove(touch) {
        const deltaX = touch.clientX - this.touchStartX;
        const deltaY = touch.clientY - this.touchStartY;
        
        // 检测滑动方向
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            this.handleHorizontalSwipe(deltaX);
        } else {
            // 垂直滑动
            this.handleVerticalSwipe(deltaY);
        }
    }

    // 添加触摸反馈
    addTouchFeedback() {
        // 为可交互元素添加触摸反馈
        const interactiveElements = document.querySelectorAll('button, .btn, a, [role="button"]');
        
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.classList.add('touch-active');
            }, { passive: true });
            
            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            }, { passive: true });
        });
        
        // 添加触摸反馈样式
        const style = document.createElement('style');
        style.textContent = `
            .touch-active {
                transform: scale(0.98);
                opacity: 0.8;
                transition: transform 0.1s ease, opacity 0.1s ease;
            }
        `;
        document.head.appendChild(style);
    }

    // 优化滚动性能
    optimizeScrollPerformance() {
        if (this.scrollOptimized) return;
        
        // 使用Intersection Observer优化滚动
        this.setupScrollOptimization();
        
        // 优化滚动事件
        this.optimizeScrollEvents();
        
        // 添加滚动惯性
        this.addScrollMomentum();
        
        this.scrollOptimized = true;
    }

    // 设置滚动优化
    setupScrollOptimization() {
        // 为滚动容器添加优化属性
        const scrollContainers = document.querySelectorAll('.scroll-container, [data-scroll]');
        
        scrollContainers.forEach(container => {
            container.style.webkitOverflowScrolling = 'touch';
            container.style.overscrollBehavior = 'contain';
            container.style.scrollBehavior = this.performanceMode === 'low' ? 'auto' : 'smooth';
        });
    }

    // 优化滚动事件
    optimizeScrollEvents() {
        let scrollTimeout;
        let isScrolling = false;
        
        window.addEventListener('scroll', () => {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    this.handleOptimizedScroll();
                    isScrolling = false;
                });
            }
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.handleScrollEnd();
            }, 150);
        }, { passive: true });
    }

    // 处理优化的滚动
    handleOptimizedScroll() {
        // 在低性能模式下减少滚动处理
        if (this.performanceMode === 'low') {
            return;
        }
        
        // 处理滚动相关的UI更新
        this.updateScrollBasedUI();
    }

    // 更新基于滚动的UI
    updateScrollBasedUI() {
        const scrollY = window.pageYOffset;
        
        // 更新导航栏状态
        const header = document.querySelector('header');
        if (header) {
            if (scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
        
        // 更新返回顶部按钮
        const backToTop = document.querySelector('.back-to-top');
        if (backToTop) {
            if (scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }
    }

    // 优化电池使用
    optimizeBatteryUsage() {
        if (!this.batteryAPI) return;
        
        // 根据电池状态调整性能
        this.adjustPerformanceForBattery();
        
        // 监控电池状态变化
        this.batteryAPI.addEventListener('levelchange', () => {
            this.adjustPerformanceForBattery();
        });
        
        this.batteryAPI.addEventListener('chargingchange', () => {
            this.adjustPerformanceForBattery();
        });
    }

    // 监控电池状态
    monitorBatteryStatus() {
        if (!this.batteryAPI) return;
        
        const batteryLevel = this.batteryAPI.level;
        const isCharging = this.batteryAPI.charging;
        
        console.log('🔋 Battery status:', {
            level: (batteryLevel * 100).toFixed(0) + '%',
            charging: isCharging
        });
    }

    // 根据电池状态调整性能
    adjustPerformanceForBattery() {
        if (!this.batteryAPI) return;
        
        const batteryLevel = this.batteryAPI.level;
        const isCharging = this.batteryAPI.charging;
        
        // 低电量时启用省电模式
        if (batteryLevel < 0.2 && !isCharging) {
            this.enablePowerSaveMode();
        } else if (batteryLevel > 0.5 || isCharging) {
            this.disablePowerSaveMode();
        }
    }

    // 启用省电模式
    enablePowerSaveMode() {
        console.log('🔋 Enabling power save mode');
        
        // 减少动画
        document.body.classList.add('power-save-mode');
        
        // 降低刷新率
        this.reducedRefreshRate = true;
        
        // 暂停非关键动画
        this.pauseNonCriticalAnimations();
        
        // 减少网络请求
        this.reduceNetworkActivity();
    }

    // 禁用省电模式
    disablePowerSaveMode() {
        console.log('🔋 Disabling power save mode');
        
        document.body.classList.remove('power-save-mode');
        this.reducedRefreshRate = false;
        this.resumeNonCriticalAnimations();
    }

    // 优化网络使用
    optimizeNetworkUsage() {
        if (!this.networkInfo) return;
        
        this.monitorNetworkStatus();
        this.adjustForNetworkConditions();
    }

    // 监控网络状态
    monitorNetworkStatus() {
        if (!this.networkInfo) return;
        
        console.log('📶 Network status:', {
            effectiveType: this.networkInfo.effectiveType,
            downlink: this.networkInfo.downlink,
            rtt: this.networkInfo.rtt
        });
        
        this.networkInfo.addEventListener('change', () => {
            this.adjustForNetworkConditions();
        });
    }

    // 根据网络条件调整
    adjustForNetworkConditions() {
        if (!this.networkInfo) return;
        
        const effectiveType = this.networkInfo.effectiveType;
        
        if (effectiveType === 'slow-2g' || effectiveType === '2g') {
            this.enableDataSaveMode();
        } else if (effectiveType === '4g') {
            this.disableDataSaveMode();
        }
    }

    // 启用数据节省模式
    enableDataSaveMode() {
        console.log('📶 Enabling data save mode');
        
        // 延迟加载非关键资源
        this.deferNonCriticalResources();
        
        // 降低图片质量
        this.reduceImageQuality();
        
        // 禁用自动播放
        this.disableAutoplay();
    }

    // 设置视口优化
    setupViewportOptimization() {
        // 确保视口设置正确
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
        
        // 防止iOS Safari的视口缩放问题
        this.preventViewportZoomIssues();
    }

    // 防止视口缩放问题
    preventViewportZoomIssues() {
        // 防止iOS Safari在输入焦点时缩放
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                input.style.fontSize = '16px';
            }
        });
    }

    // 优化输入
    optimizeInputs() {
        const inputs = document.querySelectorAll('input, textarea');
        
        inputs.forEach(input => {
            // 添加适当的输入类型
            this.optimizeInputType(input);
            
            // 优化虚拟键盘
            this.optimizeVirtualKeyboard(input);
            
            // 添加输入验证优化
            this.optimizeInputValidation(input);
        });
    }

    // 优化输入类型
    optimizeInputType(input) {
        // 根据输入内容设置合适的类型
        if (input.name && input.name.includes('email')) {
            input.type = 'email';
        } else if (input.name && input.name.includes('tel')) {
            input.type = 'tel';
        } else if (input.name && input.name.includes('url')) {
            input.type = 'url';
        }
    }

    // 优化虚拟键盘
    optimizeVirtualKeyboard(input) {
        // 设置合适的inputmode
        if (input.type === 'email') {
            input.inputMode = 'email';
        } else if (input.type === 'tel') {
            input.inputMode = 'tel';
        } else if (input.type === 'number') {
            input.inputMode = 'numeric';
        }
        
        // 设置autocomplete
        if (!input.autocomplete) {
            if (input.type === 'email') {
                input.autocomplete = 'email';
            } else if (input.type === 'tel') {
                input.autocomplete = 'tel';
            }
        }
    }

    // 获取性能报告
    getPerformanceReport() {
        return {
            isMobile: this.isMobile,
            performanceMode: this.performanceMode,
            touchSupported: this.touchSupported,
            batteryLevel: this.batteryAPI ? (this.batteryAPI.level * 100).toFixed(0) + '%' : 'unknown',
            networkType: this.networkInfo ? this.networkInfo.effectiveType : 'unknown',
            powerSaveMode: document.body.classList.contains('power-save-mode'),
            scrollOptimized: this.scrollOptimized
        };
    }

    // 清理资源
    cleanup() {
        // 移除事件监听器
        if (this.batteryAPI) {
            this.batteryAPI.removeEventListener('levelchange', this.adjustPerformanceForBattery);
            this.batteryAPI.removeEventListener('chargingchange', this.adjustPerformanceForBattery);
        }
        
        if (this.networkInfo) {
            this.networkInfo.removeEventListener('change', this.adjustForNetworkConditions);
        }
    }
}

// 导出单例
export const mobileOptimizer = new MobileOptimizer();
export default MobileOptimizer;
