/**
 * 简化的CSS加载器
 * 保持原有功能的同时优化加载性能
 */

class SimpleCSSLoader {
    constructor() {
        this.loadedStyles = new Set();
        this.init();
    }

    init() {
        // 检测用户交互，延迟加载非关键CSS
        this.setupInteractionLoading();
    }

    setupInteractionLoading() {
        let interactionOccurred = false;
        
        const loadNonCriticalCSS = () => {
            if (interactionOccurred) return;
            interactionOccurred = true;
            
            // 加载非关键CSS
            this.loadCSS('styles/animations.css');
            this.loadCSS('styles/components-extended.css');
            
            // 移除事件监听器
            ['mousedown', 'touchstart', 'keydown', 'scroll'].forEach(event => {
                document.removeEventListener(event, loadNonCriticalCSS, { passive: true });
            });
        };

        // 添加事件监听器
        ['mousedown', 'touchstart', 'keydown', 'scroll'].forEach(event => {
            document.addEventListener(event, loadNonCriticalCSS, { passive: true });
        });

        // 3秒后自动加载（fallback）
        setTimeout(() => {
            if (!interactionOccurred) {
                loadNonCriticalCSS();
            }
        }, 3000);
    }

    loadCSS(href) {
        if (this.loadedStyles.has(href)) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = () => {
                this.loadedStyles.add(href);
                resolve();
            };
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    preloadCSS(href) {
        if (this.loadedStyles.has(href)) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
            this.loadedStyles.add(href);
        };
        document.head.appendChild(link);
    }
}

// 全局实例
window.cssLoader = new SimpleCSSLoader();
