// AIGC降重页面状态
let aigcState = {
    isProcessing: false,
    currentTab: 'text',
    inputText: '',
    outputText: '',
    rewriteLevel: 'medium',
    uploadedFile: null,
    progress: 0
};

// 降重级别配置
const REWRITE_LEVELS = [
    {
        value: 'light',
        label: '轻度降重',
        desc: '保持原意，微调表达',
        color: 'from-green-500 to-green-600',
        rate: '30-50%',
        icon: 'feather'
    },
    {
        value: 'medium',
        label: '中度降重',
        desc: '适度改写，平衡质量与降重效果',
        color: 'from-blue-500 to-blue-600',
        rate: '50-70%',
        icon: 'edit-3'
    },
    {
        value: 'deep',
        label: '深度降重',
        desc: '大幅改写，最大化降重效果',
        color: 'from-purple-500 to-purple-600',
        rate: '70-90%',
        icon: 'zap'
    }
];

// DOM 元素
let aigcElements = {};

// 初始化AIGC页面
document.addEventListener('DOMContentLoaded', function() {
    initializeAigcElements();
    initializeLucideIcons();
    renderRewriteLevels();
    bindAigcEvents();
    loadSavedAigcData();
});

// 初始化DOM元素引用
function initializeAigcElements() {
    aigcElements = {
        inputText: document.getElementById('input-text'),
        outputText: document.getElementById('output-text'),
        inputCharCount: document.getElementById('input-char-count'),
        outputCharCount: document.getElementById('output-char-count'),
        estimatedTime: document.getElementById('estimated-time'),
        reductionRate: document.getElementById('reduction-rate'),
        rewriteBtn: document.getElementById('rewrite-btn'),
        copyBtn: document.getElementById('copy-btn'),
        progressContainer: document.getElementById('progress-container'),
        progressFill: document.getElementById('progress-fill'),
        progressText: document.getElementById('progress-text'),
        progressDetail: document.getElementById('progress-detail'),
        outputActions: document.getElementById('output-actions'),
        tabButtons: document.querySelectorAll('.tab-button'),
        tabContents: document.querySelectorAll('.tab-content'),
        rewriteLevelsContainer: document.getElementById('rewrite-levels'),
        fileInput: document.getElementById('file-input'),
        uploadArea: document.getElementById('upload-area'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn')
    };
}

// 渲染降重级别选择器
function renderRewriteLevels() {
    if (!aigcElements.rewriteLevelsContainer) return;
    
    const html = REWRITE_LEVELS.map(level => `
        <div class="rewrite-level-card card cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 border-0 ${
            aigcState.rewriteLevel === level.value
                ? 'ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg active'
                : 'bg-gradient-to-br from-white to-gray-50 hover:from-gray-50 hover:to-gray-100'
        }" 
             data-level="${level.value}" 
             onclick="selectRewriteLevel('${level.value}')">
            <div class="p-5">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-bold text-gray-900">${level.label}</h4>
                    <div class="w-8 h-8 bg-gradient-to-r ${level.color} rounded-lg flex items-center justify-center">
                        <i data-lucide="${level.icon}" class="w-4 h-4 text-white"></i>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mb-3">${level.desc}</p>
                <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-500">降重率</span>
                    <span class="text-sm font-bold text-blue-600">${level.rate}</span>
                </div>
            </div>
        </div>
    `).join('');
    
    aigcElements.rewriteLevelsContainer.innerHTML = html;
    initializeLucideIcons();
}

// 选择降重级别
function selectRewriteLevel(level) {
    aigcState.rewriteLevel = level;
    
    // 更新UI
    document.querySelectorAll('.rewrite-level-card').forEach(card => {
        const isSelected = card.dataset.level === level;
        
        if (isSelected) {
            card.classList.add('active', 'ring-2', 'ring-blue-500', 'bg-gradient-to-br', 'from-blue-50', 'to-blue-100', 'shadow-lg');
            card.classList.remove('bg-gradient-to-br', 'from-white', 'to-gray-50');
        } else {
            card.classList.remove('active', 'ring-2', 'ring-blue-500', 'from-blue-50', 'to-blue-100', 'shadow-lg');
            card.classList.add('bg-gradient-to-br', 'from-white', 'to-gray-50');
        }
    });
    
    // 保存状态
    saveAigcData();
}

// 绑定AIGC页面事件
function bindAigcEvents() {
    // 文本输入事件
    if (aigcElements.inputText) {
        aigcElements.inputText.addEventListener('input', handleTextInput);
    }
    
    // 标签页切换
    aigcElements.tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 文件拖拽上传
    if (aigcElements.uploadArea) {
        aigcElements.uploadArea.addEventListener('dragover', handleDragOver);
        aigcElements.uploadArea.addEventListener('drop', handleDrop);
        aigcElements.uploadArea.addEventListener('dragleave', handleDragLeave);
    }
    
    // 移动端菜单
    if (aigcElements.mobileMenuBtn) {
        aigcElements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            aigcElements.sidebar && 
            !aigcElements.sidebar.contains(e.target) && 
            aigcElements.mobileMenuBtn &&
            !aigcElements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 处理文本输入
function handleTextInput() {
    const text = aigcElements.inputText.value;
    aigcState.inputText = text;
    
    // 更新字数统计
    updateCharCount();
    
    // 更新按钮状态
    updateRewriteButtonState();
    
    // 保存状态
    saveAigcData();
}

// 更新字符计数
function updateCharCount() {
    const inputLength = aigcState.inputText.length;
    const outputLength = aigcState.outputText.length;
    
    if (aigcElements.inputCharCount) {
        aigcElements.inputCharCount.textContent = `字数: ${inputLength}/5000`;
        aigcElements.inputCharCount.className = inputLength > 5000 ? 'text-red-500' : '';
    }
    
    if (aigcElements.outputCharCount) {
        aigcElements.outputCharCount.textContent = `字数: ${outputLength}`;
    }
    
    // 显示预计处理时间
    if (inputLength > 0 && aigcElements.estimatedTime) {
        const estimatedSeconds = Math.ceil(inputLength / 1000);
        aigcElements.estimatedTime.textContent = `预计处理时间: ${estimatedSeconds}秒`;
        aigcElements.estimatedTime.classList.remove('hidden');
    } else if (aigcElements.estimatedTime) {
        aigcElements.estimatedTime.classList.add('hidden');
    }
}

// 更新降重按钮状态
function updateRewriteButtonState() {
    const hasContent = aigcState.currentTab === 'text' 
        ? aigcState.inputText.trim().length > 0 && aigcState.inputText.length <= 5000
        : aigcState.uploadedFile !== null;
    
    if (aigcElements.rewriteBtn) {
        aigcElements.rewriteBtn.disabled = !hasContent || aigcState.isProcessing;
    }
}

// 切换标签页
function switchTab(tab) {
    aigcState.currentTab = tab;
    
    // 更新标签页样式
    aigcElements.tabButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('border-cyan-500', 'text-cyan-600', 'active');
            button.classList.remove('border-transparent', 'text-gray-500');
        } else {
            button.classList.remove('border-cyan-500', 'text-cyan-600', 'active');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });
    
    // 切换内容区域
    aigcElements.tabContents.forEach(content => {
        if (content.id === `${tab}-tab`) {
            content.classList.remove('hidden');
        } else {
            content.classList.add('hidden');
        }
    });
    
    updateRewriteButtonState();
}

// 开始降重处理
async function startRewrite() {
    if (aigcState.isProcessing) return;
    
    // 验证输入
    if (aigcState.currentTab === 'text' && !aigcState.inputText.trim()) {
        showToast('请输入要降重的文本内容', 'error');
        return;
    }
    
    if (aigcState.currentTab === 'file' && !aigcState.uploadedFile) {
        showToast('请选择要降重的文件', 'error');
        return;
    }
    
    aigcState.isProcessing = true;
    
    // 更新UI状态
    updateProcessingUI(true);
    
    try {
        // 模拟降重过程
        await simulateRewriteProcess();
        
        // 生成降重结果
        const result = generateRewriteResult();
        aigcState.outputText = result;
        
        // 显示结果
        displayRewriteResult(result);
        
        showToast('智能降重完成', 'success');
        
    } catch (error) {
        showToast('降重处理失败，请重试', 'error');
    } finally {
        aigcState.isProcessing = false;
        updateProcessingUI(false);
    }
}

// 模拟降重过程
async function simulateRewriteProcess() {
    const steps = [
        { text: '正在分析文本结构...', progress: 15 },
        { text: '正在识别关键词...', progress: 30 },
        { text: '正在查找同义词...', progress: 50 },
        { text: '正在重构句式...', progress: 70 },
        { text: '正在优化语义...', progress: 85 },
        { text: '正在生成最终结果...', progress: 100 }
    ];
    
    for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 500));
        updateProgress(step.text, step.progress);
    }
}

// 更新进度
function updateProgress(text, progress) {
    if (aigcElements.progressText) {
        aigcElements.progressText.textContent = text;
    }
    if (aigcElements.progressFill) {
        aigcElements.progressFill.style.width = `${progress}%`;
    }
    if (aigcElements.progressDetail) {
        aigcElements.progressDetail.textContent = text;
    }
    
    aigcState.progress = progress;
}

// 更新处理中的UI状态
function updateProcessingUI(isProcessing) {
    if (aigcElements.rewriteBtn) {
        aigcElements.rewriteBtn.disabled = isProcessing;
        aigcElements.rewriteBtn.innerHTML = isProcessing 
            ? '<div class="spinner mr-2"></div>AI处理中...'
            : '<i data-lucide="zap" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>开始智能降重';
    }
    
    if (aigcElements.progressContainer) {
        if (isProcessing) {
            aigcElements.progressContainer.classList.remove('hidden');
        } else {
            aigcElements.progressContainer.classList.add('hidden');
        }
    }
    
    if (!isProcessing) {
        initializeLucideIcons();
    }
}

// 生成降重结果
function generateRewriteResult() {
    const text = aigcState.inputText;
    const level = aigcState.rewriteLevel;
    
    // 简单的同义词替换示例
    let result = text
        .replace(/研究/g, '探索')
        .replace(/分析/g, '剖析')
        .replace(/方法/g, '途径')
        .replace(/结果/g, '成果')
        .replace(/发现/g, '发觉')
        .replace(/表明/g, '显示')
        .replace(/重要/g, '关键')
        .replace(/显著/g, '明显')
        .replace(/提出/g, '提议')
        .replace(/建立/g, '构建');
    
    // 根据降重级别调整
    if (level === 'deep') {
        result = result
            .replace(/通过/g, '借助')
            .replace(/基于/g, '依据')
            .replace(/利用/g, '运用')
            .replace(/采用/g, '使用')
            .replace(/实现/g, '达成');
    }
    
    // 添加处理信息
    const levelInfo = REWRITE_LEVELS.find(l => l.value === level);
    result += `\n\n[✅ 已通过AIGC智能降重处理]\n[📊 降重级别: ${levelInfo.label}]\n[🎯 预计降重率: ${levelInfo.rate}]\n[🔍 语义保持度: 95%+]\n[⚡ 处理时间: ${Math.ceil(text.length / 1000)}秒]`;
    
    return result;
}

// 显示降重结果
function displayRewriteResult(result) {
    if (aigcElements.outputText) {
        aigcElements.outputText.value = result;
    }
    
    // 更新字符计数
    updateCharCount();
    
    // 显示降重率
    if (aigcElements.reductionRate) {
        const levelInfo = REWRITE_LEVELS.find(l => l.value === aigcState.rewriteLevel);
        aigcElements.reductionRate.textContent = `预计降重率: ${levelInfo.rate}`;
        aigcElements.reductionRate.classList.remove('hidden');
    }
    
    // 显示操作按钮
    if (aigcElements.outputActions) {
        aigcElements.outputActions.style.display = 'flex';
    }
}

// 复制结果
async function copyResult() {
    if (!aigcState.outputText) return;
    
    try {
        await navigator.clipboard.writeText(aigcState.outputText);
        
        // 更新按钮状态
        if (aigcElements.copyBtn) {
            const originalHTML = aigcElements.copyBtn.innerHTML;
            aigcElements.copyBtn.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-1 text-green-600"></i>已复制';
            aigcElements.copyBtn.classList.add('bg-green-50', 'border-green-200');
            
            setTimeout(() => {
                aigcElements.copyBtn.innerHTML = originalHTML;
                aigcElements.copyBtn.classList.remove('bg-green-50', 'border-green-200');
                initializeLucideIcons();
            }, 2000);
        }
        
        showToast('结果已复制到剪贴板', 'success');
    } catch (error) {
        showToast('复制失败，请手动复制', 'error');
    }
}

// 重新生成结果
function regenerateResult() {
    if (!aigcState.inputText.trim()) return;
    startRewrite();
}

// 下载结果
function downloadResult() {
    if (!aigcState.outputText) return;
    
    const blob = new Blob([aigcState.outputText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    link.href = url;
    link.download = `降重结果_${new Date().toISOString().slice(0, 10)}.txt`;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    showToast('文件下载成功', 'success');
}

// 清空输入
function clearInput() {
    if (confirm('确定要清空输入内容吗？')) {
        aigcState.inputText = '';
        if (aigcElements.inputText) {
            aigcElements.inputText.value = '';
        }
        updateCharCount();
        updateRewriteButtonState();
        saveAigcData();
    }
}

// 文件处理相关函数
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    aigcElements.uploadArea.classList.add('border-cyan-500', 'bg-cyan-100');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    aigcElements.uploadArea.classList.remove('border-cyan-500', 'bg-cyan-100');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    aigcElements.uploadArea.classList.remove('border-cyan-500', 'bg-cyan-100');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelection(files[0]);
    }
}

function handleFileUpload(input) {
    if (input.files && input.files[0]) {
        handleFileSelection(input.files[0]);
    }
}

function handleFileSelection(file) {
    // 验证文件类型
    const allowedTypes = ['.doc', '.docx', '.pdf', '.txt'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showToast('不支持的文件格式，请上传 Word、PDF 或文本文件', 'error');
        return;
    }
    
    // 验证文件大小
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showToast('文件大小超过限制，请上传小于10MB的文件', 'error');
        return;
    }
    
    aigcState.uploadedFile = file;
    updateUploadAreaUI(file);
    updateRewriteButtonState();
}

function updateUploadAreaUI(file) {
    const uploadArea = aigcElements.uploadArea;
    if (!uploadArea) return;
    
    uploadArea.innerHTML = `
        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <i data-lucide="file-check" class="w-8 h-8 text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-gray-900 mb-2">文件已选择</h3>
        <p class="text-gray-600 mb-2">${file.name}</p>
        <p class="text-sm text-gray-500 mb-4">大小: ${formatFileSize(file.size)}</p>
        <button class="btn btn-secondary" onclick="clearUploadedFile()">
            <i data-lucide="x" class="w-4 h-4 mr-2"></i>
            重新选择
        </button>
    `;
    
    initializeLucideIcons();
}

function clearUploadedFile() {
    aigcState.uploadedFile = null;
    
    // 重置上传区域UI
    const uploadArea = aigcElements.uploadArea;
    if (uploadArea) {
        uploadArea.innerHTML = `
            <div class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <i data-lucide="upload" class="w-8 h-8 sm:w-10 sm:h-10 text-white"></i>
            </div>
            <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-2">上传文档文件</h3>
            <p class="text-gray-600 mb-2">拖拽文件到此处或点击上传</p>
            <p class="text-sm text-gray-500 mb-4 sm:mb-6">支持 Word(.doc, .docx)、PDF(.pdf)、文本(.txt) 格式，最大10MB</p>
            
            <button class="btn btn-primary btn-lg group" onclick="document.getElementById('file-input').click()">
                <i data-lucide="upload" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                选择文件上传
            </button>
        `;
        initializeLucideIcons();
    }
    
    // 重置文件输入
    if (aigcElements.fileInput) {
        aigcElements.fileInput.value = '';
    }
    
    updateRewriteButtonState();
}

// 数据持久化
function saveAigcData() {
    const data = {
        inputText: aigcState.inputText,
        rewriteLevel: aigcState.rewriteLevel,
        currentTab: aigcState.currentTab
    };
    
    try {
        localStorage.setItem('aigcData', JSON.stringify(data));
    } catch (error) {
        console.error('Failed to save AIGC data:', error);
    }
}

function loadSavedAigcData() {
    try {
        const savedData = localStorage.getItem('aigcData');
        if (savedData) {
            const data = JSON.parse(savedData);
            
            if (data.inputText && aigcElements.inputText) {
                aigcState.inputText = data.inputText;
                aigcElements.inputText.value = data.inputText;
                updateCharCount();
                updateRewriteButtonState();
            }
            
            if (data.rewriteLevel) {
                selectRewriteLevel(data.rewriteLevel);
            }
            
            if (data.currentTab) {
                switchTab(data.currentTab);
            }
        }
    } catch (error) {
        console.error('Failed to load AIGC data:', error);
    }
}

// 移动端菜单控制
function toggleMobileMenu() {
    if (aigcElements.sidebar) {
        aigcElements.sidebar.classList.toggle('-translate-x-full');
    }
}

function closeMobileMenu() {
    if (aigcElements.sidebar) {
        aigcElements.sidebar.classList.add('-translate-x-full');
    }
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
