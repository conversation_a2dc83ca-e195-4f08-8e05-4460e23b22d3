/**
 * CSS加载管理器
 * 实现CSS的按需加载和优化
 */

export class CSSLoader {
    constructor() {
        this.loadedStyles = new Set();
        this.loadingPromises = new Map();
        this.criticalCSS = null;
        
        this.init();
    }

    init() {
        this.inlineCriticalCSS();
        this.setupLazyLoading();
    }

    // 内联关键CSS
    async inlineCriticalCSS() {
        try {
            // 如果关键CSS已经内联，跳过
            if (document.querySelector('style[data-critical]')) {
                return;
            }

            const response = await fetch('/styles/critical.css');
            const css = await response.text();
            
            const style = document.createElement('style');
            style.setAttribute('data-critical', 'true');
            style.textContent = css;
            
            // 插入到head的最前面
            document.head.insertBefore(style, document.head.firstChild);
            
            this.criticalCSS = css;
            this.loadedStyles.add('critical');
            
        } catch (error) {
            console.error('Failed to load critical CSS:', error);
        }
    }

    // 设置懒加载
    setupLazyLoading() {
        // 监听页面交互，加载组件样式
        this.setupInteractionLoading();
        
        // 监听路由变化，加载页面特定样式
        this.setupRouteBasedLoading();
        
        // 监听组件出现，加载相关样式
        this.setupComponentBasedLoading();
    }

    // 基于交互的加载
    setupInteractionLoading() {
        let interactionOccurred = false;
        
        const loadOnInteraction = () => {
            if (interactionOccurred) return;
            interactionOccurred = true;
            
            // 用户首次交互时加载组件样式
            this.loadCSS('components');
            
            // 移除事件监听器
            ['mousedown', 'touchstart', 'keydown', 'scroll'].forEach(event => {
                document.removeEventListener(event, loadOnInteraction, { passive: true });
            });
        };

        // 添加事件监听器
        ['mousedown', 'touchstart', 'keydown', 'scroll'].forEach(event => {
            document.addEventListener(event, loadOnInteraction, { passive: true });
        });

        // 3秒后自动加载（fallback）
        setTimeout(() => {
            if (!interactionOccurred) {
                loadOnInteraction();
            }
        }, 3000);
    }

    // 基于路由的加载
    setupRouteBasedLoading() {
        const currentPage = this.getCurrentPage();
        
        // 根据当前页面加载特定样式
        switch (currentPage) {
            case 'history':
                this.loadCSS('history');
                break;
            case 'plagiarism':
                this.loadCSS('plagiarism');
                break;
            case 'billing':
                this.loadCSS('billing');
                break;
            case 'profile':
                this.loadCSS('profile');
                break;
            default:
                this.loadCSS('home');
        }
    }

    // 基于组件的加载
    setupComponentBasedLoading() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const component = entry.target.dataset.component;
                    if (component) {
                        this.loadComponentCSS(component);
                        observer.unobserve(entry.target);
                    }
                }
            });
        }, { threshold: 0.1 });

        // 观察所有带有data-component属性的元素
        document.querySelectorAll('[data-component]').forEach(element => {
            observer.observe(element);
        });
    }

    // 加载CSS文件
    async loadCSS(name, priority = 'normal') {
        if (this.loadedStyles.has(name)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(name)) {
            return this.loadingPromises.get(name);
        }

        const promise = this.loadCSSFile(name, priority);
        this.loadingPromises.set(name, promise);

        try {
            await promise;
            this.loadedStyles.add(name);
            this.loadingPromises.delete(name);
        } catch (error) {
            this.loadingPromises.delete(name);
            throw error;
        }

        return promise;
    }

    // 加载CSS文件的具体实现
    loadCSSFile(name, priority) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = this.getCSSPath(name);
            link.media = priority === 'low' ? 'print' : 'all';
            
            // 设置优先级
            if (priority === 'high') {
                link.setAttribute('data-priority', 'high');
            }

            link.onload = () => {
                // 如果是低优先级，加载完成后改为all
                if (link.media === 'print') {
                    link.media = 'all';
                }
                resolve();
            };

            link.onerror = () => {
                reject(new Error(`Failed to load CSS: ${name}`));
            };

            // 插入到合适的位置
            this.insertCSS(link, priority);
        });
    }

    // 获取CSS文件路径
    getCSSPath(name) {
        const cssMap = {
            critical: '/styles/critical.css',
            components: '/styles/components.css',
            animations: '/styles/animations.css',
            utilities: '/styles/utilities.css',
            home: '/styles/pages/home.css',
            history: '/styles/pages/history.css',
            plagiarism: '/styles/pages/plagiarism.css',
            billing: '/styles/pages/billing.css',
            profile: '/styles/pages/profile.css'
        };

        return cssMap[name] || `/styles/${name}.css`;
    }

    // 插入CSS到合适位置
    insertCSS(link, priority) {
        const head = document.head;
        
        if (priority === 'high') {
            // 高优先级插入到关键CSS之后
            const criticalStyle = document.querySelector('style[data-critical]');
            if (criticalStyle) {
                head.insertBefore(link, criticalStyle.nextSibling);
            } else {
                head.appendChild(link);
            }
        } else {
            // 普通优先级插入到末尾
            head.appendChild(link);
        }
    }

    // 加载组件特定CSS
    async loadComponentCSS(componentName) {
        const componentCSSMap = {
            'essay-form': 'forms',
            'data-table': 'tables',
            'chart': 'charts',
            'modal': 'modals',
            'dropdown': 'dropdowns'
        };

        const cssName = componentCSSMap[componentName] || componentName;
        return this.loadCSS(cssName);
    }

    // 预加载CSS
    preloadCSS(name) {
        if (this.loadedStyles.has(name)) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = this.getCSSPath(name);
        
        document.head.appendChild(link);
    }

    // 获取当前页面
    getCurrentPage() {
        const path = window.location.pathname;
        const page = path.split('/').pop().replace('.html', '') || 'index';
        return page === 'index' ? 'home' : page;
    }

    // 移除CSS
    removeCSS(name) {
        const link = document.querySelector(`link[href="${this.getCSSPath(name)}"]`);
        if (link) {
            link.remove();
            this.loadedStyles.delete(name);
        }
    }

    // 获取已加载的CSS列表
    getLoadedCSS() {
        return Array.from(this.loadedStyles);
    }

    // 获取CSS加载状态
    getLoadingStatus() {
        return {
            loaded: this.getLoadedCSS(),
            loading: Array.from(this.loadingPromises.keys()),
            total: this.loadedStyles.size + this.loadingPromises.size
        };
    }

    // 清理未使用的CSS
    cleanupUnusedCSS() {
        const currentPage = this.getCurrentPage();
        const pageSpecificCSS = ['home', 'history', 'plagiarism', 'billing', 'profile'];
        
        pageSpecificCSS.forEach(page => {
            if (page !== currentPage && this.loadedStyles.has(page)) {
                this.removeCSS(page);
            }
        });
    }

    // 优化CSS加载顺序
    optimizeLoadOrder() {
        // 根据页面内容优化加载顺序
        const visibleComponents = this.getVisibleComponents();
        const loadOrder = this.calculateOptimalLoadOrder(visibleComponents);
        
        loadOrder.forEach((cssName, index) => {
            setTimeout(() => {
                this.loadCSS(cssName, 'low');
            }, index * 100); // 错开加载时间
        });
    }

    // 获取可见组件
    getVisibleComponents() {
        const components = [];
        const viewportHeight = window.innerHeight;
        
        document.querySelectorAll('[data-component]').forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.top < viewportHeight && rect.bottom > 0) {
                components.push(element.dataset.component);
            }
        });
        
        return components;
    }

    // 计算最优加载顺序
    calculateOptimalLoadOrder(visibleComponents) {
        // 基于组件重要性和可见性排序
        const priorityMap = {
            'essay-form': 1,
            'navigation': 2,
            'data-table': 3,
            'chart': 4,
            'modal': 5
        };

        return visibleComponents.sort((a, b) => {
            const priorityA = priorityMap[a] || 10;
            const priorityB = priorityMap[b] || 10;
            return priorityA - priorityB;
        });
    }
}

// 全局CSS加载器实例
export const cssLoader = new CSSLoader();

// 导出便捷方法
export const loadCSS = (name, priority) => cssLoader.loadCSS(name, priority);
export const preloadCSS = (name) => cssLoader.preloadCSS(name);
export const removeCSS = (name) => cssLoader.removeCSS(name);
