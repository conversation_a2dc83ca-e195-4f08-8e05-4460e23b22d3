<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI论文写作平台 - 简化版</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <style>
        /* 确保所有内容都可见 */
        * {
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .essay-type-card h3 {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
            color: #111827 !important;
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
        }
        
        .essay-type-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .essay-type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        /* 悬停颜色 */
        .essay-type-card[data-type="literature"]:hover h3 { color: #dc2626 !important; }
        .essay-type-card[data-type="proposal"]:hover h3 { color: #2563eb !important; }
        .essay-type-card[data-type="task"]:hover h3 { color: #0891b2 !important; }
        .essay-type-card[data-type="journal"]:hover h3 { color: #16a34a !important; }
        .essay-type-card[data-type="bachelor"]:hover h3 { color: #65a30d !important; }
        .essay-type-card[data-type="term"]:hover h3 { color: #9333ea !important; }
        .essay-type-card[data-type="course"]:hover h3 { color: #4f46e5 !important; }
        .essay-type-card[data-type="practice"]:hover h3 { color: #0d9488 !important; }
        .essay-type-card[data-type="business"]:hover h3 { color: #2563eb !important; }
        .essay-type-card[data-type="research"]:hover h3 { color: #059669 !important; }
        .essay-type-card[data-type="professional"]:hover h3 { color: #db2777 !important; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <!-- Page Title Section -->
                <div class="mb-8 lg:mb-12">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                AI论文写作平台
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                选择您需要的论文类型，AI将为您智能生成专业、原创的学术内容
                                <span class="inline-block ml-2 text-blue-600">✨</span>
                            </p>

                            <!-- 特色标签 -->
                            <div class="flex flex-wrap gap-2 mt-4">
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
                                    🚀 3秒生成大纲
                                </span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                    ✅ 100%原创保证
                                </span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                    🔄 无限次修改
                                </span>
                                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full font-medium">
                                    🛡️ 专业保障
                                </span>
                            </div>

                            <!-- CTA按钮 -->
                            <div class="flex flex-col sm:flex-row gap-4 mt-6">
                                <button class="btn btn-primary btn-lg group px-6 py-3 text-base font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" onclick="scrollToEssayTypes()">
                                    <i data-lucide="edit-3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    开始创作
                                    <i data-lucide="arrow-right" class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"></i>
                                </button>
                                <button class="btn btn-outline btn-lg px-6 py-3 text-base font-semibold border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 transition-all duration-300" onclick="showDemoModal()">
                                    <i data-lucide="play-circle" class="w-5 h-5 mr-2"></i>
                                    观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Essay Types Section -->
                <section id="essay-types" class="mb-12">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">选择论文类型</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">我们提供多种类型的学术论文写作服务，每种类型都有专业的AI模型支持</p>
                    </div>

                    <!-- Desktop Grid -->
                    <div class="hidden lg:grid grid-cols-4 gap-6 essay-types-grid">
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="literature" onclick="selectEssayType('literature')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600">
                                    <i data-lucide="book-open" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>文献综述</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="proposal" onclick="selectEssayType('proposal')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600">
                                    <i data-lucide="lightbulb" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>开题报告</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="task" onclick="selectEssayType('task')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-cyan-500 to-cyan-600">
                                    <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>任务书</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="journal" onclick="selectEssayType('journal')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-green-500 to-green-600">
                                    <i data-lucide="newspaper" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>期刊论文</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="bachelor" onclick="selectEssayType('bachelor')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-lime-500 to-lime-600">
                                    <i data-lucide="graduation-cap" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>本科论文</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="term" onclick="selectEssayType('term')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-purple-500 to-purple-600">
                                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>学期论文</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="course" onclick="selectEssayType('course')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-indigo-500 to-indigo-600">
                                    <i data-lucide="book" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>课程作业</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="practice" onclick="selectEssayType('practice')">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-teal-500 to-teal-600">
                                    <i data-lucide="settings" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>实习报告</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // 简化的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化组件
            if (typeof UIComponents !== 'undefined') {
                UIComponents.initializePage('index');
            }
            
            // 初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            
            // 确保所有内容可见
            setTimeout(() => {
                document.querySelectorAll('.essay-type-card h3').forEach(h3 => {
                    h3.style.opacity = '1';
                    h3.style.visibility = 'visible';
                    h3.style.display = 'block';
                    h3.style.color = '#111827';
                });
                console.log('✅ 简化版页面加载完成');
            }, 500);
        });
        
        // 基础函数
        function selectEssayType(type) {
            console.log('选择论文类型:', type);
            alert('选择了: ' + type);
        }
        
        function scrollToEssayTypes() {
            document.getElementById('essay-types').scrollIntoView({ behavior: 'smooth' });
        }
        
        function showDemoModal() {
            alert('演示功能');
        }
    </script>
</body>
</html>
