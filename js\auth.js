// 用户认证系统 - 登录和注册功能

class AuthSystem {
    constructor() {
        this.isLoggedIn = false;
        this.currentUser = null;
        this.init();
    }

    // 初始化认证系统
    init() {
        // 检查token是否过期
        this.checkTokenExpiry();

        // 检查本地存储的登录状态
        const token = localStorage.getItem('userToken');
        const userInfo = localStorage.getItem('userInfo');

        if (token && userInfo) {
            this.isLoggedIn = true;
            this.currentUser = JSON.parse(userInfo);
            this.updateUIForLoggedInUser();

            // 设置定期检查token过期
            this.startTokenExpiryCheck();
        } else {
            this.updateUIForGuestUser();
        }

        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === 'userToken' || e.key === 'userInfo') {
                this.init();
            }
        });
    }

    // 检查token是否过期
    checkTokenExpiry() {
        const tokenExpiry = localStorage.getItem('tokenExpiry');
        if (tokenExpiry && Date.now() > parseInt(tokenExpiry)) {
            // Token已过期，清除登录状态
            this.logout(false); // 静默退出，不显示确认框
            showToast('登录已过期，请重新登录', 'warning');
        }
    }

    // 开始定期检查token过期
    startTokenExpiryCheck() {
        // 每5分钟检查一次
        this.tokenCheckInterval = setInterval(() => {
            this.checkTokenExpiry();
        }, 5 * 60 * 1000);
    }

    // 停止token过期检查
    stopTokenExpiryCheck() {
        if (this.tokenCheckInterval) {
            clearInterval(this.tokenCheckInterval);
            this.tokenCheckInterval = null;
        }
    }

    // 更新已登录用户的UI
    updateUIForLoggedInUser() {
        // 更新用户头像和信息
        this.updateUserAvatar();

        // 隐藏登录注册按钮，显示用户菜单
        this.updateAuthButtons();
    }

    // 更新访客用户的UI
    updateUIForGuestUser() {
        // 显示默认头像
        this.updateUserAvatar();

        // 显示登录注册按钮
        this.updateAuthButtons();
    }

    // 更新用户头像
    updateUserAvatar() {
        const userButtons = document.querySelectorAll('[onclick="showUserMenu()"]');
        userButtons.forEach(button => {
            if (this.isLoggedIn && this.currentUser) {
                // 显示用户信息
                button.title = `${this.currentUser.username} (${this.currentUser.email})`;

                // 如果有头像，显示头像
                if (this.currentUser.avatar) {
                    button.innerHTML = `<img src="${this.currentUser.avatar}" alt="用户头像" class="w-full h-full rounded-full object-cover">`;
                } else {
                    // 显示用户名首字母
                    const initial = this.currentUser.username ? this.currentUser.username.charAt(0).toUpperCase() : 'U';
                    button.innerHTML = `<span class="text-white font-bold">${initial}</span>`;
                }
            } else {
                // 显示默认图标
                button.innerHTML = '<i data-lucide="user" class="w-4 h-4 text-white"></i>';
                button.title = '点击登录';
            }
        });

        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 更新认证按钮
    updateAuthButtons() {
        // 这里可以添加更多的UI更新逻辑
        // 比如显示/隐藏某些功能按钮等
    }

    // 显示登录模态框
    showLoginModal() {
        this.removeExistingModals();
        
        const modal = document.createElement('div');
        modal.id = 'login-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 fade-in';
        
        modal.innerHTML = `
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4 transform scale-95 transition-all duration-300" id="login-modal-content">
                <div class="p-6">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="log-in" class="w-5 h-5 text-white"></i>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900">用户登录</h2>
                        </div>
                        <button onclick="closeAuthModal()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                            <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>

                    <!-- 登录表单 -->
                    <form id="login-form" class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" name="email" class="form-input" placeholder="请输入邮箱地址" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <div class="relative">
                                <input type="password" name="password" class="form-input pr-12" placeholder="请输入密码" required>
                                <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePasswordVisibility('login')">
                                    <i data-lucide="eye" class="w-5 h-5" id="login-password-icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input type="checkbox" name="remember" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">记住我</span>
                            </label>
                            <button type="button" class="text-sm text-blue-600 hover:text-blue-800" onclick="showForgotPassword()">
                                忘记密码？
                            </button>
                        </div>

                        <button type="submit" class="btn btn-primary w-full btn-lg">
                            <i data-lucide="log-in" class="w-5 h-5 mr-2"></i>
                            登录
                        </button>
                    </form>

                    <!-- 分割线 -->
                    <div class="relative my-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">或</span>
                        </div>
                    </div>

                    <!-- 第三方登录 -->
                    <div class="space-y-3">
                        <button type="button" class="btn btn-secondary w-full" onclick="loginWithProvider('wechat')">
                            <i data-lucide="smartphone" class="w-5 h-5 mr-2"></i>
                            微信登录
                        </button>
                        <button type="button" class="btn btn-secondary w-full" onclick="loginWithProvider('qq')">
                            <i data-lucide="message-circle" class="w-5 h-5 mr-2"></i>
                            QQ登录
                        </button>
                    </div>

                    <!-- 注册链接 -->
                    <div class="text-center mt-6">
                        <span class="text-gray-600">还没有账号？</span>
                        <button type="button" class="text-blue-600 hover:text-blue-800 font-medium ml-1" onclick="showRegisterModal()">
                            立即注册
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        // 动画效果
        setTimeout(() => {
            modal.classList.remove('fade-in');
            const content = document.getElementById('login-modal-content');
            if (content) {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }
        }, 10);

        // 绑定表单提交事件
        this.bindLoginForm();
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 显示注册模态框
    showRegisterModal() {
        this.removeExistingModals();
        
        const modal = document.createElement('div');
        modal.id = 'register-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 fade-in';
        
        modal.innerHTML = `
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-md mx-4 transform scale-95 transition-all duration-300" id="register-modal-content">
                <div class="p-6">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="user-plus" class="w-5 h-5 text-white"></i>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900">用户注册</h2>
                        </div>
                        <button onclick="closeAuthModal()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                            <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>

                    <!-- 注册表单 -->
                    <form id="register-form" class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" name="username" class="form-input" placeholder="请输入用户名" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" name="email" class="form-input" placeholder="请输入邮箱地址" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <div class="relative">
                                <input type="password" name="password" class="form-input pr-12" placeholder="请输入密码（至少6位）" required>
                                <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePasswordVisibility('register')">
                                    <i data-lucide="eye" class="w-5 h-5" id="register-password-icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">确认密码</label>
                            <div class="relative">
                                <input type="password" name="confirmPassword" class="form-input pr-12" placeholder="请再次输入密码" required>
                                <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePasswordVisibility('confirm')">
                                    <i data-lucide="eye" class="w-5 h-5" id="confirm-password-icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="flex items-center">
                                <input type="checkbox" name="agreement" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" required>
                                <span class="ml-2 text-sm text-gray-600">
                                    我已阅读并同意
                                    <a href="#" class="text-blue-600 hover:text-blue-800">用户协议</a>
                                    和
                                    <a href="#" class="text-blue-600 hover:text-blue-800">隐私政策</a>
                                </span>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-full btn-lg">
                            <i data-lucide="user-plus" class="w-5 h-5 mr-2"></i>
                            注册账号
                        </button>
                    </form>

                    <!-- 登录链接 -->
                    <div class="text-center mt-6">
                        <span class="text-gray-600">已有账号？</span>
                        <button type="button" class="text-blue-600 hover:text-blue-800 font-medium ml-1" onclick="showLoginModal()">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        // 动画效果
        setTimeout(() => {
            modal.classList.remove('fade-in');
            const content = document.getElementById('register-modal-content');
            if (content) {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }
        }, 10);

        // 绑定表单提交事件
        this.bindRegisterForm();
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 绑定登录表单事件
    bindLoginForm() {
        const form = document.getElementById('login-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(new FormData(form));
            });
        }
    }

    // 绑定注册表单事件
    bindRegisterForm() {
        const form = document.getElementById('register-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRegister(new FormData(form));
            });
        }
    }

    // 处理登录
    async handleLogin(formData) {
        const email = formData.get('email');
        const password = formData.get('password');
        const remember = formData.get('remember');

        // 简单验证
        if (!email || !password) {
            showToast('请填写完整的登录信息', 'error');
            return;
        }

        // 禁用提交按钮
        const submitBtn = document.querySelector('#login-form button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 mr-2 animate-spin"></i>登录中...';
        }

        // 模拟登录请求
        try {
            showToast('正在登录...', 'info');

            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 模拟登录成功
            const userData = {
                id: 1,
                username: email.split('@')[0], // 使用邮箱前缀作为用户名
                email: email,
                avatar: null,
                loginTime: new Date().toISOString(),
                plan: 'basic', // 用户套餐
                remainingCredits: 50 // 剩余次数
            };

            // 保存登录状态
            const token = 'mock-token-' + Date.now();
            localStorage.setItem('userToken', token);
            localStorage.setItem('userInfo', JSON.stringify(userData));

            // 如果选择记住我，设置更长的过期时间
            if (remember) {
                localStorage.setItem('rememberMe', 'true');
                localStorage.setItem('tokenExpiry', (Date.now() + 30 * 24 * 60 * 60 * 1000).toString()); // 30天
            } else {
                localStorage.setItem('tokenExpiry', (Date.now() + 24 * 60 * 60 * 1000).toString()); // 1天
            }

            this.isLoggedIn = true;
            this.currentUser = userData;

            // 更新UI
            this.updateUIForLoggedInUser();

            showToast('登录成功！欢迎回来', 'success');
            this.closeAuthModal();

            // 触发登录成功事件
            window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: userData }));

        } catch (error) {
            showToast('登录失败，请检查邮箱和密码', 'error');
        } finally {
            // 恢复提交按钮
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i data-lucide="log-in" class="w-5 h-5 mr-2"></i>登录';
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        }
    }

    // 处理注册
    async handleRegister(formData) {
        const username = formData.get('username');
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const agreement = formData.get('agreement');

        // 验证
        if (!username || !email || !password || !confirmPassword) {
            showToast('请填写完整的注册信息', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showToast('两次输入的密码不一致', 'error');
            return;
        }

        if (password.length < 6) {
            showToast('密码长度至少6位', 'error');
            return;
        }

        if (!agreement) {
            showToast('请同意用户协议和隐私政策', 'error');
            return;
        }

        // 模拟注册请求
        try {
            showToast('正在注册...', 'info');
            
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            showToast('注册成功！请登录', 'success');
            this.showLoginModal();
            
        } catch (error) {
            showToast('注册失败，请稍后重试', 'error');
        }
    }

    // 移除现有模态框
    removeExistingModals() {
        const existingModals = document.querySelectorAll('#login-modal, #register-modal');
        existingModals.forEach(modal => modal.remove());
    }

    // 关闭认证模态框
    closeAuthModal() {
        this.removeExistingModals();
    }

    // 退出登录
    logout(showConfirm = true) {
        if (showConfirm && !confirm('确定要退出登录吗？')) {
            return;
        }

        // 停止token检查
        this.stopTokenExpiryCheck();

        // 清除本地存储的用户数据
        localStorage.removeItem('userToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('tokenExpiry');
        localStorage.removeItem('rememberMe');

        // 更新状态
        this.isLoggedIn = false;
        this.currentUser = null;

        // 更新UI
        this.updateUIForGuestUser();

        if (showConfirm) {
            showToast('已成功退出登录', 'success');
        }

        // 触发退出登录事件
        window.dispatchEvent(new CustomEvent('userLoggedOut'));

        // 关闭用户菜单
        const userMenu = document.getElementById('user-menu-dropdown');
        if (userMenu) {
            userMenu.remove();
        }
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查是否已登录
    isUserLoggedIn() {
        return this.isLoggedIn;
    }

    // 更新用户信息
    updateUserInfo(newUserInfo) {
        if (this.isLoggedIn) {
            this.currentUser = { ...this.currentUser, ...newUserInfo };
            localStorage.setItem('userInfo', JSON.stringify(this.currentUser));
            this.updateUIForLoggedInUser();
        }
    }
}

// 切换密码可见性
function togglePasswordVisibility(type) {
    const input = document.querySelector(`[name="${type === 'confirm' ? 'confirmPassword' : 'password'}"]`);
    const icon = document.getElementById(`${type}-password-icon`);
    
    if (input && icon) {
        if (input.type === 'password') {
            input.type = 'text';
            icon.setAttribute('data-lucide', 'eye-off');
        } else {
            input.type = 'password';
            icon.setAttribute('data-lucide', 'eye');
        }
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
}

// 第三方登录
function loginWithProvider(provider) {
    showToast(`${provider === 'wechat' ? '微信' : 'QQ'}登录功能开发中...`, 'info');
}

// 忘记密码
function showForgotPassword() {
    showToast('忘记密码功能开发中...', 'info');
}

// 创建全局实例
const authSystem = new AuthSystem();

// 全局函数
function showLoginModal() {
    authSystem.showLoginModal();
}

function showRegisterModal() {
    authSystem.showRegisterModal();
}

function closeAuthModal() {
    authSystem.closeAuthModal();
}

// 退出登录
function logout() {
    authSystem.logout();
}

// 导出到全局
window.authSystem = authSystem;
window.showLoginModal = showLoginModal;
window.showRegisterModal = showRegisterModal;
window.closeAuthModal = closeAuthModal;
window.logout = logout;
window.togglePasswordVisibility = togglePasswordVisibility;
window.loginWithProvider = loginWithProvider;
window.showForgotPassword = showForgotPassword;
