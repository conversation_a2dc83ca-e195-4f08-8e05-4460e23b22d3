/**
 * 组件样式 - 性能优化版
 * 包含非关键的组件样式，使用GPU加速和CSS变量
 */

/* 导入变量 */
@import url('variables.css');

/* 论文类型卡片 - 性能优化版 */
.essay-type-card {
    cursor: pointer;
    transition: transform var(--transition-duration-300) var(--transition-timing-out),
                box-shadow var(--transition-duration-300) var(--transition-timing-out),
                border-color var(--transition-duration-300) var(--transition-timing-out);
    border: 2px solid transparent;
    background: var(--gradient-bg-light);
    position: relative;
    overflow: hidden;
    user-select: none;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    /* GPU加速 */
    will-change: transform;
    transform: translateZ(0);
    /* 优化合成层 */
    contain: layout style paint;
}

.essay-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.essay-type-card:hover::before {
    opacity: 1;
}

.essay-type-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: rgba(59, 130, 246, 0.2);
}

.essay-type-card.active {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.essay-type-card.active::before {
    opacity: 0;
}

.essay-type-card .card-content {
    position: relative;
    z-index: 1;
}

.essay-type-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 8px 16px -4px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.essay-type-card:hover .essay-type-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 24px -4px rgba(59, 130, 246, 0.4);
}

.essay-type-card.active .essay-type-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    transform: scale(1.05);
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 0.25rem;
    transition: width 0.3s ease-in-out;
}

/* 徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 浮动操作按钮 */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
}

/* 通知样式 */
.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    max-width: 24rem;
    padding: 1rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

.toast-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.toast-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.toast-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.toast-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    padding: 1rem;
    animation: fadeIn 0.2s ease;
}

.modal-content {
    background: white;
    border-radius: 0.75rem;
    max-width: 32rem;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    animation: scaleIn 0.2s ease;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 下拉菜单 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    width: 12rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 0.5rem 0;
    z-index: 50;
    animation: fadeInDown 0.2s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    text-decoration: none;
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

/* 骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
    margin-bottom: 0;
    width: 60%;
}

.skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.skeleton-button {
    height: 2.5rem;
    border-radius: 0.375rem;
    width: 6rem;
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #1f2937;
    color: white;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 0.25rem solid transparent;
    border-top-color: #1f2937;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .essay-type-icon {
        width: 2.5rem;
        height: 2.5rem;
        margin-bottom: 0.75rem;
    }
    
    .fab {
        bottom: 5rem;
        right: 1rem;
        width: 3rem;
        height: 3rem;
    }
    
    .toast {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
    
    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
}

@media (min-width: 1024px) {
    .essay-type-icon {
        width: 4rem;
        height: 4rem;
        margin-bottom: 1.25rem;
    }
}
