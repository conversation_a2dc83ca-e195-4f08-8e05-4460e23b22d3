/**
 * 页面切换优化器
 * 实现平滑的页面过渡动画和预加载机制
 */

class PageTransition {
    constructor() {
        this.isTransitioning = false;
        this.cache = new Map();
        this.preloadedPages = new Set();
        this.transitionDuration = 300;
        
        this.init();
    }

    init() {
        this.setupPageTransitions();
        this.setupPreloading();
        this.setupBackButton();
        this.setupProgressIndicator();
    }

    // 设置页面切换
    setupPageTransitions() {
        // 拦截所有内部链接
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.isInternalLink(link.href)) {
                e.preventDefault();
                this.navigateToPage(link.href);
            }
        });

        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.navigateToPage(e.state.page, false);
            }
        });
    }

    // 设置预加载
    setupPreloading() {
        // 鼠标悬停时预加载
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.isInternalLink(link.href)) {
                this.preloadPage(link.href);
            }
        });

        // 触摸开始时预加载（移动端）
        document.addEventListener('touchstart', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.isInternalLink(link.href)) {
                this.preloadPage(link.href);
            }
        }, { passive: true });
    }

    // 设置返回按钮处理
    setupBackButton() {
        // 添加当前页面到历史记录
        const currentPage = window.location.href;
        history.replaceState({ page: currentPage }, '', currentPage);
    }

    // 设置进度指示器
    setupProgressIndicator() {
        // 创建进度条
        const progressBar = document.createElement('div');
        progressBar.id = 'page-transition-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            z-index: 9999;
            transition: width 0.3s ease;
            opacity: 0;
        `;
        document.body.appendChild(progressBar);
    }

    // 判断是否为内部链接
    isInternalLink(href) {
        try {
            const url = new URL(href, window.location.origin);
            return url.origin === window.location.origin && 
                   url.pathname.endsWith('.html');
        } catch {
            return false;
        }
    }

    // 预加载页面
    async preloadPage(href) {
        if (this.preloadedPages.has(href) || this.cache.has(href)) {
            return;
        }

        try {
            this.preloadedPages.add(href);
            
            // 预加载HTML
            const response = await fetch(href);
            const html = await response.text();
            this.cache.set(href, html);
            
            // 预加载页面特定的资源
            this.preloadPageResources(href);
            
            console.log(`📄 Preloaded: ${href}`);
        } catch (error) {
            console.error(`❌ Failed to preload ${href}:`, error);
            this.preloadedPages.delete(href);
        }
    }

    // 预加载页面特定资源
    preloadPageResources(href) {
        const pageName = this.getPageName(href);
        const pageResources = this.getPageResources(pageName);
        
        pageResources.forEach(resource => {
            this.preloadResource(resource);
        });
    }

    // 预加载资源
    preloadResource(src) {
        const link = document.createElement('link');
        
        if (src.endsWith('.css')) {
            link.rel = 'prefetch';
            link.as = 'style';
        } else if (src.endsWith('.js')) {
            link.rel = 'prefetch';
            link.as = 'script';
        } else {
            link.rel = 'prefetch';
        }
        
        link.href = src;
        document.head.appendChild(link);
    }

    // 获取页面名称
    getPageName(href) {
        const url = new URL(href, window.location.origin);
        return url.pathname.split('/').pop().replace('.html', '') || 'index';
    }

    // 获取页面特定资源
    getPageResources(pageName) {
        const resources = {
            'history': ['js/history.js'],
            'plagiarism': ['js/plagiarism.js'],
            'aigc': ['js/aigc.js'],
            'billing': ['js/billing.js'],
            'profile': ['js/profile.js']
        };
        
        return resources[pageName] || [];
    }

    // 导航到页面
    async navigateToPage(href, addToHistory = true) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        try {
            // 显示进度条
            this.showProgress();
            
            // 开始退出动画
            await this.startExitAnimation();
            
            // 获取页面内容
            const html = await this.getPageContent(href);
            
            // 更新页面内容
            this.updatePageContent(html);
            
            // 更新URL和历史记录
            if (addToHistory) {
                history.pushState({ page: href }, '', href);
            }
            
            // 开始进入动画
            await this.startEnterAnimation();
            
            // 初始化新页面
            this.initializePage();
            
            // 隐藏进度条
            this.hideProgress();
            
        } catch (error) {
            console.error('❌ Navigation failed:', error);
            this.hideProgress();
            // 回退到默认导航
            window.location.href = href;
        } finally {
            this.isTransitioning = false;
        }
    }

    // 获取页面内容
    async getPageContent(href) {
        // 先检查缓存
        if (this.cache.has(href)) {
            return this.cache.get(href);
        }
        
        // 从网络获取
        const response = await fetch(href);
        const html = await response.text();
        
        // 缓存结果
        this.cache.set(href, html);
        
        return html;
    }

    // 更新页面内容
    updatePageContent(html) {
        const parser = new DOMParser();
        const newDoc = parser.parseFromString(html, 'text/html');
        
        // 更新title
        document.title = newDoc.title;
        
        // 更新meta标签
        this.updateMetaTags(newDoc);
        
        // 更新主要内容
        const newMain = newDoc.querySelector('main');
        const currentMain = document.querySelector('main');
        
        if (newMain && currentMain) {
            currentMain.innerHTML = newMain.innerHTML;
        }
        
        // 更新导航状态
        this.updateNavigationState();
    }

    // 更新meta标签
    updateMetaTags(newDoc) {
        const metaTags = ['description', 'keywords'];
        
        metaTags.forEach(name => {
            const newMeta = newDoc.querySelector(`meta[name="${name}"]`);
            const currentMeta = document.querySelector(`meta[name="${name}"]`);
            
            if (newMeta && currentMeta) {
                currentMeta.content = newMeta.content;
            }
        });
    }

    // 更新导航状态
    updateNavigationState() {
        const currentPage = this.getPageName(window.location.href);
        
        // 更新导航链接状态
        document.querySelectorAll('nav a').forEach(link => {
            const linkPage = this.getPageName(link.href);
            if (linkPage === currentPage) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    // 开始退出动画
    startExitAnimation() {
        return new Promise(resolve => {
            const main = document.querySelector('main');
            if (main) {
                main.style.transition = `opacity ${this.transitionDuration}ms ease-out`;
                main.style.opacity = '0';
                
                setTimeout(resolve, this.transitionDuration);
            } else {
                resolve();
            }
        });
    }

    // 开始进入动画
    startEnterAnimation() {
        return new Promise(resolve => {
            const main = document.querySelector('main');
            if (main) {
                // 重置样式
                main.style.opacity = '0';
                main.style.transform = 'translateY(20px)';
                
                // 强制重排
                main.offsetHeight;
                
                // 开始动画
                main.style.transition = `opacity ${this.transitionDuration}ms ease-out, transform ${this.transitionDuration}ms ease-out`;
                main.style.opacity = '1';
                main.style.transform = 'translateY(0)';
                
                setTimeout(() => {
                    main.style.transition = '';
                    main.style.transform = '';
                    resolve();
                }, this.transitionDuration);
            } else {
                resolve();
            }
        });
    }

    // 初始化页面
    initializePage() {
        // 重新初始化Lucide图标
        if (window.lucide) {
            window.lucide.createIcons();
        }
        
        // 触发页面初始化事件
        window.dispatchEvent(new CustomEvent('pageInitialized', {
            detail: { page: this.getPageName(window.location.href) }
        }));
        
        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // 显示进度条
    showProgress() {
        const progressBar = document.getElementById('page-transition-progress');
        if (progressBar) {
            progressBar.style.opacity = '1';
            progressBar.style.width = '30%';
            
            // 模拟进度
            setTimeout(() => {
                progressBar.style.width = '70%';
            }, 100);
        }
    }

    // 隐藏进度条
    hideProgress() {
        const progressBar = document.getElementById('page-transition-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            
            setTimeout(() => {
                progressBar.style.opacity = '0';
                progressBar.style.width = '0%';
            }, 200);
        }
    }

    // 清理缓存
    clearCache() {
        this.cache.clear();
        this.preloadedPages.clear();
    }

    // 获取缓存状态
    getCacheStatus() {
        return {
            cached: Array.from(this.cache.keys()),
            preloaded: Array.from(this.preloadedPages),
            size: this.cache.size
        };
    }
}

// 导出单例
export const pageTransition = new PageTransition();
export default PageTransition;
