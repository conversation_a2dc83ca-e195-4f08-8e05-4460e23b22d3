/**
 * 模态框组件
 * 可复用的模态框组件，支持多种配置和动画
 */

import { BaseComponent, component } from './base-component.js';

@component('modal')
export class Modal extends BaseComponent {
    get defaultOptions() {
        return {
            ...super.defaultOptions,
            size: 'md', // sm, md, lg, xl, full
            backdrop: true, // 是否显示背景遮罩
            keyboard: true, // 是否支持ESC键关闭
            focus: true, // 是否自动聚焦
            closeButton: true, // 是否显示关闭按钮
            animation: 'fade', // fade, slide, zoom
            position: 'center', // center, top, bottom
            persistent: false, // 是否持久化（点击背景不关闭）
            maxWidth: null,
            maxHeight: null,
            zIndex: 1000
        };
    }

    create() {
        this.createOverlay();
        this.createModal();
        this.setupContent();
        this.setupEvents();
    }

    createOverlay() {
        this.overlay = document.createElement('div');
        this.overlay.className = `modal-overlay modal-${this.options.animation}`;
        this.overlay.style.zIndex = this.options.zIndex;
        
        if (!this.options.backdrop) {
            this.overlay.style.background = 'transparent';
            this.overlay.style.pointerEvents = 'none';
        }
    }

    createModal() {
        this.modalElement = document.createElement('div');
        this.modalElement.className = `modal modal-${this.options.size} modal-${this.options.position}`;
        this.modalElement.setAttribute('role', 'dialog');
        this.modalElement.setAttribute('aria-modal', 'true');
        this.modalElement.setAttribute('tabindex', '-1');

        // 设置最大尺寸
        if (this.options.maxWidth) {
            this.modalElement.style.maxWidth = this.options.maxWidth;
        }
        if (this.options.maxHeight) {
            this.modalElement.style.maxHeight = this.options.maxHeight;
        }

        this.overlay.appendChild(this.modalElement);
    }

    setupContent() {
        // 创建头部
        this.header = document.createElement('div');
        this.header.className = 'modal-header';

        this.title = document.createElement('h3');
        this.title.className = 'modal-title';
        this.title.id = `modal-title-${Date.now()}`;
        this.modalElement.setAttribute('aria-labelledby', this.title.id);

        this.header.appendChild(this.title);

        // 创建关闭按钮
        if (this.options.closeButton) {
            this.closeBtn = document.createElement('button');
            this.closeBtn.className = 'modal-close';
            this.closeBtn.innerHTML = '<i data-lucide="x"></i>';
            this.closeBtn.setAttribute('aria-label', '关闭');
            this.header.appendChild(this.closeBtn);
        }

        // 创建主体
        this.body = document.createElement('div');
        this.body.className = 'modal-body';

        // 创建底部
        this.footer = document.createElement('div');
        this.footer.className = 'modal-footer';

        // 组装模态框
        this.modalElement.appendChild(this.header);
        this.modalElement.appendChild(this.body);
        this.modalElement.appendChild(this.footer);

        // 如果有现有内容，移动到body中
        if (this.element && this.element.children.length > 0) {
            while (this.element.firstChild) {
                this.body.appendChild(this.element.firstChild);
            }
        }
    }

    setupEvents() {
        // 背景点击关闭
        if (this.options.backdrop && !this.options.persistent) {
            this.overlay.addEventListener('click', (e) => {
                if (e.target === this.overlay) {
                    this.close();
                }
            });
        }

        // 关闭按钮点击
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => {
                this.close();
            });
        }

        // ESC键关闭
        if (this.options.keyboard) {
            document.addEventListener('keydown', this.handleKeydown.bind(this));
        }

        // 焦点管理
        if (this.options.focus) {
            this.overlay.addEventListener('focusin', this.handleFocusIn.bind(this));
        }
    }

    handleKeydown(event) {
        if (event.key === 'Escape' && this.isVisible()) {
            this.close();
        }
    }

    handleFocusIn(event) {
        // 确保焦点在模态框内
        if (!this.modalElement.contains(event.target)) {
            this.focus();
        }
    }

    // 显示模态框
    show() {
        if (this.isVisible()) return;

        // 保存当前焦点元素
        this.previousActiveElement = document.activeElement;

        // 添加到DOM
        document.body.appendChild(this.overlay);

        // 禁用页面滚动
        document.body.style.overflow = 'hidden';

        // 触发动画
        requestAnimationFrame(() => {
            this.overlay.classList.add('show');
            this.modalElement.classList.add('show');
        });

        // 设置焦点
        if (this.options.focus) {
            this.focus();
        }

        this.setState({ visible: true });
        this.emit('show');
    }

    // 隐藏模态框
    hide() {
        if (!this.isVisible()) return;

        // 移除显示类
        this.overlay.classList.remove('show');
        this.modalElement.classList.remove('show');

        // 等待动画完成后移除DOM
        setTimeout(() => {
            if (this.overlay.parentNode) {
                document.body.removeChild(this.overlay);
            }

            // 恢复页面滚动
            document.body.style.overflow = '';

            // 恢复焦点
            if (this.previousActiveElement) {
                this.previousActiveElement.focus();
                this.previousActiveElement = null;
            }

            this.setState({ visible: false });
            this.emit('hide');
        }, 300); // 动画持续时间
    }

    // 关闭模态框（可以被阻止）
    close() {
        const closeEvent = this.emit('close');
        if (!closeEvent.defaultPrevented) {
            this.hide();
        }
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible()) {
            this.close();
        } else {
            this.show();
        }
    }

    // 检查是否可见
    isVisible() {
        return this.overlay && this.overlay.parentNode && this.overlay.classList.contains('show');
    }

    // 设置焦点
    focus() {
        // 查找第一个可聚焦元素
        const focusableElements = this.modalElement.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        } else {
            this.modalElement.focus();
        }
    }

    // 设置标题
    setTitle(title) {
        this.title.textContent = title;
        this.emit('titleChange', { title });
    }

    // 设置内容
    setContent(content) {
        if (typeof content === 'string') {
            this.body.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            this.body.innerHTML = '';
            this.body.appendChild(content);
        }
        this.emit('contentChange', { content });
    }

    // 设置底部内容
    setFooter(content) {
        if (typeof content === 'string') {
            this.footer.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            this.footer.innerHTML = '';
            this.footer.appendChild(content);
        }
        this.emit('footerChange', { content });
    }

    // 添加按钮到底部
    addButton(text, className = 'btn btn-primary', onClick = null) {
        const button = document.createElement('button');
        button.className = className;
        button.textContent = text;
        
        if (onClick) {
            button.addEventListener('click', onClick);
        }

        this.footer.appendChild(button);
        return button;
    }

    // 清空底部
    clearFooter() {
        this.footer.innerHTML = '';
    }

    // 设置尺寸
    setSize(size) {
        this.modalElement.classList.remove(`modal-${this.options.size}`);
        this.modalElement.classList.add(`modal-${size}`);
        this.options.size = size;
        this.emit('sizeChange', { size });
    }

    // 销毁模态框
    destroy() {
        if (this.isVisible()) {
            this.hide();
        }

        // 移除事件监听器
        if (this.options.keyboard) {
            document.removeEventListener('keydown', this.handleKeydown.bind(this));
        }

        super.destroy();
    }
}

// 模态框管理器
export class ModalManager {
    constructor() {
        this.modals = new Set();
        this.zIndexCounter = 1000;
    }

    // 注册模态框
    register(modal) {
        this.modals.add(modal);
        modal.options.zIndex = this.zIndexCounter++;
    }

    // 注销模态框
    unregister(modal) {
        this.modals.delete(modal);
    }

    // 关闭所有模态框
    closeAll() {
        this.modals.forEach(modal => {
            if (modal.isVisible()) {
                modal.close();
            }
        });
    }

    // 获取顶层模态框
    getTopModal() {
        let topModal = null;
        let maxZIndex = 0;

        this.modals.forEach(modal => {
            if (modal.isVisible() && modal.options.zIndex > maxZIndex) {
                maxZIndex = modal.options.zIndex;
                topModal = modal;
            }
        });

        return topModal;
    }

    // 获取可见模态框数量
    getVisibleCount() {
        return Array.from(this.modals).filter(modal => modal.isVisible()).length;
    }
}

// 全局模态框管理器
export const modalManager = new ModalManager();

// 便捷方法
export function createModal(options = {}) {
    const modal = new Modal(null, options);
    modalManager.register(modal);
    return modal;
}

export function alert(message, title = '提示') {
    const modal = createModal({
        size: 'sm',
        closeButton: true
    });

    modal.setTitle(title);
    modal.setContent(`<p>${message}</p>`);
    modal.addButton('确定', 'btn btn-primary', () => {
        modal.close();
    });

    modal.show();
    return modal;
}

export function confirm(message, title = '确认') {
    return new Promise((resolve) => {
        const modal = createModal({
            size: 'sm',
            closeButton: true,
            persistent: true
        });

        modal.setTitle(title);
        modal.setContent(`<p>${message}</p>`);
        
        modal.addButton('取消', 'btn btn-secondary', () => {
            modal.close();
            resolve(false);
        });
        
        modal.addButton('确定', 'btn btn-primary', () => {
            modal.close();
            resolve(true);
        });

        modal.show();
    });
}
