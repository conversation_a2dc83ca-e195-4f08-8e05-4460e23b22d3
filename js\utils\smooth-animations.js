/**
 * 平滑动画系统
 * 提供高性能的动画和过渡效果
 */

class SmoothAnimations {
    constructor() {
        this.animationQueue = [];
        this.isAnimating = false;
        this.observers = new Map();
        this.rafId = null;
        
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupClickEffects();
        this.optimizeAnimations();
    }

    // 设置Intersection Observer用于滚动动画
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateElement(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            this.observers.set('scroll', observer);
            
            // 观察所有需要动画的元素
            this.observeAnimationElements();
        }
    }

    // 观察动画元素
    observeAnimationElements() {
        const elements = document.querySelectorAll('[data-animate]');
        const observer = this.observers.get('scroll');
        
        elements.forEach(element => {
            if (observer) {
                observer.observe(element);
            }
        });
    }

    // 设置滚动动画
    setupScrollAnimations() {
        let ticking = false;
        
        const updateScrollAnimations = () => {
            this.updateParallaxElements();
            this.updateScrollProgress();
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        }, { passive: true });
    }

    // 更新视差元素
    updateParallaxElements() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        const scrollY = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollY * speed);
            
            element.style.transform = `translateY(${yPos}px) translateZ(0)`;
        });
    }

    // 更新滚动进度
    updateScrollProgress() {
        const progressBars = document.querySelectorAll('[data-scroll-progress]');
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollProgress = window.pageYOffset / scrollHeight;
        
        progressBars.forEach(bar => {
            bar.style.transform = `scaleX(${scrollProgress})`;
        });
    }

    // 设置悬停效果
    setupHoverEffects() {
        document.addEventListener('mouseover', (e) => {
            const element = e.target.closest('[data-hover-effect]');
            if (element) {
                this.applyHoverEffect(element, true);
            }
        });

        document.addEventListener('mouseout', (e) => {
            const element = e.target.closest('[data-hover-effect]');
            if (element) {
                this.applyHoverEffect(element, false);
            }
        });
    }

    // 应用悬停效果
    applyHoverEffect(element, isHover) {
        const effect = element.dataset.hoverEffect;
        
        switch (effect) {
            case 'lift':
                element.style.transform = isHover ? 
                    'translateY(-4px) translateZ(0)' : 
                    'translateY(0) translateZ(0)';
                break;
                
            case 'scale':
                element.style.transform = isHover ? 
                    'scale(1.05) translateZ(0)' : 
                    'scale(1) translateZ(0)';
                break;
                
            case 'glow':
                element.style.boxShadow = isHover ? 
                    '0 0 20px rgba(59, 130, 246, 0.4)' : 
                    '';
                break;
                
            case 'rotate':
                element.style.transform = isHover ? 
                    'rotate(2deg) translateZ(0)' : 
                    'rotate(0deg) translateZ(0)';
                break;
        }
    }

    // 设置点击效果
    setupClickEffects() {
        document.addEventListener('click', (e) => {
            const element = e.target.closest('[data-click-effect]');
            if (element) {
                this.applyClickEffect(element, e);
            }
        });
    }

    // 应用点击效果
    applyClickEffect(element, event) {
        const effect = element.dataset.clickEffect;
        
        switch (effect) {
            case 'ripple':
                this.createRippleEffect(element, event);
                break;
                
            case 'pulse':
                this.createPulseEffect(element);
                break;
                
            case 'bounce':
                this.createBounceEffect(element);
                break;
        }
    }

    // 创建水波纹效果
    createRippleEffect(element, event) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1000;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // 创建脉冲效果
    createPulseEffect(element) {
        element.style.animation = 'pulse 0.3s ease-out';
        
        setTimeout(() => {
            element.style.animation = '';
        }, 300);
    }

    // 创建弹跳效果
    createBounceEffect(element) {
        element.style.animation = 'bounce 0.5s ease-out';
        
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }

    // 动画元素
    animateElement(element) {
        const animation = element.dataset.animate;
        const delay = parseInt(element.dataset.delay) || 0;
        const duration = parseInt(element.dataset.duration) || 600;
        
        setTimeout(() => {
            switch (animation) {
                case 'fadeIn':
                    this.fadeIn(element, duration);
                    break;
                    
                case 'slideUp':
                    this.slideUp(element, duration);
                    break;
                    
                case 'slideLeft':
                    this.slideLeft(element, duration);
                    break;
                    
                case 'slideRight':
                    this.slideRight(element, duration);
                    break;
                    
                case 'zoomIn':
                    this.zoomIn(element, duration);
                    break;
                    
                case 'rotateIn':
                    this.rotateIn(element, duration);
                    break;
            }
        }, delay);
    }

    // 淡入动画
    fadeIn(element, duration) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
        });
    }

    // 上滑动画
    slideUp(element, duration) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }

    // 左滑动画
    slideLeft(element, duration) {
        element.style.opacity = '0';
        element.style.transform = 'translateX(30px)';
        element.style.transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
        });
    }

    // 右滑动画
    slideRight(element, duration) {
        element.style.opacity = '0';
        element.style.transform = 'translateX(-30px)';
        element.style.transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
        });
    }

    // 缩放动画
    zoomIn(element, duration) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8)';
        element.style.transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
        });
    }

    // 旋转动画
    rotateIn(element, duration) {
        element.style.opacity = '0';
        element.style.transform = 'rotate(-10deg) scale(0.8)';
        element.style.transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'rotate(0deg) scale(1)';
        });
    }

    // 优化动画性能
    optimizeAnimations() {
        // 添加CSS动画关键帧
        this.addAnimationKeyframes();
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
        
        // 监听减少动画偏好
        if (window.matchMedia) {
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (prefersReducedMotion.matches) {
                this.disableAnimations();
            }
        }
    }

    // 添加CSS动画关键帧
    addAnimationKeyframes() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
            
            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
                40%, 43% { transform: translateY(-10px); }
                70% { transform: translateY(-5px); }
                90% { transform: translateY(-2px); }
            }
        `;
        document.head.appendChild(style);
    }

    // 暂停动画
    pauseAnimations() {
        document.body.style.setProperty('--animation-play-state', 'paused');
    }

    // 恢复动画
    resumeAnimations() {
        document.body.style.setProperty('--animation-play-state', 'running');
    }

    // 禁用动画
    disableAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 清理资源
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }
    }
}

// 导出单例
export const smoothAnimations = new SmoothAnimations();
export default SmoothAnimations;
