<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC智能降重 - 博晓文</title>
    <meta name="description" content="基于先进AI技术，智能改写文本内容，有效降低重复率，保持原文语义">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-6xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-800 bg-clip-text text-transparent mb-4 leading-tight">
                                AIGC智能降重
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                基于先进AI技术，智能改写文本内容，有效降低重复率，保持原文语义
                                <span class="inline-block ml-2 text-cyan-600">🔄</span>
                            </p>
                            
                            <!-- 特色标签 -->
                            <div class="flex flex-wrap gap-2 mt-4">
                                <span class="px-3 py-1 bg-cyan-100 text-cyan-800 text-sm rounded-full font-medium">
                                    🎯 99.8%降重成功率
                                </span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                    ⚡ 3秒智能改写
                                </span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                    🔒 语义保持95%+
                                </span>
                            </div>
                        </div>
                        
                        <!-- 统计卡片 -->
                        <div class="hidden lg:flex space-x-6 xl:space-x-8">
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="target" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-cyan-600">99.8%</div>
                                <div class="text-sm text-gray-500">降重成功率</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-blue-600">3秒</div>
                                <div class="text-sm text-gray-500">智能改写</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-purple-600">95%+</div>
                                <div class="text-sm text-gray-500">语义保持</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能特色 -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="cpu" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">AI智能</h3>
                        <p class="text-xs text-gray-600">先进语言模型</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="layers" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">多级降重</h3>
                        <p class="text-xs text-gray-600">轻度/中度/深度</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">批量处理</h3>
                        <p class="text-xs text-gray-600">支持文件上传</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.4s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="shield" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">语义保护</h3>
                        <p class="text-xs text-gray-600">保持原文逻辑</p>
                    </div>
                </div>

                <!-- 主要功能区域 -->
                <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                    <div class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-t-xl p-6">
                        <h2 class="text-xl font-bold flex items-center gap-3">
                            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                            </div>
                            AIGC智能降重工具
                        </h2>
                        <p class="text-cyan-100 mt-2">输入您的文本内容，AI将为您智能改写，有效降低重复率</p>
                    </div>
                    <div class="p-6">
                        <!-- Tabs -->
                        <div class="flex border-b border-gray-200 mb-6">
                            <button class="py-2 px-4 border-b-2 border-cyan-500 text-cyan-600 font-medium text-sm tab-button active" data-tab="text">
                                文本降重
                            </button>
                            <button class="py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="file">
                                文件降重
                            </button>
                        </div>

                        <!-- 降重级别选择 -->
                        <div class="mb-6">
                            <label class="form-label text-lg font-semibold text-gray-900 mb-4 block">选择降重级别</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="rewrite-levels">
                                <!-- 降重级别选项将由JavaScript生成 -->
                            </div>
                        </div>

                        <!-- Text Input Tab -->
                        <div id="text-tab" class="tab-content space-y-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 输入区域 -->
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <label class="form-label font-semibold text-gray-900">原文内容</label>
                                        <button class="text-sm text-cyan-600 hover:text-cyan-700 transition-colors" onclick="clearInput()">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                                            清空
                                        </button>
                                    </div>
                                    <textarea id="input-text" placeholder="请输入需要降重的文本内容..." class="form-input form-textarea min-h-[300px] resize-none"></textarea>
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                                            <span id="input-char-count" class="">字数: 0/5000</span>
                                            <span id="estimated-time" class="text-green-600 hidden">预计处理时间: 1秒</span>
                                        </div>
                                        <button id="rewrite-btn" onclick="startRewrite()" class="btn btn-primary btn-lg group" disabled>
                                            <i data-lucide="zap" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                            开始智能降重
                                        </button>
                                    </div>
                                </div>

                                <!-- 输出区域 -->
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <label class="form-label font-semibold text-gray-900">降重结果</label>
                                        <div class="flex space-x-2" id="output-actions" style="display: none;">
                                            <button onclick="copyResult()" class="btn btn-secondary btn-sm" id="copy-btn">
                                                <i data-lucide="copy" class="w-4 h-4 mr-1"></i>
                                                复制结果
                                            </button>
                                            <button onclick="regenerateResult()" class="btn btn-secondary btn-sm">
                                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-1"></i>
                                                重新生成
                                            </button>
                                            <button onclick="downloadResult()" class="btn btn-secondary btn-sm">
                                                <i data-lucide="download" class="w-4 h-4 mr-1"></i>
                                                下载结果
                                            </button>
                                        </div>
                                    </div>
                                    <textarea id="output-text" placeholder="降重结果将在这里显示..." class="form-input form-textarea min-h-[300px] resize-none" readonly></textarea>
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                                            <span id="output-char-count">字数: 0</span>
                                            <span id="reduction-rate" class="text-green-600 hidden">预计降重率: 60-80%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 进度条 -->
                            <div id="progress-container" class="space-y-4 hidden">
                                <div class="text-center">
                                    <div class="inline-flex items-center space-x-2 text-cyan-600">
                                        <div class="spinner"></div>
                                        <span id="progress-text">AI正在智能改写中，请稍候...</span>
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-cyan-500 to-blue-600" id="progress-fill" style="width: 0%"></div>
                                </div>
                                <div class="text-sm text-gray-500 text-center" id="progress-detail">正在分析文本结构...</div>
                            </div>
                        </div>

                        <!-- File Upload Tab -->
                        <div id="file-tab" class="tab-content space-y-6 hidden">
                            <div class="border-2 border-dashed border-cyan-300 rounded-xl p-8 sm:p-12 text-center bg-gradient-to-br from-cyan-50 to-blue-50 hover:from-cyan-100 hover:to-blue-100 transition-all duration-300 group" id="upload-area">
                                <div class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    <i data-lucide="upload" class="w-8 h-8 sm:w-10 sm:h-10 text-white"></i>
                                </div>
                                <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-2">上传文档文件</h3>
                                <p class="text-gray-600 mb-2">拖拽文件到此处或点击上传</p>
                                <p class="text-sm text-gray-500 mb-4 sm:mb-6">支持 Word(.doc, .docx)、PDF(.pdf)、文本(.txt) 格式，最大10MB</p>
                                
                                <button class="btn btn-primary btn-lg group" onclick="document.getElementById('file-input').click()">
                                    <i data-lucide="upload" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    选择文件上传
                                </button>
                                <input type="file" id="file-input" class="hidden" accept=".doc,.docx,.pdf,.txt" onchange="handleFileUpload(this)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/global-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/aigc.js"></script>

    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('aigc');
        });
    </script>
</body>
</html>
