/**
 * 应用核心配置
 * 统一管理应用的基础配置信息
 */

export const AppConfig = {
    // 应用基本信息
    app: {
        name: "博晓文",
        description: "高质量原创AI论文写作平台，一键万字生成，无限改稿！",
        version: "1.0.0",
        author: "博晓文团队",
        keywords: ["AI", "论文", "写作", "学术"]
    },

    // API配置
    api: {
        baseUrl: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000/api',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        endpoints: {
            auth: '/auth',
            essay: '/essay',
            plagiarism: '/plagiarism',
            billing: '/billing',
            user: '/user'
        }
    },

    // 功能开关
    features: {
        plagiarismCheck: true,
        aiQc: true,
        billing: true,
        analytics: true,
        darkMode: true,
        offlineMode: false,
        experimentalFeatures: false
    },

    // 限制配置
    limits: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxTextLength: 50000, // 50k字符
        maxHistoryItems: 100,
        maxConcurrentRequests: 5,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
        maxRetries: 3
    },

    // 缓存配置
    cache: {
        enabled: true,
        ttl: 5 * 60 * 1000, // 5分钟
        maxSize: 50, // 最大缓存项数
        storageType: 'localStorage', // localStorage | sessionStorage | memory
        keys: {
            userPreferences: 'user_preferences',
            formData: 'form_data',
            essayTypes: 'essay_types',
            recentHistory: 'recent_history'
        }
    },

    // 安全配置
    security: {
        enableCSRF: true,
        enableXSS: true,
        tokenRefreshThreshold: 5 * 60 * 1000, // 5分钟前刷新token
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15分钟
    },

    // 环境配置
    environment: {
        isDevelopment: process.env.NODE_ENV === 'development',
        isProduction: process.env.NODE_ENV === 'production',
        enableDebug: process.env.NODE_ENV === 'development',
        enableAnalytics: process.env.NODE_ENV === 'production'
    }
};

// 配置验证函数
export function validateConfig() {
    const required = ['app.name', 'api.baseUrl', 'limits.maxFileSize'];
    const missing = [];

    required.forEach(path => {
        const value = getNestedValue(AppConfig, path);
        if (value === undefined || value === null) {
            missing.push(path);
        }
    });

    if (missing.length > 0) {
        throw new Error(`Missing required configuration: ${missing.join(', ')}`);
    }

    return true;
}

// 获取嵌套配置值
function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 动态更新配置
export function updateConfig(path, value) {
    const keys = path.split('.');
    let current = AppConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
}

// 获取配置值（支持默认值）
export function getConfig(path, defaultValue = null) {
    const value = getNestedValue(AppConfig, path);
    return value !== undefined ? value : defaultValue;
}

// 初始化配置
export function initializeConfig() {
    try {
        validateConfig();
        
        // 设置全局配置
        window.APP_CONFIG = AppConfig;
        
        // 发送配置就绪事件
        window.dispatchEvent(new CustomEvent('configReady', { 
            detail: AppConfig 
        }));
        
        return true;
    } catch (error) {
        console.error('Configuration initialization failed:', error);
        return false;
    }
}
