<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面 - 博晓文</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-4xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">功能测试页面</h1>
                    <p class="text-gray-600">测试所有组件和功能是否正常工作</p>
                </div>

                <!-- 测试区域 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 认证系统测试 -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                            <i data-lucide="user-check" class="w-5 h-5 text-blue-600"></i>
                            认证系统测试
                        </h2>
                        <div class="space-y-3">
                            <button onclick="showLoginModal()" class="btn btn-primary w-full">
                                <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                                测试登录窗口
                            </button>
                            <button onclick="showRegisterModal()" class="btn btn-secondary w-full">
                                <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                                测试注册窗口
                            </button>
                            <button onclick="testUserStatus()" class="btn btn-outline w-full">
                                <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                                检查登录状态
                            </button>
                            <button onclick="logout()" class="btn btn-danger w-full">
                                <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                                退出登录
                            </button>
                        </div>
                    </div>

                    <!-- Toast通知测试 -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                            <i data-lucide="bell" class="w-5 h-5 text-green-600"></i>
                            Toast通知测试
                        </h2>
                        <div class="space-y-3">
                            <button onclick="showToast('这是一条成功消息！', 'success')" class="btn btn-success w-full">
                                <i data-lucide="check-circle" class="w-4 h-4 mr-2"></i>
                                成功消息
                            </button>
                            <button onclick="showToast('这是一条错误消息！', 'error')" class="btn btn-danger w-full">
                                <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                                错误消息
                            </button>
                            <button onclick="showToast('这是一条警告消息！', 'warning')" class="btn btn-warning w-full">
                                <i data-lucide="alert-triangle" class="w-4 h-4 mr-2"></i>
                                警告消息
                            </button>
                            <button onclick="showToast('这是一条信息消息！', 'info')" class="btn btn-info w-full">
                                <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                                信息消息
                            </button>
                            <button onclick="testLoadingToast()" class="btn btn-outline w-full">
                                <i data-lucide="loader-2" class="w-4 h-4 mr-2"></i>
                                加载消息
                            </button>
                        </div>
                    </div>

                    <!-- 表单组件测试 -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                            <i data-lucide="edit-3" class="w-5 h-5 text-purple-600"></i>
                            表单组件测试
                        </h2>
                        <div class="space-y-4">
                            <div>
                                <label class="form-label">测试输入框</label>
                                <input type="text" class="form-input" placeholder="请输入内容">
                            </div>
                            <div>
                                <label class="form-label">测试下拉框</label>
                                <select class="form-input form-select">
                                    <option value="">请选择选项</option>
                                    <option value="1">选项一</option>
                                    <option value="2">选项二</option>
                                    <option value="3">选项三</option>
                                </select>
                            </div>
                            <div>
                                <label class="form-label">测试文本域</label>
                                <textarea class="form-input form-textarea" placeholder="请输入多行文本"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 导航测试 -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                            <i data-lucide="navigation" class="w-5 h-5 text-orange-600"></i>
                            导航测试
                        </h2>
                        <div class="space-y-3">
                            <a href="index.html" class="btn btn-outline w-full">
                                <i data-lucide="pen-tool" class="w-4 h-4 mr-2"></i>
                                主页
                            </a>
                            <a href="history.html" class="btn btn-outline w-full">
                                <i data-lucide="history" class="w-4 h-4 mr-2"></i>
                                历史记录
                            </a>
                            <a href="plagiarism.html" class="btn btn-outline w-full">
                                <i data-lucide="file-check" class="w-4 h-4 mr-2"></i>
                                论文查重
                            </a>
                            <a href="aigc.html" class="btn btn-outline w-full">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                AIGC降重
                            </a>
                            <a href="billing.html" class="btn btn-outline w-full">
                                <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                套餐管理
                            </a>
                            <a href="profile.html" class="btn btn-outline w-full">
                                <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                                个人中心
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                        <i data-lucide="activity" class="w-5 h-5 text-red-600"></i>
                        系统状态
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="p-4 bg-green-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-green-800">组件系统</span>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">正常</span>
                            </div>
                        </div>
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-blue-800">认证系统</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">正常</span>
                            </div>
                        </div>
                        <div class="p-4 bg-purple-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-purple-800">通知系统</span>
                                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    
    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('test');
        });

        // 测试函数
        function testUserStatus() {
            const isLoggedIn = authSystem.isUserLoggedIn();
            const currentUser = authSystem.getCurrentUser();
            
            if (isLoggedIn) {
                showToast(`已登录用户: ${currentUser.username} (${currentUser.email})`, 'success');
            } else {
                showToast('当前未登录', 'info');
            }
        }

        function testLoadingToast() {
            const loadingId = toastSystem.loading('正在处理请求...');
            
            setTimeout(() => {
                toastSystem.remove(loadingId);
                showToast('处理完成！', 'success');
            }, 3000);
        }

        // 监听用户登录事件
        window.addEventListener('userLoggedIn', function(e) {
            showToast(`欢迎回来，${e.detail.username}！`, 'success');
        });

        // 监听用户退出事件
        window.addEventListener('userLoggedOut', function() {
            showToast('已退出登录', 'info');
        });
    </script>
</body>
</html>
