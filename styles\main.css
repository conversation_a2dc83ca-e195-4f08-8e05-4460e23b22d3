/*
 * 主CSS文件 - 优化版
 * 只包含最基础的样式，其他样式按需加载
 */

/* 导入关键CSS变量 */
@import url('variables.css');

/* 性能优化的CSS变量 - 减少重复定义 */
:root {
    /* 核心颜色系统 */
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;

    /* 功能色 */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;

    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-500: #6b7280;
    --gray-700: #374151;
    --gray-900: #111827;

    /* 阴影系统 - 简化版 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* 边框半径 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
}

/* Utility Classes */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-shadow {
    box-shadow: var(--card-shadow);
}

.card-shadow-hover {
    box-shadow: var(--card-shadow-hover);
}

/* Button Styles - 完全匹配原项目shadcn/ui */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    min-height: 44px;
    padding: 0.5rem 1rem;
    position: relative;
    overflow: hidden;
    /* 移动端优化 */
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
}

.btn:disabled {
    pointer-events: none;
    opacity: 0.5;
}

.btn:focus-visible {
    outline: none;
    box-shadow: var(--ring-blue-strong);
}

/* 按钮尺寸 */
.btn-sm {
    height: 2.25rem;
    border-radius: 0.375rem;
    padding: 0 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    height: 2.75rem;
    border-radius: 0.375rem;
    padding: 0 2rem;
    font-size: 1rem;
}

/* 主要按钮 */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--button-shadow);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-gradient-hover);
    transform: translateY(-1px);
    box-shadow: var(--button-shadow-hover);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--button-shadow);
}

/* 次要按钮 */
.btn-secondary {
    background: #f8fafc;
    color: #475569;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover:not(:disabled) {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 成功按钮 */
.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.39);
}

/* 危险按钮 */
.btn-danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.39);
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(239, 68, 68, 0.39);
}

/* 轮廓按钮 */
.btn-outline {
    border: 1px solid #e2e8f0;
    background: transparent;
    color: #475569;
}

.btn-outline:hover:not(:disabled) {
    background: #f8fafc;
    color: #334155;
}

/* 幽灵按钮 */
.btn-ghost {
    background: transparent;
    color: #475569;
}

.btn-ghost:hover:not(:disabled) {
    background: #f8fafc;
    color: #334155;
}

/* Form Styles - 完全匹配原项目shadcn/ui */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #0f172a;
    margin-bottom: 0.5rem;
    line-height: 1.25rem;
}

.form-input {
    display: flex;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
    background-color: #ffffff;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #0f172a;
    transition: all 0.2s ease-in-out;
    /* 移动端优化 */
    -webkit-text-size-adjust: 100%;
    touch-action: manipulation;
}

.form-input::placeholder {
    color: #64748b;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: var(--ring-blue);
}

.form-input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.5;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25rem 1.25rem;
    padding-right: 2.75rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.form-select:hover {
    border-color: #94a3b8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.form-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    transform: translateY(-1px);
}

.form-select option {
    padding: 0.5rem 0.75rem;
    color: #1f2937;
    background-color: #ffffff;
}

.form-select option:hover {
    background-color: #f3f4f6;
}

.form-select option:checked {
    background-color: #dbeafe;
    color: #1d4ed8;
    font-weight: 500;
}

/* 表单错误状态 */
.form-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 表单成功状态 */
.form-input.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 字符计数器 */
.char-counter {
    font-size: 0.75rem;
    color: #64748b;
    text-align: right;
    margin-top: 0.25rem;
}

.char-counter.warning {
    color: #f59e0b;
}

.char-counter.danger {
    color: #ef4444;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    padding: 1.5rem;
    background: var(--primary-gradient);
    color: white;
}

.card-content {
    padding: 1.5rem;
}

/* Essay Type Cards - 完全匹配原项目 */
.essay-type-card {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    background: var(--bg-gradient-light);
    position: relative;
    overflow: hidden;
    /* 移动端优化 */
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
}

.essay-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-gradient-gray);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.essay-type-card:hover::before {
    opacity: 1;
}

.essay-type-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--card-shadow-xl);
    border-color: rgba(59, 130, 246, 0.2);
}

.essay-type-card.active {
    border-color: #3b82f6;
    background: var(--bg-gradient-blue);
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.essay-type-card.active::before {
    opacity: 0;
}

.essay-type-card .card-content {
    position: relative;
    z-index: 1;
}

.essay-type-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 8px 16px -4px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.essay-type-card:hover .essay-type-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 24px -4px rgba(59, 130, 246, 0.4);
}

.essay-type-card.active .essay-type-icon {
    background: var(--primary-gradient);
    transform: scale(1.05);
}

/* 响应式图标尺寸 */
@media (max-width: 768px) {
    .essay-type-icon {
        width: 2.5rem;
        height: 2.5rem;
        margin-bottom: 0.75rem;
    }
}

@media (min-width: 1024px) {
    .essay-type-icon {
        width: 4rem;
        height: 4rem;
        margin-bottom: 1.25rem;
    }
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 0.25rem;
    transition: width 0.3s ease-in-out;
}

/* Loading Spinner */
.spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 移动端优化类 */
.mobile-tap-highlight {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
}

.mobile-no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.touch-manipulation {
    touch-action: manipulation;
}

/* 渐变文字效果 */
.gradient-text-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-text-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 高级卡片效果 */
.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
}

.card-glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.25);
}

/* 按钮组效果 */
.btn-group {
    display: flex;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    border-right: none;
}

/* 进度指示器 */
.progress-ring {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 浮动动作按钮 */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
}

/* Mobile Optimizations */
@media (max-width: 640px) {
    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
        min-height: 48px;
    }

    .btn-lg {
        height: 3rem;
        padding: 0 1.5rem;
        font-size: 1rem;
    }

    .form-input {
        font-size: 16px; /* Prevent iOS zoom */
        padding: 0.875rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-content {
        padding: 1rem;
    }

    .essay-type-card {
        padding: 0.75rem;
    }

    .essay-type-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .fab {
        bottom: 5rem;
        right: 1rem;
        width: 3rem;
        height: 3rem;
    }
}

/* Touch Optimizations */
.touch-target {
    min-height: 44px;
    min-width: 44px;
}

@media (max-width: 640px) {
    .touch-target {
        min-height: 48px;
        min-width: 48px;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* 高级动画系统 - 完全匹配原项目 */
.fade-in {
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-down {
    animation: fadeInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-right {
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 动画关键帧 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 摇摆动画 */
.wiggle {
    animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(-3deg);
    }
    75% {
        transform: rotate(3deg);
    }
}

/* 渐变动画 */
.gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 加载动画增强 */
.loading-dots {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.loading-dots::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    margin-right: 0.25rem;
    animation: loadingDots 1.4s infinite ease-in-out both;
    animation-delay: -0.32s;
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 30;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 高级动画效果 */
.animate-in {
    animation: animateIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes animateIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬浮效果 */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 磁性按钮效果 */
.btn-magnetic {
    position: relative;
    overflow: hidden;
}

.btn-magnetic::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-magnetic:hover::before {
    left: 100%;
}

/* 粒子背景效果 */
.particles-bg {
    position: relative;
    overflow: hidden;
}

.particles-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

/* 文字打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid #3b82f6;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: #3b82f6;
    }
}

/* 波纹效果 */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
    width: 300px;
    height: 300px;
}

/* 玻璃态效果 */
.glass-morphism {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 霓虹灯效果 */
.neon-glow {
    text-shadow:
        0 0 5px #3b82f6,
        0 0 10px #3b82f6,
        0 0 15px #3b82f6,
        0 0 20px #3b82f6;
    animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        text-shadow:
            0 0 5px #3b82f6,
            0 0 10px #3b82f6,
            0 0 15px #3b82f6,
            0 0 20px #3b82f6;
    }
    50% {
        text-shadow:
            0 0 2px #3b82f6,
            0 0 5px #3b82f6,
            0 0 8px #3b82f6,
            0 0 12px #3b82f6;
    }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }

    .form-input {
        border: 2px solid #000;
    }
}

/* 渐变文字效果 */
.gradient-text-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-text-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 粒子背景效果 */
.particles-bg {
    position: relative;
    overflow: hidden;
}

.particles-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 全局内容显示修复 - 确保所有页面内容都可见 */
main,
header,
nav,
.container,
.sidebar,
h1, h2, h3, h4, h5, h6,
p, span, a, button,
.btn,
.form-input,
.form-textarea {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 强制显示类 - 最高优先级 */
.force-visible,
.force-visible * {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    color: inherit !important;
    -webkit-text-fill-color: inherit !important;
}

/* 特别确保导航和菜单可见 */
#header-container,
#sidebar-container,
#mobile-nav-container {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 论文类型卡片优化 - 防止图标闪烁 */
.essay-type-card {
    transform-origin: center;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, box-shadow;
    opacity: 1 !important;
    visibility: visible !important;
}

.essay-type-card .w-12,
.essay-type-card .w-10 {
    transform-origin: center;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.essay-type-card i {
    display: block !important;
    transform-origin: center;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    opacity: 1 !important;
    visibility: visible !important;
}

.essay-type-card h3 {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 论文类型卡片悬停效果 - 已移至HTML内联样式 */
