/**
 * 基础组件类
 * 提供组件的基础功能和生命周期管理
 */

export class BaseComponent {
    constructor(element, options = {}) {
        this.element = element;
        this.options = { ...this.defaultOptions, ...options };
        this.state = {};
        this.listeners = new Map();
        this.children = new Set();
        this.parent = null;
        this.isDestroyed = false;
        
        this.init();
    }

    // 默认选项
    get defaultOptions() {
        return {
            autoInit: true,
            destroyOnRemove: true,
            trackState: true
        };
    }

    // 初始化组件
    init() {
        if (this.options.autoInit) {
            this.create();
            this.bind();
            this.render();
        }
    }

    // 创建组件结构
    create() {
        // 子类实现
    }

    // 绑定事件
    bind() {
        // 子类实现
    }

    // 渲染组件
    render() {
        // 子类实现
    }

    // 添加事件监听器
    addEventListener(event, handler, options = {}) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        
        const listener = { handler, options };
        this.listeners.get(event).add(listener);
        
        if (this.element) {
            this.element.addEventListener(event, handler, options);
        }
        
        return () => this.removeEventListener(event, handler);
    }

    // 移除事件监听器
    removeEventListener(event, handler) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            for (const listener of eventListeners) {
                if (listener.handler === handler) {
                    eventListeners.delete(listener);
                    if (this.element) {
                        this.element.removeEventListener(event, handler, listener.options);
                    }
                    break;
                }
            }
        }
    }

    // 触发自定义事件
    emit(eventName, detail = {}) {
        if (!this.element) return;
        
        const event = new CustomEvent(eventName, {
            detail,
            bubbles: true,
            cancelable: true
        });
        
        this.element.dispatchEvent(event);
        return event;
    }

    // 设置状态
    setState(newState) {
        if (!this.options.trackState) return;
        
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        this.onStateChange(this.state, oldState);
        this.emit('stateChange', { newState: this.state, oldState });
    }

    // 状态变化回调
    onStateChange(newState, oldState) {
        // 子类可以重写
    }

    // 获取状态
    getState() {
        return { ...this.state };
    }

    // 添加子组件
    addChild(child) {
        if (child instanceof BaseComponent) {
            this.children.add(child);
            child.parent = this;
        }
    }

    // 移除子组件
    removeChild(child) {
        if (this.children.has(child)) {
            this.children.delete(child);
            child.parent = null;
            
            if (this.options.destroyOnRemove) {
                child.destroy();
            }
        }
    }

    // 查找子组件
    findChild(predicate) {
        for (const child of this.children) {
            if (predicate(child)) {
                return child;
            }
            
            const found = child.findChild(predicate);
            if (found) {
                return found;
            }
        }
        return null;
    }

    // 获取所有子组件
    getChildren() {
        return Array.from(this.children);
    }

    // 显示组件
    show() {
        if (this.element) {
            this.element.style.display = '';
            this.element.removeAttribute('hidden');
            this.emit('show');
        }
    }

    // 隐藏组件
    hide() {
        if (this.element) {
            this.element.style.display = 'none';
            this.emit('hide');
        }
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible()) {
            this.hide();
        } else {
            this.show();
        }
    }

    // 检查是否可见
    isVisible() {
        if (!this.element) return false;
        
        const style = window.getComputedStyle(this.element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0';
    }

    // 启用组件
    enable() {
        if (this.element) {
            this.element.removeAttribute('disabled');
            this.element.classList.remove('disabled');
            this.setState({ disabled: false });
            this.emit('enable');
        }
    }

    // 禁用组件
    disable() {
        if (this.element) {
            this.element.setAttribute('disabled', 'true');
            this.element.classList.add('disabled');
            this.setState({ disabled: true });
            this.emit('disable');
        }
    }

    // 检查是否禁用
    isDisabled() {
        return this.element?.hasAttribute('disabled') || this.state.disabled;
    }

    // 添加CSS类
    addClass(className) {
        if (this.element) {
            this.element.classList.add(className);
        }
    }

    // 移除CSS类
    removeClass(className) {
        if (this.element) {
            this.element.classList.remove(className);
        }
    }

    // 切换CSS类
    toggleClass(className, force) {
        if (this.element) {
            return this.element.classList.toggle(className, force);
        }
        return false;
    }

    // 检查是否有CSS类
    hasClass(className) {
        return this.element?.classList.contains(className) || false;
    }

    // 设置属性
    setAttribute(name, value) {
        if (this.element) {
            this.element.setAttribute(name, value);
        }
    }

    // 获取属性
    getAttribute(name) {
        return this.element?.getAttribute(name);
    }

    // 移除属性
    removeAttribute(name) {
        if (this.element) {
            this.element.removeAttribute(name);
        }
    }

    // 设置样式
    setStyle(property, value) {
        if (this.element) {
            this.element.style[property] = value;
        }
    }

    // 获取样式
    getStyle(property) {
        if (!this.element) return null;
        return window.getComputedStyle(this.element)[property];
    }

    // 查找元素
    find(selector) {
        return this.element?.querySelector(selector);
    }

    // 查找所有元素
    findAll(selector) {
        return this.element?.querySelectorAll(selector) || [];
    }

    // 获取元素尺寸
    getBounds() {
        return this.element?.getBoundingClientRect() || null;
    }

    // 滚动到组件
    scrollIntoView(options = {}) {
        if (this.element) {
            this.element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                ...options
            });
        }
    }

    // 验证组件
    validate() {
        // 子类实现验证逻辑
        return true;
    }

    // 重置组件
    reset() {
        // 子类实现重置逻辑
        this.setState({});
    }

    // 刷新组件
    refresh() {
        this.render();
        this.emit('refresh');
    }

    // 销毁组件
    destroy() {
        if (this.isDestroyed) return;
        
        // 销毁所有子组件
        for (const child of this.children) {
            child.destroy();
        }
        this.children.clear();
        
        // 移除所有事件监听器
        for (const [event, listeners] of this.listeners) {
            for (const listener of listeners) {
                if (this.element) {
                    this.element.removeEventListener(event, listener.handler, listener.options);
                }
            }
        }
        this.listeners.clear();
        
        // 从父组件中移除
        if (this.parent) {
            this.parent.removeChild(this);
        }
        
        // 触发销毁事件
        this.emit('destroy');
        
        // 清理引用
        this.element = null;
        this.options = null;
        this.state = null;
        this.parent = null;
        this.isDestroyed = true;
    }

    // 获取组件信息
    getInfo() {
        return {
            type: this.constructor.name,
            element: this.element?.tagName,
            state: this.getState(),
            children: this.children.size,
            isVisible: this.isVisible(),
            isDisabled: this.isDisabled(),
            isDestroyed: this.isDestroyed
        };
    }
}

// 组件工厂
export class ComponentFactory {
    constructor() {
        this.components = new Map();
        this.instances = new WeakMap();
    }

    // 注册组件
    register(name, componentClass) {
        this.components.set(name, componentClass);
    }

    // 创建组件实例
    create(name, element, options = {}) {
        const ComponentClass = this.components.get(name);
        if (!ComponentClass) {
            throw new Error(`Component "${name}" not found`);
        }

        const instance = new ComponentClass(element, options);
        this.instances.set(element, instance);
        
        return instance;
    }

    // 获取组件实例
    getInstance(element) {
        return this.instances.get(element);
    }

    // 销毁组件实例
    destroyInstance(element) {
        const instance = this.instances.get(element);
        if (instance) {
            instance.destroy();
            this.instances.delete(element);
        }
    }

    // 获取所有注册的组件
    getRegisteredComponents() {
        return Array.from(this.components.keys());
    }
}

// 全局组件工厂实例
export const componentFactory = new ComponentFactory();

// 组件装饰器
export function component(name, options = {}) {
    return function(target) {
        componentFactory.register(name, target);
        target.componentName = name;
        target.componentOptions = options;
        return target;
    };
}
