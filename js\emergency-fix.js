// 紧急修复脚本 - 最直接的显示修复
(function() {
    'use strict';

    console.log('🚨 紧急修复脚本启动...');

    function emergencyFix() {
        console.log('🔧 执行紧急修复...');

        // 1. 强制显示所有隐藏的容器
        const hiddenContainers = document.querySelectorAll('.hidden');
        hiddenContainers.forEach(el => {
            // 检查是否是响应式隐藏（如 hidden lg:grid）
            if (el.classList.contains('lg:grid') || 
                el.classList.contains('lg:block') || 
                el.classList.contains('lg:flex')) {
                el.classList.remove('hidden');
                console.log('✅ 移除响应式隐藏:', el);
            }
        });

        // 2. 强制显示所有主要容器
        const containers = [
            '#header-container',
            '#sidebar-container', 
            '#mobile-nav-container',
            'main',
            '.container',
            '.max-w-7xl',
            '#essay-types-desktop',
            '#essay-types-mobile',
            '.essay-types-grid'
        ];

        containers.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                el.style.cssText += `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                `;
                
                // 特别处理grid容器
                if (selector.includes('essay-types') || el.classList.contains('grid')) {
                    el.style.display = 'grid !important';
                }
                
                console.log('✅ 强制显示容器:', selector);
            });
        });

        // 3. 强制显示所有文字内容
        const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, button, label, div');
        textElements.forEach(el => {
            if (el.style.opacity === '0' || 
                el.style.visibility === 'hidden' || 
                el.style.display === 'none') {
                el.style.cssText += `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    color: inherit !important;
                `;
            }
        });

        // 4. 特别处理卡片标题
        document.querySelectorAll('.essay-type-card h3').forEach((h3, index) => {
            h3.style.cssText = `
                opacity: 1 !important;
                visibility: visible !important;
                display: block !important;
                color: #111827 !important;
                font-size: 0.875rem !important;
                line-height: 1.25rem !important;
                font-weight: 700 !important;
                text-align: center !important;
                margin-bottom: 0.25rem !important;
                -webkit-text-fill-color: #111827 !important;
            `;
            console.log(`✅ 修复卡片标题 ${index + 1}: "${h3.textContent}"`);
        });

        // 5. 强制显示所有卡片
        document.querySelectorAll('.essay-type-card').forEach((card, index) => {
            card.style.cssText += `
                opacity: 1 !important;
                visibility: visible !important;
                display: block !important;
            `;
            console.log(`✅ 修复卡片 ${index + 1}`);
        });

        // 6. 修复图标
        document.querySelectorAll('i[data-lucide], .lucide').forEach(icon => {
            icon.style.cssText += `
                opacity: 1 !important;
                visibility: visible !important;
                display: inline-block !important;
            `;
        });

        // 7. 强制初始化组件（如果还没有）
        if (typeof UIComponents !== 'undefined') {
            try {
                UIComponents.initializePage('index');
                console.log('✅ 组件重新初始化完成');
            } catch (e) {
                console.warn('⚠️ 组件初始化失败:', e);
            }
        }

        // 8. 强制初始化图标
        if (typeof lucide !== 'undefined') {
            try {
                lucide.createIcons();
                console.log('✅ 图标重新初始化完成');
            } catch (e) {
                console.warn('⚠️ 图标初始化失败:', e);
            }
        }

        console.log('🎉 紧急修复完成');
    }

    // 立即执行
    emergencyFix();

    // DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', emergencyFix);
    }

    // 页面完全加载后执行
    window.addEventListener('load', emergencyFix);

    // 1秒后再次执行
    setTimeout(emergencyFix, 1000);

    // 3秒后最终执行
    setTimeout(emergencyFix, 3000);

    // 页面可见性变化时执行
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            setTimeout(emergencyFix, 100);
        }
    });

    // 导出到全局
    window.emergencyFix = emergencyFix;

    console.log('✅ 紧急修复脚本加载完成');
})();
