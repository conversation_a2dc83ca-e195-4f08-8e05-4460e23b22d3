/**
 * 按钮组件
 * 可复用的按钮组件，支持多种样式和状态
 */

import { BaseComponent, component } from './base-component.js';

@component('button')
export class <PERSON><PERSON> extends BaseComponent {
    get defaultOptions() {
        return {
            ...super.defaultOptions,
            variant: 'primary', // primary, secondary, outline, ghost, danger
            size: 'md', // sm, md, lg
            disabled: false,
            loading: false,
            icon: null,
            iconPosition: 'left', // left, right
            ripple: true,
            href: null, // 如果提供，渲染为链接
            target: '_self'
        };
    }

    create() {
        if (!this.element) {
            this.element = document.createElement(this.options.href ? 'a' : 'button');
        }

        this.setupElement();
        this.setupContent();
        this.setupRipple();
    }

    setupElement() {
        const { variant, size, disabled, href, target } = this.options;

        // 设置基础类名
        this.element.className = `btn btn-${variant} btn-${size}`;

        // 设置属性
        if (href) {
            this.element.href = href;
            this.element.target = target;
        } else {
            this.element.type = 'button';
        }

        if (disabled) {
            this.disable();
        }

        // 设置可访问性属性
        this.element.setAttribute('role', 'button');
        this.element.setAttribute('tabindex', disabled ? '-1' : '0');
    }

    setupContent() {
        const { icon, iconPosition } = this.options;
        const text = this.element.textContent || this.options.text || '';

        this.element.innerHTML = '';

        // 创建内容容器
        const content = document.createElement('span');
        content.className = 'btn-content';

        // 添加图标
        if (icon) {
            const iconElement = this.createIcon(icon);
            if (iconPosition === 'left') {
                content.appendChild(iconElement);
            }
        }

        // 添加文本
        if (text) {
            const textElement = document.createElement('span');
            textElement.className = 'btn-text';
            textElement.textContent = text;
            content.appendChild(textElement);
        }

        // 添加右侧图标
        if (icon && iconPosition === 'right') {
            const iconElement = this.createIcon(icon);
            content.appendChild(iconElement);
        }

        // 添加加载指示器
        const loadingElement = this.createLoadingIndicator();
        content.appendChild(loadingElement);

        this.element.appendChild(content);
    }

    createIcon(iconName) {
        const icon = document.createElement('i');
        icon.className = 'btn-icon';
        icon.setAttribute('data-lucide', iconName);
        return icon;
    }

    createLoadingIndicator() {
        const loading = document.createElement('span');
        loading.className = 'btn-loading';
        loading.innerHTML = '<span class="spinner"></span>';
        loading.style.display = 'none';
        return loading;
    }

    setupRipple() {
        if (!this.options.ripple) return;

        this.element.classList.add('ripple');
    }

    bind() {
        // 点击事件
        this.addEventListener('click', this.handleClick.bind(this));

        // 键盘事件
        this.addEventListener('keydown', this.handleKeydown.bind(this));

        // 焦点事件
        this.addEventListener('focus', this.handleFocus.bind(this));
        this.addEventListener('blur', this.handleBlur.bind(this));

        // 鼠标事件（用于ripple效果）
        if (this.options.ripple) {
            this.addEventListener('mousedown', this.handleMouseDown.bind(this));
        }
    }

    handleClick(event) {
        if (this.isDisabled() || this.state.loading) {
            event.preventDefault();
            event.stopPropagation();
            return;
        }

        this.emit('click', { originalEvent: event });
    }

    handleKeydown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.element.click();
        }
    }

    handleFocus() {
        this.addClass('focused');
        this.emit('focus');
    }

    handleBlur() {
        this.removeClass('focused');
        this.emit('blur');
    }

    handleMouseDown(event) {
        this.createRippleEffect(event);
    }

    createRippleEffect(event) {
        const rect = this.element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.className = 'ripple-effect';
        ripple.style.cssText = `
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
        `;

        this.element.appendChild(ripple);

        // 移除ripple效果
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // 设置加载状态
    setLoading(loading = true) {
        this.setState({ loading });

        const loadingElement = this.find('.btn-loading');
        const contentElement = this.find('.btn-content');

        if (loading) {
            this.disable();
            loadingElement.style.display = 'inline-flex';
            contentElement.style.opacity = '0.7';
            this.addClass('loading');
        } else {
            this.enable();
            loadingElement.style.display = 'none';
            contentElement.style.opacity = '1';
            this.removeClass('loading');
        }

        this.emit('loadingChange', { loading });
    }

    // 设置文本
    setText(text) {
        const textElement = this.find('.btn-text');
        if (textElement) {
            textElement.textContent = text;
        }
        this.emit('textChange', { text });
    }

    // 设置图标
    setIcon(iconName) {
        const iconElement = this.find('.btn-icon');
        if (iconElement) {
            iconElement.setAttribute('data-lucide', iconName);
            // 重新初始化图标
            if (window.lucide) {
                window.lucide.createIcons();
            }
        }
        this.emit('iconChange', { icon: iconName });
    }

    // 设置变体
    setVariant(variant) {
        // 移除旧的变体类
        const oldVariant = this.options.variant;
        this.removeClass(`btn-${oldVariant}`);

        // 添加新的变体类
        this.addClass(`btn-${variant}`);
        this.options.variant = variant;

        this.emit('variantChange', { variant, oldVariant });
    }

    // 设置尺寸
    setSize(size) {
        // 移除旧的尺寸类
        const oldSize = this.options.size;
        this.removeClass(`btn-${oldSize}`);

        // 添加新的尺寸类
        this.addClass(`btn-${size}`);
        this.options.size = size;

        this.emit('sizeChange', { size, oldSize });
    }

    // 禁用按钮
    disable() {
        super.disable();
        this.element.setAttribute('aria-disabled', 'true');
        this.element.tabIndex = -1;
    }

    // 启用按钮
    enable() {
        super.enable();
        this.element.removeAttribute('aria-disabled');
        this.element.tabIndex = 0;
    }

    // 模拟点击
    click() {
        if (!this.isDisabled() && !this.state.loading) {
            this.element.click();
        }
    }

    // 获取按钮文本
    getText() {
        const textElement = this.find('.btn-text');
        return textElement ? textElement.textContent : '';
    }

    // 获取按钮图标
    getIcon() {
        const iconElement = this.find('.btn-icon');
        return iconElement ? iconElement.getAttribute('data-lucide') : null;
    }

    // 验证按钮
    validate() {
        return !this.isDisabled() && !this.state.loading;
    }

    // 重置按钮
    reset() {
        this.setLoading(false);
        this.enable();
        this.setState({});
    }
}

// 按钮组组件
@component('button-group')
export class ButtonGroup extends BaseComponent {
    get defaultOptions() {
        return {
            ...super.defaultOptions,
            orientation: 'horizontal', // horizontal, vertical
            size: 'md',
            variant: 'primary',
            exclusive: false, // 是否只能选择一个
            multiple: false, // 是否可以多选
            required: false // 是否必须选择一个
        };
    }

    create() {
        if (!this.element) {
            this.element = document.createElement('div');
        }

        this.element.className = `btn-group btn-group-${this.options.orientation}`;
        this.element.setAttribute('role', 'group');

        this.buttons = [];
        this.selectedButtons = new Set();
    }

    bind() {
        // 监听按钮点击
        this.addEventListener('click', this.handleButtonClick.bind(this));
    }

    handleButtonClick(event) {
        const button = event.target.closest('.btn');
        if (!button) return;

        const buttonComponent = this.findButtonComponent(button);
        if (!buttonComponent) return;

        if (this.options.exclusive) {
            this.selectExclusive(buttonComponent);
        } else if (this.options.multiple) {
            this.toggleSelection(buttonComponent);
        } else {
            this.selectSingle(buttonComponent);
        }
    }

    // 添加按钮
    addButton(button) {
        if (button instanceof Button) {
            this.buttons.push(button);
            this.addChild(button);
            this.element.appendChild(button.element);
        } else if (typeof button === 'string') {
            // 创建新按钮
            const buttonElement = document.createElement('button');
            buttonElement.textContent = button;
            const buttonComponent = new Button(buttonElement, {
                variant: this.options.variant,
                size: this.options.size
            });
            this.addButton(buttonComponent);
        }
    }

    // 移除按钮
    removeButton(button) {
        const index = this.buttons.indexOf(button);
        if (index > -1) {
            this.buttons.splice(index, 1);
            this.removeChild(button);
            this.selectedButtons.delete(button);
        }
    }

    // 查找按钮组件
    findButtonComponent(element) {
        return this.buttons.find(btn => btn.element === element);
    }

    // 独占选择
    selectExclusive(button) {
        // 取消所有选择
        this.selectedButtons.forEach(btn => {
            btn.removeClass('selected');
            btn.setAttribute('aria-pressed', 'false');
        });
        this.selectedButtons.clear();

        // 选择当前按钮
        button.addClass('selected');
        button.setAttribute('aria-pressed', 'true');
        this.selectedButtons.add(button);

        this.emit('selectionChange', { 
            selected: Array.from(this.selectedButtons),
            button 
        });
    }

    // 切换选择
    toggleSelection(button) {
        if (this.selectedButtons.has(button)) {
            button.removeClass('selected');
            button.setAttribute('aria-pressed', 'false');
            this.selectedButtons.delete(button);
        } else {
            button.addClass('selected');
            button.setAttribute('aria-pressed', 'true');
            this.selectedButtons.add(button);
        }

        this.emit('selectionChange', { 
            selected: Array.from(this.selectedButtons),
            button 
        });
    }

    // 单选
    selectSingle(button) {
        this.selectExclusive(button);
    }

    // 获取选中的按钮
    getSelected() {
        return Array.from(this.selectedButtons);
    }

    // 清除选择
    clearSelection() {
        this.selectedButtons.forEach(btn => {
            btn.removeClass('selected');
            btn.setAttribute('aria-pressed', 'false');
        });
        this.selectedButtons.clear();

        this.emit('selectionChange', { 
            selected: [],
            button: null 
        });
    }

    // 验证选择
    validate() {
        if (this.options.required && this.selectedButtons.size === 0) {
            return false;
        }
        return true;
    }
}
