<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文查重 - 博晓文</title>
    <meta name="description" content="专业的学术不端检测系统，支持多数据库对比分析">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-6xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8 lg:mb-12 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                论文查重检测
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                专业的学术不端检测系统，支持多数据库对比分析
                                <span class="inline-block ml-2 text-blue-600">🔍</span>
                            </p>

                            <!-- 特色标签 -->
                            <div class="flex flex-wrap gap-2 mt-4">
                                <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full font-medium">
                                    🎯 99.9%准确率
                                </span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
                                    ⚡ 3秒极速检测
                                </span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                    📊 详细报告
                                </span>
                            </div>
                        </div>
                        <!-- 统计卡片 -->
                        <div class="hidden lg:flex space-x-6 xl:space-x-8">
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="target" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-red-600">99.9%</div>
                                <div class="text-sm text-gray-500">检测准确率</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-green-600">3秒</div>
                                <div class="text-sm text-gray-500">平均检测时间</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="database" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-purple-600">5000万+</div>
                                <div class="text-sm text-gray-500">文献数据库</div>
                            </div>
                        </div>
                    </div>

                    <!-- 移动端统计 -->
                    <div class="lg:hidden grid grid-cols-3 gap-3 mt-6">
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-red-600">99.9%</div>
                            <div class="text-xs text-gray-500">检测准确率</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-green-600">3秒</div>
                            <div class="text-xs text-gray-500">平均检测时间</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-purple-600">5000万+</div>
                            <div class="text-xs text-gray-500">文献数据库</div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 查重输入区域 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-xl p-6">
                                <h2 class="text-xl font-bold flex items-center gap-3">
                                    <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="file-text" class="w-5 h-5"></i>
                                    </div>
                                    智能查重检测
                                </h2>
                                <p class="text-blue-100 mt-2">输入您的论文内容，AI将进行全面的查重分析</p>
                            </div>
                            <div class="p-6 space-y-6">
                                <!-- Tabs -->
                                <div class="flex border-b border-gray-200">
                                    <button class="py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm tab-button active" data-tab="text">
                                        文本输入
                                    </button>
                                    <button class="py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="file">
                                        文件上传
                                    </button>
                                </div>

                                <!-- Text Input Tab -->
                                <div id="text-tab" class="tab-content space-y-4">
                                    <textarea id="text-input" placeholder="请输入要检测的论文内容..." class="form-input form-textarea min-h-[300px] resize-none"></textarea>
                                    <div class="flex justify-between items-center text-sm text-gray-500">
                                        <span>字数: <span id="char-count">0</span></span>
                                        <span>建议字数: 1000-50000字</span>
                                    </div>
                                </div>

                                <!-- File Upload Tab -->
                                <div id="file-tab" class="tab-content space-y-4 hidden">
                                    <div class="border-2 border-dashed border-blue-300 rounded-xl p-8 sm:p-12 text-center bg-gradient-to-br from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 group" id="upload-area">
                                        <div class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <i data-lucide="upload" class="w-8 h-8 sm:w-10 sm:h-10 text-white"></i>
                                        </div>
                                        <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-2">上传论文文件</h3>
                                        <p class="text-gray-600 mb-2">拖拽文件到此处或点击上传</p>
                                        <p class="text-sm text-gray-500 mb-4 sm:mb-6">支持 Word(.doc, .docx)、PDF(.pdf)、文本(.txt) 格式，最大20MB</p>

                                        <!-- 支持格式图标 -->
                                        <div class="flex justify-center gap-4 mb-6">
                                            <div class="flex flex-col items-center">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-1">
                                                    <i data-lucide="file-text" class="w-4 h-4 text-blue-600"></i>
                                                </div>
                                                <span class="text-xs text-gray-500">Word</span>
                                            </div>
                                            <div class="flex flex-col items-center">
                                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mb-1">
                                                    <i data-lucide="file" class="w-4 h-4 text-red-600"></i>
                                                </div>
                                                <span class="text-xs text-gray-500">PDF</span>
                                            </div>
                                            <div class="flex flex-col items-center">
                                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-1">
                                                    <i data-lucide="file-text" class="w-4 h-4 text-green-600"></i>
                                                </div>
                                                <span class="text-xs text-gray-500">TXT</span>
                                            </div>
                                        </div>

                                        <button class="btn btn-primary btn-lg group" onclick="document.getElementById('file-input').click()">
                                            <i data-lucide="upload" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                            选择文件上传
                                        </button>
                                        <input type="file" id="file-input" class="hidden" accept=".doc,.docx,.pdf,.txt" onchange="handleFileUpload(this)">
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-500">
                                        <span class="font-medium">检测范围:</span> 知网、万方、维普、互联网资源
                                    </div>
                                    <button id="check-btn" onclick="startPlagiarismCheck()" class="btn btn-primary" disabled>
                                        <i data-lucide="check-circle" class="w-5 h-5 mr-2"></i>
                                        开始智能查重
                                    </button>
                                </div>

                                <!-- Progress -->
                                <div id="progress-container" class="space-y-4 hidden">
                                    <div class="text-center">
                                        <div class="inline-flex items-center space-x-2 text-blue-600">
                                            <div class="spinner"></div>
                                            <span id="progress-text">正在检测中，请稍候...</span>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="text-sm text-gray-500 text-center" id="progress-detail">正在与知网数据库对比...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="space-y-6">
                        <!-- 检测统计 -->
                        <div class="bg-white rounded-xl shadow-lg p-6 bg-gradient-to-br from-green-50 to-green-100">
                            <h3 class="text-lg font-bold flex items-center gap-2 mb-4">
                                <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="check-circle" class="w-4 h-4 text-white"></i>
                                </div>
                                检测统计
                            </h3>
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">本月检测次数</span>
                                    <span class="font-bold text-gray-900">15/100</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">剩余次数</span>
                                    <span class="font-bold text-green-600">85次</span>
                                </div>
                                <div class="progress-bar h-3">
                                    <div class="progress-fill bg-gradient-to-r from-green-500 to-green-600" style="width: 15%"></div>
                                </div>
                                <button class="btn btn-success w-full">
                                    升级套餐
                                </button>
                            </div>
                        </div>

                        <!-- 查重结果 -->
                        <div id="result-container" class="bg-white rounded-xl shadow-lg hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-bold flex items-center">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-green-600"></i>
                                    检测结果
                                </h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="text-center">
                                    <div id="similarity-rate" class="text-4xl font-bold text-green-600">0%</div>
                                    <span id="similarity-badge" class="badge badge-success">通过</span>
                                </div>

                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">总字数</span>
                                        <span id="total-words">0</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">重复字数</span>
                                        <span class="text-red-600" id="duplicate-words">0</span>
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <h4 class="font-semibold">数据库对比结果</h4>
                                    <div id="sources-list">
                                        <!-- Sources will be populated by JavaScript -->
                                    </div>
                                </div>

                                <button class="btn btn-secondary w-full" onclick="showDetailedReport()">
                                    查看详细报告
                                </button>
                            </div>
                        </div>

                        <!-- 重复内容详情 -->
                        <div id="details-container" class="bg-white rounded-xl shadow-lg hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-bold flex items-center">
                                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-yellow-600"></i>
                                    重复内容
                                </h3>
                            </div>
                            <div class="p-6 space-y-4" id="details-list">
                                <!-- Details will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/global-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/plagiarism.js"></script>

    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('plagiarism');
        });
    </script>
</body>
</html>
