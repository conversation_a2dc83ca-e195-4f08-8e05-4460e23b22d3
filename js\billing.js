// 套餐管理页面状态
let billingState = {
    currentTab: 'plans',
    currentPlan: 'pro',
    usageData: {},
    billingHistory: []
};

// 套餐配置
const PLANS = [
    {
        id: 'basic',
        name: '基础版',
        price: 29,
        period: '月',
        color: 'from-gray-500 to-gray-600',
        popular: false,
        features: [
            '每月10篇论文生成',
            '基础查重功能',
            '标准AI模型',
            '邮件客服支持',
            '基础模板库'
        ],
        limits: {
            essays: 10,
            plagiarism: 20,
            aigc: 5
        }
    },
    {
        id: 'pro',
        name: '专业版',
        price: 99,
        period: '月',
        color: 'from-blue-500 to-purple-600',
        popular: true,
        features: [
            '每月50篇论文生成',
            '高级查重功能',
            '高级AI模型',
            '优先客服支持',
            '专业模板库',
            'AIGC智能降重',
            '批量处理功能'
        ],
        limits: {
            essays: 50,
            plagiarism: 100,
            aigc: 30
        }
    },
    {
        id: 'enterprise',
        name: '企业版',
        price: 299,
        period: '月',
        color: 'from-purple-500 to-pink-600',
        popular: false,
        features: [
            '无限论文生成',
            '企业级查重',
            '顶级AI模型',
            '专属客服经理',
            '定制模板库',
            '高级AIGC功能',
            'API接口访问',
            '团队协作功能',
            '数据分析报告'
        ],
        limits: {
            essays: -1, // 无限
            plagiarism: -1,
            aigc: -1
        }
    }
];

// 使用统计数据
const USAGE_DATA = {
    essays: { used: 15, limit: 50, name: '论文生成' },
    plagiarism: { used: 8, limit: 100, name: '查重次数' },
    aigc: { used: 12, limit: 30, name: 'AIGC降重' },
    api: { used: 1250, limit: 10000, name: 'API调用' }
};

// 账单历史数据
const BILLING_HISTORY = [
    {
        id: 1,
        date: '2024-01-15',
        plan: '专业版',
        amount: 99.00,
        status: 'paid',
        invoice: 'INV-2024-001'
    },
    {
        id: 2,
        date: '2023-12-15',
        plan: '专业版',
        amount: 99.00,
        status: 'paid',
        invoice: 'INV-2023-012'
    },
    {
        id: 3,
        date: '2023-11-15',
        plan: '基础版',
        amount: 29.00,
        status: 'paid',
        invoice: 'INV-2023-011'
    }
];

// DOM 元素
let billingElements = {};

// 初始化套餐管理页面
document.addEventListener('DOMContentLoaded', function() {
    initializeBillingElements();
    initializeLucideIcons();
    renderPlans();
    renderUsageStats();
    renderSubscriptionInfo();
    renderBillingHistory();
    bindBillingEvents();
    loadBillingData();
});

// 初始化DOM元素引用
function initializeBillingElements() {
    billingElements = {
        tabButtons: document.querySelectorAll('.tab-button'),
        tabContents: document.querySelectorAll('.tab-content'),
        plansContainer: document.getElementById('plans-container'),
        usageStats: document.getElementById('usage-stats'),
        subscriptionInfo: document.getElementById('subscription-info'),
        billingTableBody: document.getElementById('billing-table-body'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn')
    };
}

// 渲染套餐卡片
function renderPlans() {
    if (!billingElements.plansContainer) return;
    
    const html = PLANS.map((plan, index) => `
        <div class="plan-card relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 ${
            plan.popular ? 'ring-2 ring-blue-500 transform scale-105' : ''
        } fade-in-up" style="animation-delay: ${index * 0.1}s;">
            ${plan.popular ? `
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                        🔥 最受欢迎
                    </span>
                </div>
            ` : ''}
            
            <div class="p-6">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gradient-to-r ${plan.color} rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <i data-lucide="${plan.id === 'basic' ? 'star' : plan.id === 'pro' ? 'crown' : 'diamond'}" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">${plan.name}</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-4xl font-bold bg-gradient-to-r ${plan.color} bg-clip-text text-transparent">¥${plan.price}</span>
                        <span class="text-gray-500 ml-2">/${plan.period}</span>
                    </div>
                </div>
                
                <ul class="space-y-3 mb-8">
                    ${plan.features.map(feature => `
                        <li class="flex items-center text-sm">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-3 flex-shrink-0"></i>
                            <span class="text-gray-700">${feature}</span>
                        </li>
                    `).join('')}
                </ul>
                
                <button onclick="selectPlan('${plan.id}')" class="btn ${
                    billingState.currentPlan === plan.id 
                        ? 'btn-secondary cursor-not-allowed' 
                        : 'btn-primary'
                } w-full btn-lg">
                    ${billingState.currentPlan === plan.id ? '当前套餐' : '选择套餐'}
                </button>
            </div>
        </div>
    `).join('');
    
    billingElements.plansContainer.innerHTML = html;
    initializeLucideIcons();
}

// 渲染使用统计
function renderUsageStats() {
    if (!billingElements.usageStats) return;
    
    const html = Object.entries(USAGE_DATA).map(([key, data]) => {
        const percentage = data.limit === -1 ? 0 : (data.used / data.limit) * 100;
        const isUnlimited = data.limit === -1;
        
        return `
            <div class="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                <div class="flex justify-between mb-3">
                    <span class="text-sm font-medium text-gray-700">${data.name}</span>
                    <span class="text-sm font-bold text-blue-600">
                        ${data.used}${isUnlimited ? '' : `/${data.limit}`}
                    </span>
                </div>
                ${!isUnlimited ? `
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300" style="width: ${percentage}%"></div>
                    </div>
                    <p class="text-xs text-gray-500">还可使用 ${data.limit - data.used} 次</p>
                ` : `
                    <div class="text-xs text-green-600 font-medium">✨ 无限使用</div>
                `}
            </div>
        `;
    }).join('');
    
    billingElements.usageStats.innerHTML = html;
}

// 渲染订阅信息
function renderSubscriptionInfo() {
    if (!billingElements.subscriptionInfo) return;
    
    const currentPlan = PLANS.find(p => p.id === billingState.currentPlan);
    const nextBillingDate = new Date();
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    
    const html = `
        <div class="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
            <span class="text-gray-700 font-medium">当前套餐</span>
            <span class="px-3 py-1 bg-gradient-to-r ${currentPlan.color} text-white rounded-full text-sm font-bold">
                ${currentPlan.name}
            </span>
        </div>
        <div class="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg">
            <span class="text-gray-700 font-medium">订阅状态</span>
            <span class="px-3 py-1 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-full text-sm font-bold">
                活跃
            </span>
        </div>
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <span class="text-gray-700 font-medium">下次续费</span>
            <span class="font-bold text-gray-900">${nextBillingDate.toISOString().slice(0, 10)}</span>
        </div>
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <span class="text-gray-700 font-medium">续费金额</span>
            <span class="font-bold text-gray-900">¥${currentPlan.price}.00</span>
        </div>
        <div class="pt-4 space-y-3">
            <button onclick="manageBilling()" class="btn btn-primary w-full">
                <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                管理订阅
            </button>
            <button onclick="cancelSubscription()" class="btn btn-secondary w-full">
                <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                取消订阅
            </button>
        </div>
    `;
    
    billingElements.subscriptionInfo.innerHTML = html;
    initializeLucideIcons();
}

// 渲染账单历史
function renderBillingHistory() {
    if (!billingElements.billingTableBody) return;
    
    const html = BILLING_HISTORY.map(bill => `
        <tr class="hover:bg-gray-50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${bill.date}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium">
                    ${bill.plan}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                ¥${bill.amount.toFixed(2)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 ${
                    bill.status === 'paid' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                } text-xs rounded-full font-medium">
                    ${bill.status === 'paid' ? '已支付' : '待支付'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button onclick="downloadInvoice('${bill.invoice}')" class="text-blue-600 hover:text-blue-900 mr-3">
                    <i data-lucide="download" class="w-4 h-4"></i>
                </button>
                <button onclick="viewInvoice('${bill.invoice}')" class="text-green-600 hover:text-green-900">
                    <i data-lucide="eye" class="w-4 h-4"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    billingElements.billingTableBody.innerHTML = html;
    initializeLucideIcons();
}

// 绑定套餐管理页面事件
function bindBillingEvents() {
    // 标签页切换
    billingElements.tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 移动端菜单
    if (billingElements.mobileMenuBtn) {
        billingElements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            billingElements.sidebar && 
            !billingElements.sidebar.contains(e.target) && 
            billingElements.mobileMenuBtn &&
            !billingElements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 切换标签页
function switchTab(tab) {
    billingState.currentTab = tab;
    
    // 更新标签页样式
    billingElements.tabButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('border-pink-500', 'text-pink-600', 'active');
            button.classList.remove('border-transparent', 'text-gray-500');
        } else {
            button.classList.remove('border-pink-500', 'text-pink-600', 'active');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });
    
    // 切换内容区域
    billingElements.tabContents.forEach(content => {
        if (content.id === `${tab}-tab`) {
            content.classList.remove('hidden');
        } else {
            content.classList.add('hidden');
        }
    });
}

// 选择套餐
function selectPlan(planId) {
    if (billingState.currentPlan === planId) return;
    
    const plan = PLANS.find(p => p.id === planId);
    if (!plan) return;
    
    if (confirm(`确定要切换到${plan.name}吗？\n价格：¥${plan.price}/${plan.period}`)) {
        billingState.currentPlan = planId;
        renderPlans();
        renderSubscriptionInfo();
        showToast(`已切换到${plan.name}`, 'success');
        saveBillingData();
    }
}

// 管理订阅
function manageBilling() {
    showToast('订阅管理功能开发中...', 'info');
}

// 取消订阅
function cancelSubscription() {
    if (confirm('确定要取消订阅吗？取消后将无法使用高级功能。')) {
        showToast('订阅取消功能开发中...', 'info');
    }
}

// 下载发票
function downloadInvoice(invoiceId) {
    showToast(`正在下载发票 ${invoiceId}...`, 'info');
    
    // 模拟下载
    setTimeout(() => {
        showToast('发票下载成功', 'success');
    }, 1000);
}

// 查看发票
function viewInvoice(invoiceId) {
    showToast(`正在查看发票 ${invoiceId}...`, 'info');
}

// 数据持久化
function saveBillingData() {
    const data = {
        currentPlan: billingState.currentPlan,
        currentTab: billingState.currentTab
    };
    
    try {
        localStorage.setItem('billingData', JSON.stringify(data));
    } catch (error) {
        console.error('Failed to save billing data:', error);
    }
}

function loadBillingData() {
    try {
        const savedData = localStorage.getItem('billingData');
        if (savedData) {
            const data = JSON.parse(savedData);
            
            if (data.currentPlan) {
                billingState.currentPlan = data.currentPlan;
            }
            
            if (data.currentTab) {
                switchTab(data.currentTab);
            }
        }
    } catch (error) {
        console.error('Failed to load billing data:', error);
    }
}

// 移动端菜单控制
function toggleMobileMenu() {
    if (billingElements.sidebar) {
        billingElements.sidebar.classList.toggle('-translate-x-full');
    }
}

function closeMobileMenu() {
    if (billingElements.sidebar) {
        billingElements.sidebar.classList.add('-translate-x-full');
    }
}

// 工具函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
