/**
 * 性能监控仪表板
 * 整合所有性能优化功能，提供统一的监控和控制界面
 */

import { optimizedLoader } from './optimized-loader.js';
import { pageTransition } from './page-transition.js';
import { cssOptimizer } from './css-optimizer.js';
import { jsOptimizer } from './js-optimizer.js';
import { mediaOptimizer } from './media-optimizer.js';
import { animationOptimizer } from './animation-optimizer.js';
import { mobileOptimizer } from './mobile-optimizer.js';

class PerformanceDashboard {
    constructor() {
        this.isVisible = false;
        this.metrics = new Map();
        this.observers = new Map();
        this.updateInterval = null;
        
        this.init();
    }

    init() {
        this.createDashboard();
        this.setupPerformanceMonitoring();
        this.setupKeyboardShortcuts();
        this.startMetricsCollection();
    }

    // 创建仪表板UI
    createDashboard() {
        const dashboard = document.createElement('div');
        dashboard.id = 'performance-dashboard';
        dashboard.innerHTML = `
            <div class="dashboard-header">
                <h3>🚀 性能监控仪表板</h3>
                <button class="dashboard-close" onclick="performanceDashboard.hide()">×</button>
            </div>
            
            <div class="dashboard-content">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <h4>📊 核心指标</h4>
                        <div id="core-metrics"></div>
                    </div>
                    
                    <div class="metric-card">
                        <h4>🎯 资源加载</h4>
                        <div id="resource-metrics"></div>
                    </div>
                    
                    <div class="metric-card">
                        <h4>🎨 渲染性能</h4>
                        <div id="render-metrics"></div>
                    </div>
                    
                    <div class="metric-card">
                        <h4>📱 移动端优化</h4>
                        <div id="mobile-metrics"></div>
                    </div>
                </div>
                
                <div class="controls-section">
                    <h4>🎛️ 性能控制</h4>
                    <div class="controls-grid">
                        <button onclick="performanceDashboard.optimizeAll()">全面优化</button>
                        <button onclick="performanceDashboard.clearCaches()">清理缓存</button>
                        <button onclick="performanceDashboard.runDiagnostics()">性能诊断</button>
                        <button onclick="performanceDashboard.exportReport()">导出报告</button>
                    </div>
                </div>
                
                <div class="recommendations-section">
                    <h4>💡 优化建议</h4>
                    <div id="recommendations"></div>
                </div>
            </div>
        `;
        
        this.addDashboardStyles();
        document.body.appendChild(dashboard);
    }

    // 添加仪表板样式
    addDashboardStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #performance-dashboard {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 400px;
                max-height: 80vh;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                transform: translateX(420px);
                transition: transform 0.3s ease;
                overflow: hidden;
            }
            
            #performance-dashboard.visible {
                transform: translateX(0);
            }
            
            .dashboard-header {
                background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                color: white;
                padding: 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .dashboard-header h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }
            
            .dashboard-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
            }
            
            .dashboard-close:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .dashboard-content {
                padding: 16px;
                max-height: calc(80vh - 60px);
                overflow-y: auto;
            }
            
            .metrics-grid {
                display: grid;
                gap: 12px;
                margin-bottom: 20px;
            }
            
            .metric-card {
                background: #f8fafc;
                border-radius: 8px;
                padding: 12px;
                border: 1px solid #e2e8f0;
            }
            
            .metric-card h4 {
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
                color: #374151;
            }
            
            .metric-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
                font-size: 12px;
            }
            
            .metric-value {
                font-weight: 500;
                color: #059669;
            }
            
            .metric-value.warning {
                color: #d97706;
            }
            
            .metric-value.error {
                color: #dc2626;
            }
            
            .controls-section {
                margin-bottom: 20px;
            }
            
            .controls-section h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                font-weight: 600;
                color: #374151;
            }
            
            .controls-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }
            
            .controls-grid button {
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.2s;
            }
            
            .controls-grid button:hover {
                background: #2563eb;
            }
            
            .recommendations-section h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                font-weight: 600;
                color: #374151;
            }
            
            .recommendation-item {
                background: #fef3c7;
                border: 1px solid #fcd34d;
                border-radius: 6px;
                padding: 8px;
                margin-bottom: 8px;
                font-size: 12px;
                color: #92400e;
            }
            
            @media (max-width: 768px) {
                #performance-dashboard {
                    width: calc(100vw - 40px);
                    right: 20px;
                    left: 20px;
                    transform: translateY(100vh);
                }
                
                #performance-dashboard.visible {
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 设置性能监控
    setupPerformanceMonitoring() {
        // 监控Core Web Vitals
        this.monitorCoreWebVitals();
        
        // 监控资源加载
        this.monitorResourceLoading();
        
        // 监控内存使用
        this.monitorMemoryUsage();
        
        // 监控网络状况
        this.monitorNetworkConditions();
    }

    // 监控Core Web Vitals
    monitorCoreWebVitals() {
        if ('PerformanceObserver' in window) {
            // 监控LCP (Largest Contentful Paint)
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.set('LCP', lastEntry.startTime);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            
            // 监控FID (First Input Delay)
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.metrics.set('FID', entry.processingStart - entry.startTime);
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
            
            // 监控CLS (Cumulative Layout Shift)
            let clsValue = 0;
            const clsObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                this.metrics.set('CLS', clsValue);
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
        }
    }

    // 监控资源加载
    monitorResourceLoading() {
        if ('PerformanceObserver' in window) {
            const resourceObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                let totalSize = 0;
                let totalDuration = 0;
                const resourceTypes = {};
                
                entries.forEach(entry => {
                    totalSize += entry.transferSize || 0;
                    totalDuration += entry.duration;
                    
                    const type = this.getResourceType(entry.name);
                    resourceTypes[type] = (resourceTypes[type] || 0) + 1;
                });
                
                this.metrics.set('totalResourceSize', totalSize);
                this.metrics.set('avgResourceDuration', totalDuration / entries.length);
                this.metrics.set('resourceTypes', resourceTypes);
            });
            
            resourceObserver.observe({ entryTypes: ['resource'] });
        }
    }

    // 获取资源类型
    getResourceType(url) {
        if (url.includes('.css')) return 'CSS';
        if (url.includes('.js')) return 'JavaScript';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'Image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'Font';
        return 'Other';
    }

    // 监控内存使用
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.metrics.set('memoryUsed', memory.usedJSHeapSize);
                this.metrics.set('memoryTotal', memory.totalJSHeapSize);
                this.metrics.set('memoryLimit', memory.jsHeapSizeLimit);
            }, 5000);
        }
    }

    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + P 打开性能仪表板
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                this.toggle();
            }
        });
    }

    // 开始指标收集
    startMetricsCollection() {
        this.updateInterval = setInterval(() => {
            this.updateMetrics();
        }, 2000);
    }

    // 更新指标
    updateMetrics() {
        if (!this.isVisible) return;
        
        this.updateCoreMetrics();
        this.updateResourceMetrics();
        this.updateRenderMetrics();
        this.updateMobileMetrics();
        this.updateRecommendations();
    }

    // 更新核心指标
    updateCoreMetrics() {
        const container = document.getElementById('core-metrics');
        if (!container) return;
        
        const lcp = this.metrics.get('LCP') || 0;
        const fid = this.metrics.get('FID') || 0;
        const cls = this.metrics.get('CLS') || 0;
        
        container.innerHTML = `
            <div class="metric-item">
                <span>LCP:</span>
                <span class="metric-value ${lcp > 2500 ? 'error' : lcp > 1500 ? 'warning' : ''}">${lcp.toFixed(0)}ms</span>
            </div>
            <div class="metric-item">
                <span>FID:</span>
                <span class="metric-value ${fid > 100 ? 'error' : fid > 50 ? 'warning' : ''}">${fid.toFixed(0)}ms</span>
            </div>
            <div class="metric-item">
                <span>CLS:</span>
                <span class="metric-value ${cls > 0.25 ? 'error' : cls > 0.1 ? 'warning' : ''}">${cls.toFixed(3)}</span>
            </div>
        `;
    }

    // 更新资源指标
    updateResourceMetrics() {
        const container = document.getElementById('resource-metrics');
        if (!container) return;
        
        const loaderStatus = optimizedLoader.getLoadingStatus();
        const mediaStatus = mediaOptimizer.getPerformanceReport();
        
        container.innerHTML = `
            <div class="metric-item">
                <span>已加载资源:</span>
                <span class="metric-value">${loaderStatus.loaded.length}</span>
            </div>
            <div class="metric-item">
                <span>图片缓存:</span>
                <span class="metric-value">${mediaStatus.cacheSize}</span>
            </div>
            <div class="metric-item">
                <span>加载进度:</span>
                <span class="metric-value">${mediaStatus.loadingProgress}</span>
            </div>
        `;
    }

    // 更新渲染指标
    updateRenderMetrics() {
        const container = document.getElementById('render-metrics');
        if (!container) return;
        
        const animationStatus = animationOptimizer.getPerformanceReport();
        const cssReport = cssOptimizer.generateUsageReport();
        
        container.innerHTML = `
            <div class="metric-item">
                <span>活跃动画:</span>
                <span class="metric-value">${animationStatus.activeAnimations}</span>
            </div>
            <div class="metric-item">
                <span>CSS使用率:</span>
                <span class="metric-value">${cssReport.usagePercentage}</span>
            </div>
            <div class="metric-item">
                <span>性能模式:</span>
                <span class="metric-value">${animationStatus.performanceMode}</span>
            </div>
        `;
    }

    // 更新移动端指标
    updateMobileMetrics() {
        const container = document.getElementById('mobile-metrics');
        if (!container) return;
        
        const mobileStatus = mobileOptimizer.getPerformanceReport();
        
        container.innerHTML = `
            <div class="metric-item">
                <span>移动设备:</span>
                <span class="metric-value">${mobileStatus.isMobile ? '是' : '否'}</span>
            </div>
            <div class="metric-item">
                <span>电池电量:</span>
                <span class="metric-value">${mobileStatus.batteryLevel}</span>
            </div>
            <div class="metric-item">
                <span>网络类型:</span>
                <span class="metric-value">${mobileStatus.networkType}</span>
            </div>
        `;
    }

    // 更新建议
    updateRecommendations() {
        const container = document.getElementById('recommendations');
        if (!container) return;
        
        const recommendations = this.generateRecommendations();
        
        container.innerHTML = recommendations.map(rec => 
            `<div class="recommendation-item">${rec}</div>`
        ).join('');
    }

    // 生成建议
    generateRecommendations() {
        const recommendations = [];
        
        const lcp = this.metrics.get('LCP') || 0;
        const cls = this.metrics.get('CLS') || 0;
        const memoryUsed = this.metrics.get('memoryUsed') || 0;
        const memoryLimit = this.metrics.get('memoryLimit') || 0;
        
        if (lcp > 2500) {
            recommendations.push('LCP过高，建议优化关键资源加载');
        }
        
        if (cls > 0.1) {
            recommendations.push('布局偏移较大，检查图片和动态内容');
        }
        
        if (memoryUsed / memoryLimit > 0.8) {
            recommendations.push('内存使用率过高，建议清理缓存');
        }
        
        const cssReport = cssOptimizer.generateUsageReport();
        if (parseFloat(cssReport.usagePercentage) < 60) {
            recommendations.push('CSS使用率较低，建议移除未使用的样式');
        }
        
        return recommendations;
    }

    // 显示仪表板
    show() {
        this.isVisible = true;
        const dashboard = document.getElementById('performance-dashboard');
        dashboard.classList.add('visible');
    }

    // 隐藏仪表板
    hide() {
        this.isVisible = false;
        const dashboard = document.getElementById('performance-dashboard');
        dashboard.classList.remove('visible');
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // 全面优化
    async optimizeAll() {
        console.log('🚀 Starting comprehensive optimization...');
        
        // 清理缓存
        await this.clearCaches();
        
        // 优化CSS
        cssOptimizer.cleanupUnusedCSS();
        
        // 触发垃圾回收
        if (window.gc) {
            window.gc();
        }
        
        console.log('✅ Optimization complete');
    }

    // 清理缓存
    async clearCaches() {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            const messageChannel = new MessageChannel();
            
            return new Promise(resolve => {
                messageChannel.port1.onmessage = (event) => {
                    resolve(event.data);
                };
                
                navigator.serviceWorker.controller.postMessage(
                    { type: 'CLEAR_CACHE' },
                    [messageChannel.port2]
                );
            });
        }
    }

    // 运行诊断
    runDiagnostics() {
        const report = {
            timestamp: new Date().toISOString(),
            metrics: Object.fromEntries(this.metrics),
            optimizers: {
                loader: optimizedLoader.getLoadingStatus(),
                media: mediaOptimizer.getPerformanceReport(),
                animation: animationOptimizer.getPerformanceReport(),
                mobile: mobileOptimizer.getPerformanceReport()
            }
        };
        
        console.log('📊 Performance Diagnostics:', report);
        return report;
    }

    // 导出报告
    exportReport() {
        const report = this.runDiagnostics();
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-report-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
}

// 导出单例
export const performanceDashboard = new PerformanceDashboard();
export default PerformanceDashboard;

// 全局访问
window.performanceDashboard = performanceDashboard;
