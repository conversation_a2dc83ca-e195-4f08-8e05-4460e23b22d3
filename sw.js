/**
 * Service Worker - 优化版
 * 实现智能缓存策略、离线支持和性能优化
 */

const CACHE_VERSION = '2.0.0';
const CACHE_PREFIX = 'essay-platform';
const STATIC_CACHE = `${CACHE_PREFIX}-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE = `${CACHE_PREFIX}-dynamic-${CACHE_VERSION}`;
const API_CACHE = `${CACHE_PREFIX}-api-${CACHE_VERSION}`;
const IMAGE_CACHE = `${CACHE_PREFIX}-images-${CACHE_VERSION}`;
const FONT_CACHE = `${CACHE_PREFIX}-fonts-${CACHE_VERSION}`;

// 缓存配置
const CACHE_CONFIG = {
    maxEntries: {
        static: 50,
        dynamic: 100,
        api: 50,
        images: 200,
        fonts: 20
    },
    maxAge: {
        static: 30 * 24 * 60 * 60 * 1000, // 30天
        dynamic: 7 * 24 * 60 * 60 * 1000,  // 7天
        api: 5 * 60 * 1000,                // 5分钟
        images: 30 * 24 * 60 * 60 * 1000,  // 30天
        fonts: 365 * 24 * 60 * 60 * 1000   // 1年
    }
};

// 关键静态资源列表
const CRITICAL_ASSETS = [
    '/',
    '/index.html',
    '/styles/critical.css',
    '/styles/variables.css',
    '/js/utils/optimized-loader.js',
    '/js/utils/page-transition.js'
];

// 静态资源列表
const STATIC_ASSETS = [
    ...CRITICAL_ASSETS,
    '/styles/main.css',
    '/styles/components.css',
    '/styles/animations.css',
    '/js/components.js',
    '/js/main.js',
    '/js/utils/smooth-animations.js',
    '/js/utils/css-optimizer.js',
    '/js/utils/js-optimizer.js',
    '/js/utils/media-optimizer.js'
];

// 缓存策略配置
const CACHE_STRATEGIES = {
    static: 'cache-first',
    api: 'network-first',
    pages: 'stale-while-revalidate',
    images: 'cache-first',
    fonts: 'cache-first'
};

// 安装事件 - 优化版
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');

    event.waitUntil(
        Promise.all([
            // 缓存关键资源
            caches.open(STATIC_CACHE).then(cache => {
                console.log('📦 Caching critical assets...');
                return cache.addAll(CRITICAL_ASSETS);
            }),
            // 预缓存其他静态资源
            caches.open(STATIC_CACHE).then(cache => {
                console.log('📦 Pre-caching static assets...');
                return Promise.allSettled(
                    STATIC_ASSETS.filter(asset => !CRITICAL_ASSETS.includes(asset))
                        .map(asset => cache.add(asset).catch(err => {
                            console.warn(`Failed to cache ${asset}:`, err);
                        }))
                );
            })
        ])
        .then(() => {
            console.log('✅ Assets cached successfully');
            return self.skipWaiting();
        })
        .catch((error) => {
            console.error('❌ Failed to cache assets:', error);
        })
    );
});

// 激活事件
self.addEventListener('activate', (event) => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName.startsWith(CACHE_PREFIX) && 
                            cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== API_CACHE) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// 拦截请求
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非GET请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 根据请求类型选择缓存策略
    if (isStaticAsset(url)) {
        event.respondWith(cacheFirst(request, STATIC_CACHE));
    } else if (isAPIRequest(url)) {
        event.respondWith(networkFirst(request, API_CACHE));
    } else if (isPageRequest(url)) {
        event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
    } else {
        event.respondWith(networkFirst(request, DYNAMIC_CACHE));
    }
});

// 判断是否为静态资源
function isStaticAsset(url) {
    const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2'];
    return staticExtensions.some(ext => url.pathname.endsWith(ext)) ||
           url.hostname === 'cdn.tailwindcss.com' ||
           url.hostname === 'unpkg.com';
}

// 判断是否为API请求
function isAPIRequest(url) {
    return url.pathname.startsWith('/api/');
}

// 判断是否为页面请求
function isPageRequest(url) {
    return url.pathname.endsWith('.html') || 
           url.pathname === '/' ||
           !url.pathname.includes('.');
}

// 缓存优先策略
async function cacheFirst(request, cacheName) {
    try {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        return new Response('Network error', { status: 503 });
    }
}

// 网络优先策略
async function networkFirst(request, cacheName) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(cacheName);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response('Offline', { status: 503 });
    }
}

// 过期重新验证策略
async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => {
        // 网络失败时返回缓存
        return cachedResponse;
    });
    
    // 如果有缓存，立即返回，同时在后台更新
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // 如果没有缓存，等待网络响应
    return fetchPromise;
}

// 消息处理
self.addEventListener('message', (event) => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then((size) => {
                event.ports[0].postMessage({ size });
            });
            break;
            
        default:
            console.log('Unknown message type:', type);
    }
});

// 清除所有缓存
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    const deletePromises = cacheNames
        .filter(name => name.startsWith(CACHE_PREFIX))
        .map(name => caches.delete(name));
    
    return Promise.all(deletePromises);
}

// 获取缓存大小
async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
        if (cacheName.startsWith(CACHE_PREFIX)) {
            const cache = await caches.open(cacheName);
            const requests = await cache.keys();
            
            for (const request of requests) {
                const response = await cache.match(request);
                if (response) {
                    const blob = await response.blob();
                    totalSize += blob.size;
                }
            }
        }
    }
    
    return totalSize;
}

// 后台同步
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // 实现后台同步逻辑
    console.log('Background sync triggered');
}

// 推送通知
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: data.data,
            actions: data.actions
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// 通知点击处理
self.addEventListener('notificationclick', (event) => {
    event.notification.close();

    if (event.action === 'open') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url)
        );
    }
});

// 消息处理 - 优化版
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }

    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_VERSION });
    }

    if (event.data && event.data.type === 'CLEAR_CACHE') {
        clearAllCaches().then(() => {
            event.ports[0].postMessage({ success: true });
        });
    }

    if (event.data && event.data.type === 'GET_CACHE_STATUS') {
        getCacheStatus().then(status => {
            event.ports[0].postMessage(status);
        });
    }
});

// 获取缓存状态
async function getCacheStatus() {
    const cacheNames = await caches.keys();
    const status = {};

    for (const cacheName of cacheNames) {
        if (cacheName.startsWith(CACHE_PREFIX)) {
            const cache = await caches.open(cacheName);
            const keys = await cache.keys();
            status[cacheName] = {
                entries: keys.length,
                type: getCacheType(cacheName)
            };
        }
    }

    return status;
}

// 获取缓存类型
function getCacheType(cacheName) {
    if (cacheName.includes('static')) return 'static';
    if (cacheName.includes('dynamic')) return 'dynamic';
    if (cacheName.includes('api')) return 'api';
    if (cacheName.includes('images')) return 'images';
    if (cacheName.includes('fonts')) return 'fonts';
    return 'unknown';
}

// 清理所有缓存
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('🗑️ All caches cleared');
}

// 智能缓存更新
async function smartCacheUpdate() {
    try {
        const cache = await caches.open(STATIC_CACHE);

        for (const asset of CRITICAL_ASSETS) {
            const response = await fetch(asset);
            if (response.ok) {
                await cache.put(asset, response);
                console.log('🔄 Updated cache for:', asset);
            }
        }
    } catch (error) {
        console.warn('Failed to update cache:', error);
    }
}
