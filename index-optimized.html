<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博晓文 - 高质量原创AI论文写作平台</title>
    <meta name="description" content="高质量原创AI论文写作平台，一键万字生成，无限改稿！">
    <meta name="keywords" content="AI,论文,写作,学术">
    
    <!-- 预连接关键域名 -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://unpkg.com">
    
    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    
    <!-- 关键CSS内联 -->
    <style data-critical="true">
        /* 关键CSS将在这里内联 */
        :root {
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --gray-50: #f9fafb;
            --gray-700: #374151;
            --header-height: 5rem;
            --sidebar-width: 16rem;
        }
        
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background-color: var(--gray-50);
            color: var(--gray-700);
            min-height: 100vh;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gray-50);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid var(--primary-500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/js/core/app.js" as="script">
    <link rel="preload" href="/styles/components.css" as="style">
    
    <!-- 预获取非关键资源 -->
    <link rel="prefetch" href="/js/components/button.js">
    <link rel="prefetch" href="/js/components/modal.js">
    
    <!-- Tailwind CSS - 异步加载 -->
    <link rel="preload" href="https://cdn.tailwindcss.com" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdn.tailwindcss.com"></noscript>
    
    <!-- Lucide Icons - 异步加载 -->
    <link rel="preload" href="https://unpkg.com/lucide@latest/dist/umd/lucide.js" as="script">
    
    <!-- Service Worker注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    
    <!-- 性能监控初始化 -->
    <script>
        // 记录页面开始时间
        window.pageStartTime = performance.now();
        
        // 记录关键时间点
        window.addEventListener('DOMContentLoaded', () => {
            window.domContentLoadedTime = performance.now();
        });
        
        window.addEventListener('load', () => {
            window.loadTime = performance.now();
        });
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600">正在加载应用...</p>
        </div>
    </div>

    <!-- 应用容器 -->
    <div id="app" style="display: none;">
        <!-- Header Container -->
        <div id="header-container"></div>

        <!-- Sidebar Container -->
        <div id="sidebar-container"></div>

        <!-- Main Content -->
        <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <!-- Page Title -->
                    <div class="mb-8 lg:mb-12 particles-bg">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div class="mb-6 lg:mb-0 fade-in-up">
                                <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                    智能AI论文写作平台
                                </h1>
                                <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                    选择您需要的论文类型，AI将为您智能生成专业、原创的学术内容
                                    <span class="inline-block ml-2 text-blue-600">✨</span>
                                </p>

                                <!-- 特色标签 -->
                                <div class="flex flex-wrap gap-2 mt-4">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                        🚀 3秒生成大纲
                                    </span>
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
                                        ✅ 100%原创保证
                                    </span>
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                        🔄 无限次修改
                                    </span>
                                </div>
                            </div>

                            <!-- 统计卡片 - 懒加载 -->
                            <div class="hidden lg:flex space-x-6 xl:space-x-8" data-component="stats-cards">
                                <!-- 统计卡片内容将通过JavaScript加载 -->
                            </div>
                        </div>

                        <!-- 移动端统计 -->
                        <div class="lg:hidden grid grid-cols-3 gap-3 mt-6" data-component="mobile-stats">
                            <!-- 移动端统计内容将通过JavaScript加载 -->
                        </div>
                    </div>

                    <!-- Main Content Layout -->
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                        <!-- Left Column - Main Content -->
                        <div class="lg:col-span-3">
                            <!-- Essay Type Grid - 懒加载 -->
                            <div class="hidden lg:grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-4 mb-6 sm:mb-8" 
                                 id="essay-types-desktop" 
                                 data-component="essay-types">
                                <!-- Essay types will be populated by JavaScript -->
                            </div>

                            <!-- Essay Type Grid - 移动端 -->
                            <div class="lg:hidden grid grid-cols-2 gap-3 mb-6" 
                                 id="essay-types-mobile"
                                 data-component="essay-types-mobile">
                                <!-- Essay types will be populated by JavaScript -->
                            </div>

                            <!-- Form Container - 懒加载 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up" 
                                 id="form-wrapper" 
                                 style="display: none;"
                                 data-component="essay-form">
                                <!-- Form content will be populated by JavaScript -->
                            </div>

                            <!-- 移动端提示卡片 -->
                            <div class="lg:hidden bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-xl text-center mb-6 fade-in" 
                                 id="mobile-tip">
                                <h3 class="font-bold mb-2 flex items-center justify-center gap-2">
                                    <i data-lucide="lightbulb" class="w-5 h-5"></i>
                                    使用提示
                                </h3>
                                <p class="text-sm">选择论文类型后，填写相关信息即可快速生成专业大纲</p>
                            </div>
                        </div>

                        <!-- Right Column - Sidebar -->
                        <div class="lg:col-span-1 space-y-6 order-1 lg:order-2" data-component="sidebar-widgets">
                            <!-- 侧边栏内容将通过JavaScript懒加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Mobile Navigation Container -->
        <div id="mobile-nav-container"></div>

        <!-- 浮动操作按钮 -->
        <button class="fab ripple" id="fab-help" title="获取帮助" data-component="fab">
            <i data-lucide="help-circle" class="w-5 h-5"></i>
        </button>
    </div>

    <!-- 错误边界 -->
    <div id="error-boundary" style="display: none;" class="fixed inset-0 bg-red-50 flex items-center justify-center z-50">
        <div class="text-center p-8">
            <h2 class="text-2xl font-bold text-red-600 mb-4">应用加载失败</h2>
            <p class="text-red-500 mb-4">抱歉，应用无法正常加载。请刷新页面重试。</p>
            <button onclick="window.location.reload()" class="bg-red-600 text-white px-4 py-2 rounded">
                刷新页面
            </button>
        </div>
    </div>

    <!-- 脚本加载 -->
    <script>
        // 异步加载Lucide图标
        const lucideScript = document.createElement('script');
        lucideScript.src = 'https://unpkg.com/lucide@latest/dist/umd/lucide.js';
        lucideScript.async = true;
        document.head.appendChild(lucideScript);
        
        // 异步加载组件样式
        const componentStyles = document.createElement('link');
        componentStyles.rel = 'stylesheet';
        componentStyles.href = '/styles/components.css';
        componentStyles.media = 'print';
        componentStyles.onload = function() {
            this.media = 'all';
        };
        document.head.appendChild(componentStyles);
    </script>

    <!-- 主应用脚本 - 模块化加载 -->
    <script type="module">
        import { app } from '/js/core/app.js';
        
        // 应用初始化
        app.initialize()
            .then(() => {
                // 隐藏加载屏幕
                document.getElementById('loading-screen').style.display = 'none';
                // 显示应用
                document.getElementById('app').style.display = 'block';
                document.getElementById('app').classList.add('fade-in');
                
                // 记录应用就绪时间
                window.appReadyTime = performance.now();
                console.log(`应用就绪时间: ${window.appReadyTime - window.pageStartTime}ms`);
            })
            .catch((error) => {
                console.error('应用初始化失败:', error);
                
                // 显示错误边界
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('error-boundary').style.display = 'flex';
            });
    </script>

    <!-- 回退脚本（非模块浏览器） -->
    <script nomodule>
        // 为不支持ES模块的浏览器提供回退
        console.warn('您的浏览器不支持ES模块，正在加载回退版本...');
        
        const fallbackScript = document.createElement('script');
        fallbackScript.src = '/js/main-legacy.js';
        document.head.appendChild(fallbackScript);
    </script>
</body>
</html>
