/**
 * UI配置管理
 * 统一管理界面相关的配置
 */

export const UIConfig = {
    // 主题配置
    theme: {
        default: 'light',
        available: ['light', 'dark', 'auto'],
        colors: {
            primary: {
                50: '#eff6ff',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8'
            },
            secondary: {
                50: '#f8fafc',
                500: '#64748b',
                600: '#475569',
                700: '#334155'
            }
        }
    },

    // 布局配置
    layout: {
        sidebar: {
            width: '16rem', // 64 * 0.25rem
            collapsedWidth: '4rem',
            breakpoint: 1024, // lg breakpoint
            defaultCollapsed: false
        },
        header: {
            height: '5rem', // 20 * 0.25rem
            sticky: true,
            blur: true
        },
        content: {
            maxWidth: '80rem', // 7xl
            padding: {
                mobile: '1.5rem',
                desktop: '2rem'
            }
        }
    },

    // 动画配置
    animations: {
        enabled: true,
        duration: {
            fast: '0.15s',
            normal: '0.3s',
            slow: '0.5s'
        },
        easing: {
            default: 'cubic-bezier(0.4, 0, 0.2, 1)',
            bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        },
        stagger: {
            delay: 0.1, // 交错动画延迟
            maxItems: 10 // 最大交错项数
        }
    },

    // 响应式断点
    breakpoints: {
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280,
        '2xl': 1536
    },

    // 组件默认配置
    components: {
        button: {
            sizes: {
                sm: { height: '2.25rem', padding: '0 0.75rem', fontSize: '0.75rem' },
                md: { height: '2.5rem', padding: '0 1rem', fontSize: '0.875rem' },
                lg: { height: '2.75rem', padding: '0 2rem', fontSize: '1rem' }
            },
            variants: {
                primary: 'btn-primary',
                secondary: 'btn-secondary',
                outline: 'btn-outline',
                ghost: 'btn-ghost'
            }
        },
        input: {
            sizes: {
                sm: { height: '2.25rem', padding: '0.5rem 0.75rem', fontSize: '0.75rem' },
                md: { height: '2.5rem', padding: '0.5rem 0.75rem', fontSize: '0.875rem' },
                lg: { height: '2.75rem', padding: '0.75rem 1rem', fontSize: '1rem' }
            }
        },
        card: {
            shadows: {
                sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
            },
            radius: {
                sm: '0.375rem',
                md: '0.5rem',
                lg: '0.75rem',
                xl: '1rem'
            }
        }
    },

    // 表单配置
    forms: {
        validation: {
            showErrorsOnBlur: true,
            showErrorsOnSubmit: true,
            realTimeValidation: false
        },
        autoSave: {
            enabled: true,
            interval: 30000, // 30秒
            key: 'form_autosave'
        }
    },

    // 通知配置
    notifications: {
        position: 'top-right',
        duration: {
            success: 3000,
            error: 5000,
            warning: 4000,
            info: 3000
        },
        maxVisible: 5,
        animations: {
            enter: 'fade-in-up',
            exit: 'fade-out-up'
        }
    },

    // 加载状态配置
    loading: {
        spinner: {
            size: '1.5rem',
            color: '#3b82f6'
        },
        skeleton: {
            baseColor: '#f3f4f6',
            highlightColor: '#e5e7eb',
            animationDuration: '1.5s'
        },
        overlay: {
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            backdropBlur: '4px'
        }
    },

    // 图标配置
    icons: {
        library: 'lucide',
        defaultSize: '1.25rem',
        strokeWidth: 2
    }
};

// 获取当前主题
export function getCurrentTheme() {
    const saved = localStorage.getItem('theme');
    if (saved && UIConfig.theme.available.includes(saved)) {
        return saved;
    }
    
    if (UIConfig.theme.default === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    
    return UIConfig.theme.default;
}

// 设置主题
export function setTheme(theme) {
    if (!UIConfig.theme.available.includes(theme)) {
        console.warn(`Theme "${theme}" is not available`);
        return false;
    }
    
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // 发送主题变更事件
    window.dispatchEvent(new CustomEvent('themeChanged', { 
        detail: { theme } 
    }));
    
    return true;
}

// 获取响应式断点
export function getBreakpoint() {
    const width = window.innerWidth;
    
    if (width >= UIConfig.breakpoints['2xl']) return '2xl';
    if (width >= UIConfig.breakpoints.xl) return 'xl';
    if (width >= UIConfig.breakpoints.lg) return 'lg';
    if (width >= UIConfig.breakpoints.md) return 'md';
    if (width >= UIConfig.breakpoints.sm) return 'sm';
    
    return 'xs';
}

// 检查是否为移动设备
export function isMobile() {
    return window.innerWidth < UIConfig.breakpoints.lg;
}

// 初始化UI配置
export function initializeUIConfig() {
    // 设置初始主题
    const theme = getCurrentTheme();
    setTheme(theme);
    
    // 监听系统主题变化
    if (UIConfig.theme.default === 'auto') {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (localStorage.getItem('theme') === 'auto') {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
    
    // 设置全局UI配置
    window.UI_CONFIG = UIConfig;
    
    // 发送UI配置就绪事件
    window.dispatchEvent(new CustomEvent('uiConfigReady', { 
        detail: UIConfig 
    }));
}
