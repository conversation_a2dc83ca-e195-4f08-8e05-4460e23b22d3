/**
 * 应用主入口
 * 统一管理应用初始化和模块加载
 */

import { moduleLoader, initializeCore } from './module-loader.js';
import { AppConfig, initializeConfig } from '../config/app-config.js';
import { UIConfig, initializeUIConfig } from '../config/ui-config.js';
import { PerformanceConfig, initializePerformanceConfig } from '../config/performance-config.js';

export class App {
    constructor() {
        this.initialized = false;
        this.modules = new Map();
        this.services = new Map();
        this.components = new Map();
        this.currentPage = null;
        this.startTime = performance.now();
        
        this.state = {
            loading: true,
            error: null,
            ready: false
        };
    }

    // 初始化应用
    async initialize() {
        if (this.initialized) {
            return this;
        }

        try {
            console.log('🚀 Initializing application...');
            
            // 1. 初始化配置
            await this.initializeConfigs();
            
            // 2. 初始化核心模块
            await this.initializeCore();
            
            // 3. 设置错误处理
            this.setupErrorHandling();
            
            // 4. 初始化性能监控
            this.initializePerformanceMonitoring();
            
            // 5. 检测当前页面
            this.detectCurrentPage();
            
            // 6. 加载页面特定模块
            await this.loadPageModules();
            
            // 7. 初始化UI组件
            await this.initializeComponents();
            
            // 8. 设置路由监听
            this.setupRouting();
            
            // 9. 完成初始化
            this.finializeInitialization();
            
            this.initialized = true;
            console.log('✅ Application initialized successfully');
            
            return this;
        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.handleInitializationError(error);
            throw error;
        }
    }

    // 初始化配置
    async initializeConfigs() {
        console.log('📋 Initializing configurations...');
        
        const configPromises = [
            initializeConfig(),
            initializeUIConfig(),
            initializePerformanceConfig()
        ];

        await Promise.all(configPromises);
        
        // 验证配置
        this.validateConfigurations();
    }

    // 验证配置
    validateConfigurations() {
        const requiredConfigs = [
            { config: AppConfig, name: 'AppConfig' },
            { config: UIConfig, name: 'UIConfig' },
            { config: PerformanceConfig, name: 'PerformanceConfig' }
        ];

        for (const { config, name } of requiredConfigs) {
            if (!config) {
                throw new Error(`${name} is not properly initialized`);
            }
        }
    }

    // 初始化核心模块
    async initializeCore() {
        console.log('🔧 Initializing core modules...');
        
        const success = await initializeCore();
        if (!success) {
            throw new Error('Failed to initialize core modules');
        }

        // 注册核心服务
        await this.registerCoreServices();
    }

    // 注册核心服务
    async registerCoreServices() {
        const coreServices = [
            'resource-loader',
            'css-loader',
            'event-bus',
            'storage',
            'validator'
        ];

        for (const serviceName of coreServices) {
            try {
                const service = await moduleLoader.load(serviceName);
                this.services.set(serviceName, service);
            } catch (error) {
                console.warn(`Failed to load service: ${serviceName}`, error);
            }
        }
    }

    // 设置错误处理
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', this.handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
        
        // 模块加载错误
        moduleLoader.on('moduleError', this.handleModuleError.bind(this));
    }

    // 初始化性能监控
    initializePerformanceMonitoring() {
        if (PerformanceConfig.monitoring.metrics.enabled) {
            // 记录初始化时间
            this.recordMetric('app.initStart', this.startTime);
            
            // 监听页面性能指标
            this.observePerformanceMetrics();
        }
    }

    // 观察性能指标
    observePerformanceMetrics() {
        // 观察Core Web Vitals
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric(`performance.${entry.name}`, entry.value);
                }
            });

            observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input'] });
        }
    }

    // 检测当前页面
    detectCurrentPage() {
        const path = window.location.pathname;
        const page = path.split('/').pop().replace('.html', '') || 'index';
        this.currentPage = page === 'index' ? 'home' : page;
        
        console.log(`📄 Current page: ${this.currentPage}`);
    }

    // 加载页面特定模块
    async loadPageModules() {
        console.log(`📦 Loading modules for page: ${this.currentPage}`);
        
        const pageModules = this.getPageModules(this.currentPage);
        
        if (pageModules.length > 0) {
            await moduleLoader.loadBatch(pageModules);
            
            // 注册页面模块
            for (const moduleName of pageModules) {
                const module = moduleLoader.get(moduleName);
                if (module) {
                    this.modules.set(moduleName, module);
                }
            }
        }
    }

    // 获取页面模块列表
    getPageModules(page) {
        const pageModuleMap = {
            home: ['essay-forms', 'base-component', 'button'],
            history: ['history', 'table'],
            plagiarism: ['plagiarism', 'form'],
            billing: ['billing', 'modal'],
            profile: ['profile', 'form'],
            aigc: ['aigc', 'form']
        };

        return pageModuleMap[page] || [];
    }

    // 初始化UI组件
    async initializeComponents() {
        console.log('🎨 Initializing UI components...');
        
        // 加载基础组件
        await moduleLoader.load('base-component');
        
        // 初始化页面组件
        this.initializePageComponents();
        
        // 设置组件自动发现
        this.setupComponentAutoDiscovery();
    }

    // 初始化页面组件
    initializePageComponents() {
        // 根据当前页面初始化特定组件
        const componentInitializers = {
            home: () => this.initializeHomeComponents(),
            history: () => this.initializeHistoryComponents(),
            plagiarism: () => this.initializePlagiarismComponents(),
            billing: () => this.initializeBillingComponents(),
            profile: () => this.initializeProfileComponents()
        };

        const initializer = componentInitializers[this.currentPage];
        if (initializer) {
            initializer();
        }

        // 初始化通用组件
        this.initializeCommonComponents();
    }

    // 初始化通用组件
    initializeCommonComponents() {
        // 初始化导航组件
        const UIComponents = moduleLoader.get('base-component')?.UIComponents;
        if (UIComponents) {
            UIComponents.initializePage(this.currentPage);
        }
    }

    // 设置组件自动发现
    setupComponentAutoDiscovery() {
        // 使用MutationObserver自动发现新添加的组件
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.discoverComponents(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 发现组件
    discoverComponents(element) {
        // 查找带有data-component属性的元素
        const components = element.querySelectorAll('[data-component]');
        components.forEach((el) => {
            const componentName = el.dataset.component;
            this.initializeComponent(el, componentName);
        });
    }

    // 初始化单个组件
    async initializeComponent(element, componentName) {
        try {
            const componentModule = await moduleLoader.load(componentName);
            if (componentModule && componentModule.default) {
                const instance = new componentModule.default(element);
                this.components.set(element, instance);
            }
        } catch (error) {
            console.warn(`Failed to initialize component: ${componentName}`, error);
        }
    }

    // 设置路由监听
    setupRouting() {
        // 监听popstate事件（浏览器前进后退）
        window.addEventListener('popstate', this.handleRouteChange.bind(this));
        
        // 监听链接点击
        document.addEventListener('click', this.handleLinkClick.bind(this));
    }

    // 处理路由变化
    handleRouteChange() {
        const newPage = this.detectCurrentPage();
        if (newPage !== this.currentPage) {
            this.navigateToPage(newPage);
        }
    }

    // 处理链接点击
    handleLinkClick(event) {
        const link = event.target.closest('a');
        if (link && link.href && link.origin === window.location.origin) {
            // 内部链接，可以进行预加载
            const href = link.getAttribute('href');
            if (href && href.endsWith('.html')) {
                this.preloadPage(href);
            }
        }
    }

    // 预加载页面
    async preloadPage(href) {
        const page = href.replace('.html', '').split('/').pop();
        const modules = this.getPageModules(page);
        
        if (modules.length > 0) {
            moduleLoader.preload(modules);
        }
    }

    // 导航到页面
    async navigateToPage(page) {
        console.log(`🧭 Navigating to page: ${page}`);
        
        // 清理当前页面
        this.cleanupCurrentPage();
        
        // 更新当前页面
        this.currentPage = page;
        
        // 加载新页面模块
        await this.loadPageModules();
        
        // 初始化新页面组件
        this.initializePageComponents();
    }

    // 清理当前页面
    cleanupCurrentPage() {
        // 销毁页面特定组件
        this.components.forEach((component, element) => {
            if (component.destroy) {
                component.destroy();
            }
        });
        this.components.clear();
    }

    // 完成初始化
    finializeInitialization() {
        const endTime = performance.now();
        const initTime = endTime - this.startTime;
        
        this.recordMetric('app.initTime', initTime);
        this.recordMetric('app.initEnd', endTime);
        
        this.state = {
            loading: false,
            error: null,
            ready: true
        };

        // 发送应用就绪事件
        this.emit('ready', {
            initTime,
            currentPage: this.currentPage,
            modules: Array.from(this.modules.keys()),
            services: Array.from(this.services.keys())
        });

        console.log(`⚡ Application ready in ${initTime.toFixed(2)}ms`);
    }

    // 错误处理方法
    handleGlobalError(event) {
        console.error('Global error:', event.error);
        this.recordError('global', event.error);
    }

    handleUnhandledRejection(event) {
        console.error('Unhandled promise rejection:', event.reason);
        this.recordError('promise', event.reason);
    }

    handleModuleError(event) {
        console.error('Module error:', event.detail);
        this.recordError('module', event.detail.error);
    }

    handleInitializationError(error) {
        this.state = {
            loading: false,
            error: error.message,
            ready: false
        };

        this.emit('error', { error });
    }

    // 工具方法
    recordMetric(name, value) {
        if (window.performanceMonitor) {
            window.performanceMonitor.recordMetric(name, value);
        }
    }

    recordError(type, error) {
        if (window.performanceMonitor) {
            window.performanceMonitor.recordError({ type, error });
        }
    }

    emit(event, data) {
        const customEvent = new CustomEvent(`app:${event}`, {
            detail: data
        });
        window.dispatchEvent(customEvent);
    }

    // 获取应用状态
    getState() {
        return { ...this.state };
    }

    // 获取服务
    getService(name) {
        return this.services.get(name);
    }

    // 获取模块
    getModule(name) {
        return this.modules.get(name);
    }

    // 获取组件
    getComponent(element) {
        return this.components.get(element);
    }

    // 页面特定初始化方法（子类可以重写）
    initializeHomeComponents() {}
    initializeHistoryComponents() {}
    initializePlagiarismComponents() {}
    initializeBillingComponents() {}
    initializeProfileComponents() {}
}

// 创建全局应用实例
export const app = new App();

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.initialize());
} else {
    app.initialize();
}
