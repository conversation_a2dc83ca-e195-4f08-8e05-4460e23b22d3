/**
 * 性能监控系统
 * 监控应用性能指标和用户体验
 */

import { PerformanceConfig } from '../config/performance-config.js';

export class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.thresholds = PerformanceConfig.thresholds.performance;
        this.isEnabled = PerformanceConfig.monitoring.metrics.enabled;
        this.startTime = performance.now();
        
        if (this.isEnabled) {
            this.init();
        }
    }

    init() {
        this.setupCoreWebVitals();
        this.setupResourceTiming();
        this.setupUserTiming();
        this.setupNavigationTiming();
        this.setupMemoryMonitoring();
        this.setupErrorTracking();
        this.setupUserInteractionTracking();
        this.startPeriodicReporting();
    }

    // 设置Core Web Vitals监控
    setupCoreWebVitals() {
        if (!('PerformanceObserver' in window)) return;

        // Largest Contentful Paint (LCP)
        this.observeMetric('largest-contentful-paint', (entry) => {
            this.recordMetric('lcp', entry.startTime, {
                element: entry.element?.tagName,
                url: entry.url
            });
        });

        // First Input Delay (FID)
        this.observeMetric('first-input', (entry) => {
            this.recordMetric('fid', entry.processingStart - entry.startTime, {
                eventType: entry.name,
                target: entry.target?.tagName
            });
        });

        // Cumulative Layout Shift (CLS)
        this.observeMetric('layout-shift', (entry) => {
            if (!entry.hadRecentInput) {
                this.recordMetric('cls', entry.value, {
                    sources: entry.sources?.map(s => s.node?.tagName)
                });
            }
        });

        // First Contentful Paint (FCP)
        this.observeMetric('paint', (entry) => {
            if (entry.name === 'first-contentful-paint') {
                this.recordMetric('fcp', entry.startTime);
            }
        });
    }

    // 设置资源时间监控
    setupResourceTiming() {
        this.observeMetric('resource', (entry) => {
            const duration = entry.responseEnd - entry.startTime;
            const size = entry.transferSize || 0;
            
            this.recordMetric('resource.duration', duration, {
                name: entry.name,
                type: this.getResourceType(entry.name),
                size: size,
                cached: entry.transferSize === 0 && entry.decodedBodySize > 0
            });

            // 检查慢资源
            if (duration > 1000) { // 超过1秒
                this.recordSlowResource(entry);
            }
        });
    }

    // 设置用户时间监控
    setupUserTiming() {
        this.observeMetric('measure', (entry) => {
            this.recordMetric(`user.${entry.name}`, entry.duration, {
                detail: entry.detail
            });
        });

        this.observeMetric('mark', (entry) => {
            this.recordMetric(`mark.${entry.name}`, entry.startTime, {
                detail: entry.detail
            });
        });
    }

    // 设置导航时间监控
    setupNavigationTiming() {
        this.observeMetric('navigation', (entry) => {
            const metrics = {
                'nav.dns': entry.domainLookupEnd - entry.domainLookupStart,
                'nav.connect': entry.connectEnd - entry.connectStart,
                'nav.request': entry.responseStart - entry.requestStart,
                'nav.response': entry.responseEnd - entry.responseStart,
                'nav.dom': entry.domContentLoadedEventEnd - entry.responseEnd,
                'nav.load': entry.loadEventEnd - entry.loadEventStart,
                'nav.total': entry.loadEventEnd - entry.startTime
            };

            Object.entries(metrics).forEach(([name, value]) => {
                this.recordMetric(name, value);
            });
        });
    }

    // 设置内存监控
    setupMemoryMonitoring() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.recordMetric('memory.used', memory.usedJSHeapSize);
                this.recordMetric('memory.total', memory.totalJSHeapSize);
                this.recordMetric('memory.limit', memory.jsHeapSizeLimit);
            }, 30000); // 每30秒检查一次
        }
    }

    // 设置错误跟踪
    setupErrorTracking() {
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                reason: event.reason
            });
        });
    }

    // 设置用户交互跟踪
    setupUserInteractionTracking() {
        if (!PerformanceConfig.monitoring.ux.enabled) return;

        // 点击跟踪
        if (PerformanceConfig.monitoring.ux.trackClicks) {
            document.addEventListener('click', (event) => {
                this.recordInteraction('click', {
                    target: event.target.tagName,
                    x: event.clientX,
                    y: event.clientY
                });
            }, { passive: true });
        }

        // 滚动跟踪
        if (PerformanceConfig.monitoring.ux.trackScrolls) {
            let scrollTimeout;
            document.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    this.recordInteraction('scroll', {
                        scrollY: window.scrollY,
                        scrollX: window.scrollX
                    });
                }, 100);
            }, { passive: true });
        }

        // 表单提交跟踪
        if (PerformanceConfig.monitoring.ux.trackFormSubmissions) {
            document.addEventListener('submit', (event) => {
                this.recordInteraction('form_submit', {
                    form: event.target.id || event.target.className
                });
            });
        }
    }

    // 观察性能指标
    observeMetric(entryType, callback) {
        try {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(callback);
            });

            observer.observe({ entryTypes: [entryType] });
            this.observers.set(entryType, observer);
        } catch (error) {
            console.warn(`Failed to observe ${entryType}:`, error);
        }
    }

    // 记录指标
    recordMetric(name, value, metadata = {}) {
        const timestamp = Date.now();
        const metric = {
            name,
            value,
            timestamp,
            metadata,
            sessionId: this.getSessionId()
        };

        // 存储指标
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        this.metrics.get(name).push(metric);

        // 检查阈值
        this.checkThreshold(name, value);

        // 触发事件
        this.emit('metric', metric);

        // 限制存储数量
        this.limitMetricStorage(name);
    }

    // 记录错误
    recordError(error) {
        const errorMetric = {
            ...error,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.getSessionId()
        };

        if (!this.metrics.has('errors')) {
            this.metrics.set('errors', []);
        }
        this.metrics.get('errors').push(errorMetric);

        this.emit('error', errorMetric);
        this.limitMetricStorage('errors');
    }

    // 记录用户交互
    recordInteraction(type, data) {
        this.recordMetric(`interaction.${type}`, Date.now(), data);
    }

    // 记录慢资源
    recordSlowResource(entry) {
        this.recordMetric('slow_resource', entry.responseEnd - entry.startTime, {
            name: entry.name,
            type: this.getResourceType(entry.name),
            size: entry.transferSize
        });
    }

    // 检查阈值
    checkThreshold(name, value) {
        const threshold = this.thresholds[name];
        if (threshold && value > threshold) {
            this.recordMetric('threshold_exceeded', value, {
                metric: name,
                threshold,
                excess: value - threshold
            });

            console.warn(`Performance threshold exceeded for ${name}: ${value} > ${threshold}`);
        }
    }

    // 限制指标存储
    limitMetricStorage(name) {
        const metrics = this.metrics.get(name);
        const maxSize = PerformanceConfig.monitoring.errors.maxErrors || 100;
        
        if (metrics && metrics.length > maxSize) {
            metrics.splice(0, metrics.length - maxSize);
        }
    }

    // 获取资源类型
    getResourceType(url) {
        if (url.includes('.css')) return 'css';
        if (url.includes('.js')) return 'js';
        if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
        return 'other';
    }

    // 获取会话ID
    getSessionId() {
        if (!this.sessionId) {
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        return this.sessionId;
    }

    // 开始定期报告
    startPeriodicReporting() {
        setInterval(() => {
            this.generateReport();
        }, 60000); // 每分钟生成一次报告
    }

    // 生成性能报告
    generateReport() {
        const report = {
            timestamp: Date.now(),
            sessionId: this.getSessionId(),
            url: window.location.href,
            metrics: this.getMetricsSummary(),
            vitals: this.getCoreWebVitals(),
            resources: this.getResourceSummary(),
            errors: this.getErrorSummary(),
            memory: this.getMemoryInfo()
        };

        this.emit('report', report);
        return report;
    }

    // 获取指标摘要
    getMetricsSummary() {
        const summary = {};
        
        for (const [name, values] of this.metrics) {
            if (name === 'errors') continue;
            
            const numericValues = values.map(v => v.value).filter(v => typeof v === 'number');
            if (numericValues.length > 0) {
                summary[name] = {
                    count: numericValues.length,
                    avg: numericValues.reduce((a, b) => a + b, 0) / numericValues.length,
                    min: Math.min(...numericValues),
                    max: Math.max(...numericValues),
                    latest: numericValues[numericValues.length - 1]
                };
            }
        }
        
        return summary;
    }

    // 获取Core Web Vitals
    getCoreWebVitals() {
        const vitals = {};
        ['lcp', 'fid', 'cls', 'fcp'].forEach(metric => {
            const values = this.metrics.get(metric);
            if (values && values.length > 0) {
                vitals[metric] = values[values.length - 1].value;
            }
        });
        return vitals;
    }

    // 获取资源摘要
    getResourceSummary() {
        const resourceMetrics = this.metrics.get('resource.duration') || [];
        const slowResources = this.metrics.get('slow_resource') || [];
        
        return {
            totalRequests: resourceMetrics.length,
            slowRequests: slowResources.length,
            avgDuration: resourceMetrics.length > 0 
                ? resourceMetrics.reduce((sum, m) => sum + m.value, 0) / resourceMetrics.length 
                : 0
        };
    }

    // 获取错误摘要
    getErrorSummary() {
        const errors = this.metrics.get('errors') || [];
        const errorTypes = {};
        
        errors.forEach(error => {
            errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
        });
        
        return {
            totalErrors: errors.length,
            errorTypes,
            recentErrors: errors.slice(-5)
        };
    }

    // 获取内存信息
    getMemoryInfo() {
        if ('memory' in performance) {
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }

    // 获取所有指标
    getAllMetrics() {
        const result = {};
        for (const [name, values] of this.metrics) {
            result[name] = values;
        }
        return result;
    }

    // 清除指标
    clearMetrics() {
        this.metrics.clear();
    }

    // 导出数据
    exportData() {
        return {
            sessionId: this.getSessionId(),
            startTime: this.startTime,
            endTime: Date.now(),
            metrics: this.getAllMetrics(),
            report: this.generateReport()
        };
    }

    // 事件发射
    emit(event, data) {
        const customEvent = new CustomEvent(`performance:${event}`, {
            detail: data
        });
        window.dispatchEvent(customEvent);
    }

    // 监听事件
    on(event, handler) {
        window.addEventListener(`performance:${event}`, handler);
        return () => window.removeEventListener(`performance:${event}`, handler);
    }

    // 销毁监控器
    destroy() {
        // 停止所有观察器
        for (const observer of this.observers.values()) {
            observer.disconnect();
        }
        this.observers.clear();
        
        // 清除数据
        this.metrics.clear();
        
        this.emit('destroy');
    }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 便捷方法
export const recordMetric = (name, value, metadata) => 
    performanceMonitor.recordMetric(name, value, metadata);

export const recordError = (error) => 
    performanceMonitor.recordError(error);

export const generateReport = () => 
    performanceMonitor.generateReport();
