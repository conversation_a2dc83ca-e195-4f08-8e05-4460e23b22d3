<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文类型悬停测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* 完全禁用所有可能导致文字消失的效果 */
        * {
            -webkit-text-fill-color: unset !important;
        }
        
        /* 强制显示所有文字 */
        .test-card h3 {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
            color: #111827 !important;
            -webkit-text-fill-color: #111827 !important;
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
            transition: color 0.3s ease !important;
        }
        
        /* 悬停效果 */
        .test-card {
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .test-card:hover .icon-container {
            transform: scale(1.1);
        }
        
        .test-card[data-type="literature"]:hover h3 {
            color: #dc2626 !important;
            -webkit-text-fill-color: #dc2626 !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }
        
        .test-card[data-type="proposal"]:hover h3 {
            color: #2563eb !important;
            -webkit-text-fill-color: #2563eb !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }
        
        /* 强制覆盖任何group-hover效果 */
        .group:hover .group-hover\:text-red-600,
        .group:hover .group-hover\:text-blue-600,
        .group:hover .group-hover\:text-transparent,
        .group:hover [class*="group-hover:text-"] {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
            color: inherit !important;
            -webkit-text-fill-color: inherit !important;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-8 text-center">论文类型悬停测试</h1>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <!-- 测试卡片1：文献综述 -->
            <div class="test-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="literature">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600 icon-container transition-transform duration-300">
                        <i data-lucide="book-open" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 style="opacity: 1 !important; visibility: visible !important; display: block !important; color: #111827 !important;">文献综述</h3>
                    <div class="text-xs text-gray-500">专业定制</div>
                </div>
            </div>
            
            <!-- 测试卡片2：开题报告 -->
            <div class="test-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="proposal">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 icon-container transition-transform duration-300">
                        <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 style="opacity: 1 !important; visibility: visible !important; display: block !important; color: #111827 !important;">开题报告</h3>
                    <div class="text-xs text-gray-500">专业定制</div>
                </div>
            </div>
            
            <!-- 测试卡片3：带group类的 -->
            <div class="test-card group cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="literature">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-green-500 to-green-600 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="newspaper" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="group-hover:text-red-600" style="opacity: 1 !important; visibility: visible !important; display: block !important; color: #111827 !important;">期刊论文(group)</h3>
                    <div class="text-xs text-gray-500">专业定制</div>
                </div>
            </div>
            
            <!-- 测试卡片4：原始样式 -->
            <div class="test-card cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1" data-type="proposal">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-purple-500 to-purple-600 icon-container transition-transform duration-300">
                        <i data-lucide="pen-tool" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="font-bold text-gray-900 mb-1 text-sm leading-tight">任务书</h3>
                    <div class="text-xs text-gray-500">专业定制</div>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-bold mb-4">测试说明</h2>
            <ul class="space-y-2 text-sm">
                <li>• 卡片1：使用内联样式强制显示</li>
                <li>• 卡片2：使用内联样式强制显示</li>
                <li>• 卡片3：带group类，测试group-hover冲突</li>
                <li>• 卡片4：原始样式，无特殊保护</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 初始化Lucide图标
        lucide.createIcons();
        
        // JavaScript强制保护
        function forceTextVisible() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach(card => {
                const h3 = card.querySelector('h3');
                if (h3) {
                    h3.style.setProperty('opacity', '1', 'important');
                    h3.style.setProperty('visibility', 'visible', 'important');
                    h3.style.setProperty('display', 'block', 'important');
                    h3.style.setProperty('-webkit-text-fill-color', 'inherit', 'important');
                }
            });
        }
        
        // 页面加载后执行
        document.addEventListener('DOMContentLoaded', forceTextVisible);
        
        // 定期检查
        setInterval(forceTextVisible, 500);
        
        // 添加悬停事件监听
        document.querySelectorAll('.test-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                const h3 = this.querySelector('h3');
                if (h3) {
                    console.log('Mouse enter:', h3.textContent, 'Opacity:', getComputedStyle(h3).opacity, 'Visibility:', getComputedStyle(h3).visibility);
                    forceTextVisible();
                }
            });
            
            card.addEventListener('mouseleave', function() {
                const h3 = this.querySelector('h3');
                if (h3) {
                    console.log('Mouse leave:', h3.textContent, 'Opacity:', getComputedStyle(h3).opacity, 'Visibility:', getComputedStyle(h3).visibility);
                    forceTextVisible();
                }
            });
        });
    </script>
</body>
</html>
