/**
 * CSS优化器
 * 分析和优化CSS性能，移除未使用的样式
 */

class CSSOptimizer {
    constructor() {
        this.usedSelectors = new Set();
        this.unusedSelectors = new Set();
        this.criticalCSS = '';
        this.nonCriticalCSS = '';
        this.observer = null;
        
        this.init();
    }

    init() {
        this.analyzeCSSUsage();
        this.setupDynamicLoading();
        this.optimizeExistingCSS();
    }

    // 分析CSS使用情况
    analyzeCSSUsage() {
        // 获取所有CSS规则
        const allRules = this.getAllCSSRules();
        
        // 分析哪些选择器被使用
        this.analyzeUsedSelectors(allRules);
        
        // 识别关键CSS
        this.identifyCriticalCSS(allRules);
        
        console.log('📊 CSS Analysis:', {
            total: allRules.length,
            used: this.usedSelectors.size,
            unused: this.unusedSelectors.size,
            critical: this.criticalCSS.length
        });
    }

    // 获取所有CSS规则
    getAllCSSRules() {
        const rules = [];
        
        try {
            for (const stylesheet of document.styleSheets) {
                if (this.isAccessibleStylesheet(stylesheet)) {
                    const cssRules = stylesheet.cssRules || stylesheet.rules;
                    for (const rule of cssRules) {
                        if (rule.type === CSSRule.STYLE_RULE) {
                            rules.push({
                                selector: rule.selectorText,
                                cssText: rule.cssText,
                                stylesheet: stylesheet.href || 'inline'
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Cannot access some stylesheets due to CORS:', error);
        }
        
        return rules;
    }

    // 检查样式表是否可访问
    isAccessibleStylesheet(stylesheet) {
        try {
            // 尝试访问cssRules来检查是否可访问
            const rules = stylesheet.cssRules;
            return true;
        } catch (error) {
            return false;
        }
    }

    // 分析使用的选择器
    analyzeUsedSelectors(rules) {
        rules.forEach(rule => {
            try {
                // 检查选择器是否匹配DOM中的元素
                if (document.querySelector(rule.selector)) {
                    this.usedSelectors.add(rule.selector);
                } else {
                    this.unusedSelectors.add(rule.selector);
                }
            } catch (error) {
                // 无效的选择器
                console.warn('Invalid selector:', rule.selector);
            }
        });
    }

    // 识别关键CSS
    identifyCriticalCSS(rules) {
        const criticalSelectors = [
            'body', 'html', 'header', 'nav', 'main',
            '.container', '.btn', '.card', '.loading',
            '[class*="grid"]', '[class*="flex"]',
            // 首屏可见的元素
            '.hero', '.banner', '.navbar', '.sidebar'
        ];

        const criticalRules = rules.filter(rule => {
            return criticalSelectors.some(selector => 
                rule.selector.includes(selector) ||
                this.isAboveFold(rule.selector)
            );
        });

        this.criticalCSS = criticalRules.map(rule => rule.cssText).join('\n');
    }

    // 检查是否在首屏
    isAboveFold(selector) {
        try {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                const rect = element.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    return true;
                }
            }
        } catch (error) {
            return false;
        }
        return false;
    }

    // 设置动态加载
    setupDynamicLoading() {
        // 延迟加载非关键CSS
        this.loadNonCriticalCSS();
        
        // 设置条件加载
        this.setupConditionalLoading();
        
        // 设置媒体查询优化
        this.optimizeMediaQueries();
    }

    // 加载非关键CSS
    loadNonCriticalCSS() {
        const nonCriticalFiles = [
            'styles/components.css',
            'styles/animations.css',
            'styles/utilities.css'
        ];

        // 在页面加载完成后加载
        if (document.readyState === 'complete') {
            this.loadCSSFiles(nonCriticalFiles);
        } else {
            window.addEventListener('load', () => {
                this.loadCSSFiles(nonCriticalFiles);
            });
        }
    }

    // 加载CSS文件
    loadCSSFiles(files) {
        files.forEach((file, index) => {
            setTimeout(() => {
                this.loadCSS(file);
            }, index * 100);
        });
    }

    // 加载单个CSS文件
    loadCSS(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = 'print'; // 先设为print避免阻塞
            
            link.onload = () => {
                link.media = 'all'; // 加载完成后改为all
                resolve();
            };
            
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    // 设置条件加载
    setupConditionalLoading() {
        // 根据页面类型加载特定CSS
        const pageName = this.getCurrentPageName();
        const pageSpecificCSS = this.getPageSpecificCSS(pageName);
        
        if (pageSpecificCSS) {
            this.loadCSS(pageSpecificCSS);
        }

        // 根据设备类型加载
        if (this.isMobile()) {
            this.loadCSS('styles/mobile.css');
        }

        // 根据用户偏好加载
        if (this.prefersDarkMode()) {
            this.loadCSS('styles/dark-mode.css');
        }
    }

    // 获取当前页面名称
    getCurrentPageName() {
        const path = window.location.pathname;
        return path.split('/').pop().replace('.html', '') || 'index';
    }

    // 获取页面特定CSS
    getPageSpecificCSS(pageName) {
        const pageCSS = {
            'history': 'styles/pages/history.css',
            'plagiarism': 'styles/pages/plagiarism.css',
            'aigc': 'styles/pages/aigc.css',
            'billing': 'styles/pages/billing.css',
            'profile': 'styles/pages/profile.css'
        };
        
        return pageCSS[pageName];
    }

    // 检查是否为移动设备
    isMobile() {
        return window.innerWidth <= 768 || 
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 检查是否偏好暗色模式
    prefersDarkMode() {
        return window.matchMedia && 
               window.matchMedia('(prefers-color-scheme: dark)').matches;
    }

    // 优化媒体查询
    optimizeMediaQueries() {
        // 移除不匹配的媒体查询
        this.removeUnmatchedMediaQueries();
        
        // 合并相似的媒体查询
        this.mergeSimilarMediaQueries();
    }

    // 移除不匹配的媒体查询
    removeUnmatchedMediaQueries() {
        const stylesheets = document.styleSheets;
        
        for (const stylesheet of stylesheets) {
            if (this.isAccessibleStylesheet(stylesheet)) {
                const rules = stylesheet.cssRules || stylesheet.rules;
                
                for (let i = rules.length - 1; i >= 0; i--) {
                    const rule = rules[i];
                    
                    if (rule.type === CSSRule.MEDIA_RULE) {
                        if (!window.matchMedia(rule.conditionText).matches) {
                            // 如果媒体查询不匹配，可以考虑移除（谨慎操作）
                            console.log('Unmatched media query:', rule.conditionText);
                        }
                    }
                }
            }
        }
    }

    // 合并相似的媒体查询
    mergeSimilarMediaQueries() {
        // 这里可以实现媒体查询的合并逻辑
        // 由于复杂性，这里只是记录日志
        console.log('📱 Media query optimization completed');
    }

    // 优化现有CSS
    optimizeExistingCSS() {
        // 内联关键CSS
        this.inlineCriticalCSS();
        
        // 压缩CSS
        this.compressCSS();
        
        // 移除重复规则
        this.removeDuplicateRules();
    }

    // 内联关键CSS
    inlineCriticalCSS() {
        if (this.criticalCSS && !document.querySelector('style[data-critical-inline]')) {
            const style = document.createElement('style');
            style.setAttribute('data-critical-inline', 'true');
            style.textContent = this.criticalCSS;
            document.head.insertBefore(style, document.head.firstChild);
            
            console.log('✅ Critical CSS inlined');
        }
    }

    // 压缩CSS
    compressCSS() {
        // 简单的CSS压缩（移除空白和注释）
        const styles = document.querySelectorAll('style');
        
        styles.forEach(style => {
            if (!style.hasAttribute('data-compressed')) {
                style.textContent = this.minifyCSS(style.textContent);
                style.setAttribute('data-compressed', 'true');
            }
        });
    }

    // 简单的CSS压缩
    minifyCSS(css) {
        return css
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
            .replace(/\s+/g, ' ') // 压缩空白
            .replace(/;\s*}/g, '}') // 移除最后的分号
            .replace(/\s*{\s*/g, '{') // 压缩大括号
            .replace(/}\s*/g, '}') // 压缩大括号
            .replace(/:\s*/g, ':') // 压缩冒号
            .replace(/;\s*/g, ';') // 压缩分号
            .trim();
    }

    // 移除重复规则
    removeDuplicateRules() {
        const seenRules = new Set();
        const styles = document.querySelectorAll('style');
        
        styles.forEach(style => {
            const rules = style.textContent.split('}').filter(rule => rule.trim());
            const uniqueRules = [];
            
            rules.forEach(rule => {
                const normalizedRule = rule.trim() + '}';
                if (!seenRules.has(normalizedRule)) {
                    seenRules.add(normalizedRule);
                    uniqueRules.push(rule);
                }
            });
            
            style.textContent = uniqueRules.join('}');
        });
    }

    // 生成CSS使用报告
    generateUsageReport() {
        const totalSelectors = this.usedSelectors.size + this.unusedSelectors.size;
        const usagePercentage = (this.usedSelectors.size / totalSelectors * 100).toFixed(2);
        
        return {
            total: totalSelectors,
            used: this.usedSelectors.size,
            unused: this.unusedSelectors.size,
            usagePercentage: usagePercentage + '%',
            criticalCSSSize: this.criticalCSS.length,
            unusedSelectors: Array.from(this.unusedSelectors).slice(0, 10) // 前10个未使用的选择器
        };
    }

    // 清理未使用的CSS
    cleanupUnusedCSS() {
        // 注意：这个操作需要谨慎，可能会影响动态内容
        console.warn('⚠️ CSS cleanup should be done carefully in production');
        
        const report = this.generateUsageReport();
        console.log('📊 CSS Usage Report:', report);
        
        return report;
    }

    // 监控CSS性能
    monitorCSSPerformance() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.name.endsWith('.css')) {
                        console.log('📊 CSS Load Time:', {
                            name: entry.name,
                            duration: entry.duration,
                            size: entry.transferSize
                        });
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
        }
    }
}

// 导出单例
export const cssOptimizer = new CSSOptimizer();
export default CSSOptimizer;
