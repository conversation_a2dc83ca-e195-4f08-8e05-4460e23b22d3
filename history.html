<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录 - 博晓文</title>
    <meta name="description" content="管理您的所有论文创作记录，随时查看和编辑">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8 lg:mb-12 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                历史记录
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                管理您的所有论文创作记录，随时查看和编辑
                                <span class="inline-block ml-2 text-blue-600">📚</span>
                            </p>

                            <!-- 特色标签 -->
                            <div class="flex flex-wrap gap-2 mt-4">
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">
                                    ✅ 云端同步
                                </span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                                    🔍 智能搜索
                                </span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                    📊 数据统计
                                </span>
                            </div>
                        </div>
                        <!-- 统计卡片 -->
                        <div class="hidden lg:flex space-x-6 xl:space-x-8">
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold gradient-text-blue">24</div>
                                <div class="text-sm text-gray-500">总论文数</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-green-600">18</div>
                                <div class="text-sm text-gray-500">已完成</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-yellow-600">4</div>
                                <div class="text-sm text-gray-500">进行中</div>
                            </div>
                        </div>
                    </div>

                    <!-- 移动端统计 -->
                    <div class="lg:hidden grid grid-cols-3 gap-3 mt-6">
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-blue-600">24</div>
                            <div class="text-xs text-gray-500">总论文数</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-green-600">18</div>
                            <div class="text-xs text-gray-500">已完成</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg shadow-md">
                            <div class="text-lg font-bold text-yellow-600">4</div>
                            <div class="text-xs text-gray-500">进行中</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white rounded-xl shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 mb-8 fade-in-up">
                    <div class="p-4 sm:p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold flex items-center gap-2">
                            <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="filter" class="w-4 h-4 text-white"></i>
                            </div>
                            搜索与筛选
                        </h3>
                    </div>
                    <div class="p-4 sm:p-6">
                        <div class="flex flex-col lg:flex-row lg:items-end lg:justify-between gap-4">
                            <!-- Search -->
                            <div class="flex-1 max-w-md">
                                <label class="form-label text-sm mb-2">搜索论文</label>
                                <div class="relative">
                                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                                    <input type="text" placeholder="输入论文标题关键词..." class="form-input pl-10" id="search-input">
                                </div>
                            </div>

                            <!-- Filters -->
                            <div class="flex flex-wrap gap-3 lg:gap-4">
                                <div class="min-w-[120px]">
                                    <label class="form-label text-sm mb-2">论文类型</label>
                                    <select class="form-input form-select" id="type-filter">
                                        <option value="">所有类型</option>
                                        <option value="academic">学术论文</option>
                                        <option value="review">文献综述</option>
                                        <option value="practice">实习报告</option>
                                        <option value="business">商业计划</option>
                                        <option value="research">调研报告</option>
                                        <option value="professional">专业报告</option>
                                        <option value="proposal">开题报告</option>
                                        <option value="other">其他类型</option>
                                    </select>
                                </div>

                                <div class="min-w-[120px]">
                                    <label class="form-label text-sm mb-2">状态筛选</label>
                                    <select class="form-input form-select" id="status-filter">
                                        <option value="">所有状态</option>
                                        <option value="completed">已完成</option>
                                        <option value="processing">生成中</option>
                                        <option value="draft">草稿</option>
                                    </select>
                                </div>

                                <div class="flex items-end">
                                    <button class="btn btn-primary btn-lg group" onclick="exportHistory()">
                                        <i data-lucide="download" class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform"></i>
                                        导出数据
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 快速筛选标签 -->
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex flex-wrap gap-2">
                                <span class="text-sm text-gray-600 mr-2">快速筛选:</span>
                                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors" onclick="quickFilter('today')">
                                    今日创建
                                </button>
                                <button class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full hover:bg-green-200 transition-colors" onclick="quickFilter('week')">
                                    本周创建
                                </button>
                                <button class="px-3 py-1 text-xs bg-purple-100 text-purple-800 rounded-full hover:bg-purple-200 transition-colors" onclick="quickFilter('large')">
                                    大型论文(>10k字)
                                </button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 transition-colors" onclick="clearFilters()">
                                    清除筛选
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Table -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
                        <h2 class="text-xl font-bold flex items-center gap-3">
                            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="file-text" class="w-5 h-5"></i>
                            </div>
                            论文列表
                        </h2>
                        <p class="text-blue-100 mt-2">查看和管理您的所有论文创作记录</p>
                    </div>
                    
                    <!-- Tabs -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" id="tabs">
                            <button class="py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm tab-button active" data-tab="all">
                                全部
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="completed">
                                已完成
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="processing">
                                进行中
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="draft">
                                草稿
                            </button>
                        </nav>
                    </div>
                    
                    <!-- Table Content -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">论文标题</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="history-table-body">
                                <!-- Table content will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="bg-white px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="btn btn-secondary" id="prev-mobile">上一页</button>
                            <button class="btn btn-secondary" id="next-mobile">下一页</button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示 <span class="font-medium" id="start-item">1</span> 到 <span class="font-medium" id="end-item">10</span> 
                                    共 <span class="font-medium" id="total-items">24</span> 条记录
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                                    <!-- Pagination buttons will be populated by JavaScript -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/global-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/history.js"></script>

    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('history');
        });
    </script>
</body>
</html>
