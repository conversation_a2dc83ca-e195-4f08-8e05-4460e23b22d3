/**
 * 资源加载管理器
 * 实现预加载、懒加载、缓存等优化策略
 */

import { PerformanceConfig } from '../config/performance-config.js';

export class ResourceLoader {
    constructor() {
        this.loadedResources = new Set();
        this.loadingPromises = new Map();
        this.cache = new Map();
        this.preloadQueue = [];
        this.lazyLoadObserver = null;
        
        this.init();
    }

    init() {
        this.setupPreloading();
        this.setupLazyLoading();
        this.setupCaching();
    }

    // 预加载关键资源
    setupPreloading() {
        if (!PerformanceConfig.loading.preload.enabled) return;

        // 预加载关键CSS
        this.preloadCSS('styles/critical.css', true);
        
        // 预加载关键JavaScript
        this.preloadScript('js/core.js', true);
        
        // 预加载关键页面
        PerformanceConfig.loading.preload.criticalPages.forEach(page => {
            this.preloadPage(page);
        });

        // DNS预解析
        this.setupDNSPrefetch();
    }

    // 预加载CSS文件
    preloadCSS(href, critical = false) {
        if (this.loadedResources.has(href)) return Promise.resolve();

        const link = document.createElement('link');
        link.rel = critical ? 'preload' : 'prefetch';
        link.as = 'style';
        link.href = href;
        
        if (critical) {
            link.onload = () => {
                link.rel = 'stylesheet';
            };
        }

        document.head.appendChild(link);
        this.loadedResources.add(href);

        return new Promise((resolve, reject) => {
            link.onload = resolve;
            link.onerror = reject;
        });
    }

    // 预加载JavaScript文件
    preloadScript(src, critical = false) {
        if (this.loadedResources.has(src)) return Promise.resolve();

        const link = document.createElement('link');
        link.rel = critical ? 'preload' : 'prefetch';
        link.as = 'script';
        link.href = src;

        document.head.appendChild(link);
        this.loadedResources.add(src);

        return new Promise((resolve, reject) => {
            link.onload = resolve;
            link.onerror = reject;
        });
    }

    // 预加载页面
    preloadPage(href) {
        if (this.loadedResources.has(href)) return;

        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;

        document.head.appendChild(link);
        this.loadedResources.add(href);
    }

    // 设置DNS预解析
    setupDNSPrefetch() {
        const domains = PerformanceConfig.network.connection.dns.prefetch;
        
        domains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = domain;
            document.head.appendChild(link);
        });

        // 预连接关键域名
        PerformanceConfig.network.connection.dns.preconnect.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = domain;
            document.head.appendChild(link);
        });
    }

    // 设置懒加载
    setupLazyLoading() {
        if (!PerformanceConfig.loading.lazyLoad.enabled) return;

        const options = {
            threshold: PerformanceConfig.loading.lazyLoad.threshold,
            rootMargin: PerformanceConfig.loading.lazyLoad.rootMargin
        };

        this.lazyLoadObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadLazyResource(entry.target);
                    this.lazyLoadObserver.unobserve(entry.target);
                }
            });
        }, options);

        // 观察所有懒加载元素
        this.observeLazyElements();
    }

    // 观察懒加载元素
    observeLazyElements() {
        // 懒加载图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.lazyLoadObserver.observe(img);
        });

        // 懒加载模块
        document.querySelectorAll('[data-lazy-module]').forEach(element => {
            this.lazyLoadObserver.observe(element);
        });
    }

    // 加载懒加载资源
    async loadLazyResource(element) {
        if (element.tagName === 'IMG') {
            await this.loadLazyImage(element);
        } else if (element.dataset.lazyModule) {
            await this.loadLazyModule(element.dataset.lazyModule);
        }
    }

    // 懒加载图片
    async loadLazyImage(img) {
        const src = img.dataset.src;
        if (!src) return;

        try {
            // 创建新图片对象预加载
            const image = new Image();
            image.src = src;
            
            await new Promise((resolve, reject) => {
                image.onload = resolve;
                image.onerror = reject;
            });

            // 应用淡入效果
            img.style.opacity = '0';
            img.src = src;
            img.removeAttribute('data-src');

            // 淡入动画
            img.style.transition = `opacity ${PerformanceConfig.loading.lazyLoad.images.fadeInDuration}ms ease`;
            setTimeout(() => {
                img.style.opacity = '1';
            }, 10);

        } catch (error) {
            console.error('Failed to load lazy image:', src, error);
            // 显示错误占位符
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yPC90ZXh0Pjwvc3ZnPg==';
        }
    }

    // 懒加载模块
    async loadLazyModule(moduleName) {
        if (this.loadingPromises.has(moduleName)) {
            return this.loadingPromises.get(moduleName);
        }

        const promise = this.dynamicImport(moduleName);
        this.loadingPromises.set(moduleName, promise);

        try {
            const module = await promise;
            this.cache.set(moduleName, module);
            return module;
        } catch (error) {
            console.error('Failed to load lazy module:', moduleName, error);
            this.loadingPromises.delete(moduleName);
            throw error;
        }
    }

    // 动态导入模块
    async dynamicImport(moduleName) {
        const moduleMap = {
            'essay-forms': () => import('../essay-forms.js'),
            'history': () => import('../history.js'),
            'plagiarism': () => import('../plagiarism.js'),
            'billing': () => import('../billing.js'),
            'profile': () => import('../profile.js'),
            'aigc': () => import('../aigc.js')
        };

        const importFn = moduleMap[moduleName];
        if (!importFn) {
            throw new Error(`Unknown module: ${moduleName}`);
        }

        return await importFn();
    }

    // 设置缓存
    setupCaching() {
        // 实现Service Worker缓存
        if ('serviceWorker' in navigator && PerformanceConfig.cache.serviceWorker.enabled) {
            this.registerServiceWorker();
        }

        // 实现内存缓存
        this.setupMemoryCache();
    }

    // 注册Service Worker
    async registerServiceWorker() {
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered:', registration);
        } catch (error) {
            console.error('Service Worker registration failed:', error);
        }
    }

    // 设置内存缓存
    setupMemoryCache() {
        // 实现LRU缓存
        this.memoryCache = new LRUCache(PerformanceConfig.cache.memory.maxSize);
    }

    // 获取缓存的资源
    getCachedResource(key) {
        return this.cache.get(key) || this.memoryCache?.get(key);
    }

    // 缓存资源
    cacheResource(key, resource) {
        this.cache.set(key, resource);
        this.memoryCache?.set(key, resource);
    }

    // 清理缓存
    clearCache() {
        this.cache.clear();
        this.memoryCache?.clear();
    }
}

// LRU缓存实现
class LRUCache {
    constructor(maxSize) {
        this.maxSize = maxSize;
        this.cache = new Map();
    }

    get(key) {
        if (this.cache.has(key)) {
            const value = this.cache.get(key);
            // 移到最后（最近使用）
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }

    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // 删除最久未使用的项
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    clear() {
        this.cache.clear();
    }
}

// 全局资源加载器实例
export const resourceLoader = new ResourceLoader();
