// 移除ES6导入，改为直接使用配置对象
// import { AppConfig } from './config/app-config.js';
// import { UIConfig } from './config/ui-config.js';
// import { PerformanceConfig } from './config/performance-config.js';

// 论文类型配置 - 完全参照原项目
const ESSAY_TYPES = [
    {
        id: "graduation",
        name: "毕业论文",
        icon: "graduation-cap",
        description: "本科、硕士、博士毕业论文写作",
        color: "from-orange-500 to-orange-600"
    },
    {
        id: "literature",
        name: "文献综述",
        icon: "book-open",
        description: "系统性文献回顾与分析",
        color: "from-red-500 to-red-600"
    },
    {
        id: "proposal",
        name: "开题报告",
        icon: "file-text",
        description: "研究提案、项目申请、学术规划",
        color: "from-blue-500 to-blue-600"
    },
    {
        id: "task",
        name: "任务书",
        icon: "clipboard-list",
        description: "毕业设计任务书、课程设计任务书",
        color: "from-cyan-500 to-cyan-600"
    },
    {
        id: "journal",
        name: "期刊论文",
        icon: "newspaper",
        description: "学术期刊投稿论文写作",
        color: "from-green-500 to-green-600"
    },
    {
        id: "bachelor",
        name: "专升本论文",
        icon: "book-marked",
        description: "专升本毕业论文写作",
        color: "from-lime-500 to-lime-600"
    },
    {
        id: "term",
        name: "期末论文",
        icon: "pen-tool",
        description: "期末课程论文、学期论文",
        color: "from-purple-500 to-purple-600"
    },
    {
        id: "course",
        name: "课程论文",
        icon: "file-check",
        description: "课程作业论文、专业课论文",
        color: "from-indigo-500 to-indigo-600"
    },
    {
        id: "practice",
        name: "实习报告",
        icon: "settings",
        description: "实习总结、实践报告、工作汇报",
        color: "from-teal-500 to-teal-600"
    },
    {
        id: "business",
        name: "创业策划",
        icon: "briefcase",
        description: "创业计划书、商业策划方案",
        color: "from-blue-500 to-blue-600"
    },
    {
        id: "research",
        name: "研究论文",
        icon: "search",
        description: "科研论文、学术研究报告",
        color: "from-emerald-500 to-emerald-600"
    },
    {
        id: "professional",
        name: "职业规划",
        icon: "trending-up",
        description: "职业生涯规划、发展规划书",
        color: "from-pink-500 to-pink-600"
    }
];

// 全局状态
let currentEssayType = 'graduation';
let formData = {};
let isGenerating = false;

// DOM 元素
let elements = {};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM内容已加载');

    // 延迟执行以确保所有外部脚本都已加载
    setTimeout(() => {
        console.log('开始初始化应用...');

        initializeElements();
        console.log('元素初始化完成');

        initializeLucideIcons();
        console.log('图标库初始化完成');

        // 检查是否有静态内容，如果没有则渲染动态内容
        const desktopContainer = document.getElementById('essay-types-desktop');
        const hasStaticContent = desktopContainer && desktopContainer.querySelector('.static-essay-type');

        if (!hasStaticContent) {
            renderEssayTypes();
            console.log('动态论文类型渲染完成');
        } else {
            console.log('使用静态论文类型内容');
        }

        bindEvents();
        console.log('事件绑定完成');

        loadSavedData();
        console.log('数据加载完成');

        // 默认选择毕业论文并显示表单（不滚动）
        selectEssayType('graduation', false);
        console.log('默认论文类型选择完成');

        initializeAnimations();
        setupPerformanceOptimizations();
        console.log('应用初始化完成');
    }, 100);
});



// 初始化动画
function initializeAnimations() {
    // 添加页面加载动画
    document.body.classList.add('fade-in');

    // 设置交错动画延迟
    const animatedElements = document.querySelectorAll('.fade-in-up');
    animatedElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
    });

    // 添加滚动触发动画
    setupScrollAnimations();
}

// 设置滚动动画
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // 观察所有需要动画的元素
    document.querySelectorAll('.fade-in-up, .slide-in-right').forEach(el => {
        observer.observe(el);
    });
}

// 性能优化设置
function setupPerformanceOptimizations() {
    // 图片懒加载
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }

    // 预加载关键资源
    preloadCriticalResources();

    // 设置防抖和节流
    setupDebounceThrottle();
}

// 预加载关键资源
function preloadCriticalResources() {
    const criticalPages = ['history.html', 'plagiarism.html'];

    criticalPages.forEach(page => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = page;
        document.head.appendChild(link);
    });
}

// 设置防抖和节流
function setupDebounceThrottle() {
    // 窗口大小改变的节流处理
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            handleWindowResize();
        }, 250);
    });
}

// 处理窗口大小改变
function handleWindowResize() {
    // 重新计算布局
    if (window.innerWidth >= 1024) {
        closeMobileMenu();
    }

    // 更新动画状态
    updateAnimationStates();
}

// 更新动画状态
function updateAnimationStates() {
    const isMobile = window.innerWidth < 768;
    const animatedElements = document.querySelectorAll('.fade-in-up');

    animatedElements.forEach(element => {
        if (isMobile) {
            element.style.animationDuration = '0.3s';
        } else {
            element.style.animationDuration = '0.6s';
        }
    });
}

// 初始化DOM元素引用
function initializeElements() {
    elements = {
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn'),
        essayTypesDesktop: document.getElementById('essay-types-desktop'),
        essayTypesMobile: document.getElementById('essay-types-mobile'),
        formWrapper: document.getElementById('form-section') || document.getElementById('form-wrapper'),
        formContainer: document.getElementById('form-container'),
        formTitle: document.getElementById('form-title'),
        formDescription: document.getElementById('form-description'),
        mobileTip: document.getElementById('mobile-tip'),
        recentRecords: document.getElementById('recent-records'),
        quickActions: document.getElementById('quick-actions')
    };
    
    // 渲染侧边栏内容
    renderSidebarContent();
}

// 初始化Lucide图标
function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// 渲染论文类型选择器
function renderEssayTypes() {
    console.log('开始渲染论文类型...');
    console.log('ESSAY_TYPES 数据:', ESSAY_TYPES);
    console.log('Desktop容器:', elements.essayTypesDesktop);
    console.log('Mobile容器:', elements.essayTypesMobile);
    
    // 如果元素还没有初始化，重新获取
    if (!elements.essayTypesDesktop || !elements.essayTypesMobile) {
        elements.essayTypesDesktop = document.getElementById('essay-types-desktop');
        elements.essayTypesMobile = document.getElementById('essay-types-mobile');
        console.log('重新获取元素:', elements.essayTypesDesktop, elements.essayTypesMobile);
    }
    
    const desktopHtml = ESSAY_TYPES.map((type, index) => `
        <div class="essay-type-card group cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-4 ${
            currentEssayType === type.id
                ? 'ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg'
                : 'hover:-translate-y-1'
        } fade-in-up"
             data-type="${type.id}"
             onclick="selectEssayType('${type.id}')"
             style="animation-delay: ${index * 0.1}s;">
            <div class="text-center">
                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg ${
                    currentEssayType === type.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                        : `bg-gradient-to-r ${type.color}`
                } group-hover:scale-110 transition-transform duration-300">
                    <i data-lucide="${type.icon}" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="font-bold text-gray-900 mb-1 text-sm leading-tight ${
                    currentEssayType === type.id ? '' : 'group-hover:text-blue-600'
                } transition-colors">${type.name}</h3>
                <div class="text-xs text-gray-500">专业定制</div>
            </div>
        </div>
    `).join('');

    const mobileHtml = ESSAY_TYPES.map((type, index) => `
        <div class="essay-type-card group cursor-pointer transition-all duration-300 hover:shadow-xl bg-white rounded-xl shadow-lg border border-gray-200 p-3 ${
            currentEssayType === type.id
                ? 'ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-blue-100'
                : 'hover:-translate-y-1'
        } fade-in-up"
             data-type="${type.id}"
             onclick="selectEssayType('${type.id}')"
             style="animation-delay: ${index * 0.05}s;">
            <div class="text-center">
                <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg ${
                    currentEssayType === type.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                        : `bg-gradient-to-r ${type.color}`
                } group-hover:scale-110 transition-transform duration-300">
                    <i data-lucide="${type.icon}" class="w-5 h-5 text-white"></i>
                </div>
                <h3 class="font-bold text-gray-900 text-sm leading-tight ${
                    currentEssayType === type.id ? '' : 'group-hover:text-blue-600'
                } transition-colors">${type.name}</h3>
            </div>
        </div>
    `).join('');

    console.log('生成的桌面HTML长度:', desktopHtml.length);
    console.log('生成的移动HTML长度:', mobileHtml.length);

    if (elements.essayTypesDesktop) {
        // 移除静态内容并设置新的HTML
        elements.essayTypesDesktop.innerHTML = desktopHtml;
        console.log('桌面版HTML已设置');
    } else {
        console.error('桌面版容器未找到');
    }
    
    if (elements.essayTypesMobile) {
        // 移除静态内容并设置新的HTML
        elements.essayTypesMobile.innerHTML = mobileHtml;
        console.log('移动版HTML已设置');
    } else {
        console.error('移动版容器未找到');
    }
    
    // 如果JavaScript渲染成功，移除所有静态内容的标记
    document.querySelectorAll('.static-essay-type').forEach(el => {
        el.classList.add('js-replaced');
    });

    // 重新初始化图标
    setTimeout(() => {
        initializeLucideIcons();
        console.log('图标已重新初始化');
    }, 100);
}

// 选择论文类型
function selectEssayType(typeId, shouldScroll = true) {
    console.log('选择论文类型:', typeId, '是否滚动:', shouldScroll);
    currentEssayType = typeId;

    // 更新UI - 桌面端和移动端
    document.querySelectorAll('.essay-type-card').forEach(card => {
        const isSelected = card.dataset.type === typeId;

        if (isSelected) {
            card.classList.add('active', 'ring-2', 'ring-blue-500', 'bg-gradient-to-br', 'from-blue-50', 'to-blue-100', 'shadow-lg');
            card.classList.remove('bg-gradient-to-br', 'from-white', 'to-gray-50');

            // 更新图标颜色
            const icon = card.querySelector('.essay-type-icon');
            if (icon) {
                icon.className = icon.className.replace(/bg-gradient-to-r from-\w+-\d+ to-\w+-\d+/, 'bg-gradient-to-r from-blue-500 to-purple-600');
            }
        } else {
            card.classList.remove('active', 'ring-2', 'ring-blue-500', 'from-blue-50', 'to-blue-100', 'shadow-lg');
            card.classList.add('bg-gradient-to-br', 'from-white', 'to-gray-50');

            // 恢复原始图标颜色
            const type = ESSAY_TYPES.find(t => t.id === card.dataset.type);
            const icon = card.querySelector('.essay-type-icon');
            if (icon && type) {
                icon.className = icon.className.replace(/bg-gradient-to-r from-\w+-\d+ to-\w+-\d+/, `bg-gradient-to-r ${type.color}`);
            }
        }
    });

    // 显示表单容器并添加动画
    if (elements.formWrapper) {
        elements.formWrapper.style.display = 'block';
        elements.formWrapper.classList.remove('hidden');
        elements.formWrapper.classList.add('fade-in-up');

        // 只在用户点击时才滚动到表单位置
        if (shouldScroll) {
            setTimeout(() => {
                elements.formWrapper.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            }, 300);
        }
    }

    // 隐藏移动端提示
    if (elements.mobileTip && window.innerWidth < 1024) {
        elements.mobileTip.style.display = 'none';
    }

    // 更新表单
    renderForm();

    // 保存状态
    saveToLocalStorage('currentEssayType', typeId);

    // 添加选择反馈动画
    const selectedCard = document.querySelector(`[data-type="${typeId}"]`);
    if (selectedCard) {
        selectedCard.classList.add('scale-in');
        setTimeout(() => {
            selectedCard.classList.remove('scale-in');
        }, 300);
    }
}

// 渲染表单
function renderForm() {
    console.log('开始渲染表单, 当前类型:', currentEssayType);
    
    const type = ESSAY_TYPES.find(t => t.id === currentEssayType);
    if (!type) {
        console.error('未找到论文类型:', currentEssayType);
        return;
    }
    
    console.log('找到论文类型:', type);
    
    // 更新标题和描述
    if (elements.formTitle) {
        elements.formTitle.textContent = type.name + ' - AI智能写作';
        console.log('更新表单标题:', elements.formTitle.textContent);
    }
    if (elements.formDescription) {
        elements.formDescription.textContent = `填写${type.name}要求，AI将为您生成专业的学术内容`;
        console.log('更新表单描述:', elements.formDescription.textContent);
    }
    
    // 检查generateFormHTML函数是否可用
    console.log('检查generateFormHTML函数:', typeof generateFormHTML);
    
    // 渲染表单内容 - 使用essay-forms.js中的函数
    if (typeof generateFormHTML === 'function') {
        console.log('开始生成表单HTML...');
        const formHtml = generateFormHTML(currentEssayType);
        console.log('生成的表单HTML长度:', formHtml.length);
        
        if (elements.formContainer) {
            elements.formContainer.innerHTML = formHtml;
            console.log('表单HTML已设置到容器');
            
            // 重新绑定事件
            if (typeof bindFormEvents === 'function') {
                bindFormEvents();
                console.log('表单事件已绑定');
            }
            
            // 填充保存的数据
            populateFormData();
            console.log('表单数据已填充');
            
            // 重新初始化图标
            setTimeout(() => {
                initializeLucideIcons();
                console.log('表单图标已重新初始化');
            }, 100);
        } else {
            console.error('表单容器未找到');
        }
    } else {
        console.error('generateFormHTML函数未找到，essay-forms.js可能未正确加载');
        // 显示备用表单
        showFallbackForm(type);
    }
}

// 备用表单显示
function showFallbackForm(type) {
    console.log('显示备用表单');
    if (elements.formContainer) {
        elements.formContainer.innerHTML = `
            <div class="p-6 text-center">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-gradient-to-r ${type.color} rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="${type.icon}" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">${type.name}</h3>
                    <p class="text-gray-600 mb-6">${type.description}</p>
                </div>
                <div class="space-y-4">
                    <div class="form-group">
                        <label class="form-label">论文标题 *</label>
                        <input type="text" name="title" class="form-input" placeholder="请输入论文标题..." required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">具体要求 *</label>
                        <textarea name="requirements" class="form-input form-textarea" rows="4" placeholder="请详细描述论文的具体要求..." required></textarea>
                    </div>
                    <button type="button" onclick="generateEssay()" class="btn btn-primary btn-lg w-full">
                        <i data-lucide="edit-3" class="w-5 h-5 mr-2"></i>
                        开始生成${type.name}
                    </button>
                </div>
            </div>
        `;
        initializeLucideIcons();
    }
}

// 绑定事件
function bindEvents() {
    // 移动端菜单切换
    if (elements.mobileMenuBtn) {
        elements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            !elements.sidebar.contains(e.target) && 
            !elements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
    
    // 窗口大小改变时关闭移动端菜单
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeMobileMenu();
        }
    });
}

// 绑定表单事件
function bindFormEvents() {
    const form = document.getElementById('essay-form');
    if (!form) return;
    
    // 表单输入事件
    form.addEventListener('input', function(e) {
        const { name, value } = e.target;
        formData[name] = value;
        saveFormData();
    });
    
    // 表单变化事件
    form.addEventListener('change', function(e) {
        const { name, value } = e.target;
        formData[name] = value;
        saveFormData();
        
        // 处理自定义字数
        if (name === 'wordCount' && value === 'custom') {
            showCustomWordCountInput();
        }
    });
    
    // 重新初始化图标
    initializeLucideIcons();
}

// 切换移动端菜单
function toggleMobileMenu() {
    elements.sidebar.classList.toggle('-translate-x-full');
}

// 关闭移动端菜单
function closeMobileMenu() {
    elements.sidebar.classList.add('-translate-x-full');
}

// 显示自定义字数输入
function showCustomWordCountInput() {
    const wordCount = prompt('请输入自定义字数：');
    if (wordCount && !isNaN(wordCount)) {
        formData.wordCount = parseInt(wordCount);
        saveFormData();
    }
}

// 生成大纲
async function generateOutline() {
    if (!validateForm()) return;
    
    showLoading('正在生成大纲...');
    
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const outline = generateMockOutline();
        showOutlineModal(outline);
    } catch (error) {
        showError('生成大纲失败，请重试');
    } finally {
        hideLoading();
    }
}

// 生成论文
async function generateEssay() {
    if (!validateForm()) return;
    
    const generateBtn = document.getElementById('generate-btn');
    if (!generateBtn) return;
    
    isGenerating = true;
    generateBtn.disabled = true;
    generateBtn.innerHTML = `
        <div class="spinner mr-2"></div>
        AI生成中...
    `;
    
    try {
        // 模拟API调用
        await simulateEssayGeneration();
        
        showSuccess('论文生成成功！');
        // 这里可以跳转到结果页面或显示结果
        
    } catch (error) {
        showError('生成失败，请重试');
    } finally {
        isGenerating = false;
        generateBtn.disabled = false;
        generateBtn.innerHTML = `
            <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
            一键生成论文
        `;
        initializeLucideIcons();
    }
}

// 验证表单
function validateForm() {
    const form = document.getElementById('essay-form');
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('border-red-500');
            isValid = false;
        } else {
            field.classList.remove('border-red-500');
        }
    });
    
    if (!isValid) {
        showError('请填写所有必填字段');
    }
    
    return isValid;
}

// 模拟论文生成过程
async function simulateEssayGeneration() {
    const steps = [
        '分析论文要求...',
        '搜索相关文献...',
        '生成论文大纲...',
        '撰写论文内容...',
        '优化语言表达...',
        '完成最终检查...'
    ];
    
    for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        updateGenerationProgress(steps[i], (i + 1) / steps.length * 100);
    }
}

// 更新生成进度
function updateGenerationProgress(message, progress) {
    // 这里可以显示进度条和消息
    console.log(`${message} (${progress.toFixed(0)}%)`);
}

// 生成模拟大纲
function generateMockOutline() {
    return [
        '1. 引言',
        '1.1 研究背景',
        '1.2 研究意义',
        '1.3 研究目标',
        '2. 文献综述',
        '2.1 相关理论基础',
        '2.2 国内外研究现状',
        '2.3 研究空白与不足',
        '3. 研究方法',
        '3.1 研究设计',
        '3.2 数据收集',
        '3.3 分析方法',
        '4. 结果与分析',
        '4.1 主要发现',
        '4.2 结果分析',
        '4.3 讨论',
        '5. 结论',
        '5.1 研究总结',
        '5.2 研究贡献',
        '5.3 局限性与展望'
    ];
}

// 显示大纲模态框
function showOutlineModal(outline) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
        <div class="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900">论文大纲</h3>
            </div>
            <div class="p-6">
                <ul class="space-y-2">
                    ${outline.map(item => `<li class="text-gray-700">${item}</li>`).join('')}
                </ul>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end space-x-4">
                <button onclick="this.closest('.fixed').remove()" class="btn btn-secondary">关闭</button>
                <button onclick="this.closest('.fixed').remove(); generateEssay();" class="btn btn-primary">确认并生成论文</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// 工具函数
function showLoading(message = '加载中...') {
    // 实现加载提示
    console.log(message);
}

function hideLoading() {
    // 隐藏加载提示
    console.log('Loading hidden');
}

function showSuccess(message) {
    showToast(message, 'success');
}

function showError(message) {
    showToast(message, 'error');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 数据持久化
function saveFormData() {
    saveToLocalStorage('formData', formData);
}

function loadSavedData() {
    const savedType = getFromLocalStorage('currentEssayType');
    if (savedType && ESSAY_TYPES.find(t => t.id === savedType)) {
        currentEssayType = savedType;
        renderEssayTypes();
    }
    
    const savedFormData = getFromLocalStorage('formData');
    if (savedFormData) {
        formData = savedFormData;
    }
}

function populateFormData() {
    Object.keys(formData).forEach(key => {
        const field = document.querySelector(`[name="${key}"]`);
        if (field) {
            field.value = formData[key];
        }
    });
}

function saveToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Failed to save to localStorage:', error);
    }
}

function getFromLocalStorage(key) {
    try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        console.error('Failed to get from localStorage:', error);
        return null;
    }
}

// 渲染侧边栏内容
function renderSidebarContent() {
    renderRecentRecords();
    renderQuickActions();
}

// 渲染最近记录
function renderRecentRecords() {
    if (!elements.recentRecords) return;
    
    const recentRecords = [
        {
            id: 1,
            title: "基于深度学习的图像识别技术研究",
            type: "毕业论文",
            wordCount: "15,000字",
            status: "completed",
            statusText: "已完成",
            statusClass: "bg-green-100 text-green-800"
        },
        {
            id: 2,
            title: "人工智能在教育领域的应用分析",
            type: "期刊论文",
            wordCount: "8,000字",
            status: "processing",
            statusText: "生成中",
            statusClass: "bg-yellow-100 text-yellow-800"
        },
        {
            id: 3,
            title: "企业数字化转型策略研究",
            type: "研究论文",
            wordCount: "12,000字",
            status: "draft",
            statusText: "草稿",
            statusClass: "bg-gray-100 text-gray-800"
        }
    ];
    
    const html = recentRecords.map(record => `
        <div class="group p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:from-gray-100 hover:to-gray-200 transition-all cursor-pointer hover:shadow-md transform hover:scale-105" onclick="viewRecord(${record.id})">
            <h4 class="font-medium text-gray-900 text-sm mb-2 truncate group-hover:text-blue-600 transition-colors">${record.title}</h4>
            <div class="flex items-center justify-between">
                <div class="flex flex-col space-y-1">
                    <span class="text-xs text-gray-500">${record.type} • ${record.wordCount}</span>
                </div>
                <span class="text-xs px-2 py-1 rounded-full ${record.statusClass} group-hover:scale-110 transition-transform">${record.statusText}</span>
            </div>
            <div class="mt-2 flex items-center text-xs text-gray-400">
                <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                2小时前
            </div>
        </div>
    `).join('');
    
    elements.recentRecords.innerHTML = html;
    initializeLucideIcons();
}

// 渲染快捷操作
function renderQuickActions() {
    if (!elements.quickActions) return;
    
    const quickActions = [
        {
            id: 'plagiarism',
            title: '论文查重',
            description: '专业查重检测',
            icon: 'file-check',
            color: 'from-red-500 to-red-600',
            bgColor: 'from-red-50 to-red-100 hover:from-red-100 hover:to-red-200',
            href: 'plagiarism.html'
        },
        {
            id: 'aigc',
            title: 'AIGC降重',
            description: '智能降重优化',
            icon: 'refresh-cw',
            color: 'from-cyan-500 to-cyan-600',
            bgColor: 'from-cyan-50 to-cyan-100 hover:from-cyan-100 hover:to-cyan-200',
            href: 'aigc.html'
        },
        {
            id: 'format',
            title: '格式调整',
            description: '自动格式化',
            icon: 'align-left',
            color: 'from-purple-500 to-purple-600',
            bgColor: 'from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200',
            href: '#'
        },
        {
            id: 'export',
            title: '导出下载',
            description: '多格式导出',
            icon: 'download',
            color: 'from-green-500 to-green-600',
            bgColor: 'from-green-50 to-green-100 hover:from-green-100 hover:to-green-200',
            href: '#'
        }
    ];
    
    const html = quickActions.map(action => `
        <button class="group w-full p-4 bg-gradient-to-r ${action.bgColor} rounded-xl transition-all text-left hover:shadow-lg transform hover:scale-105" onclick="handleQuickAction('${action.id}', '${action.href}')">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-r ${action.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                    <i data-lucide="${action.icon}" class="w-5 h-5 text-white"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">${action.title}</h4>
                    <p class="text-xs text-gray-600">${action.description}</p>
                </div>
                <i data-lucide="arrow-right" class="w-4 h-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all"></i>
            </div>
        </button>
    `).join('');
    
    elements.quickActions.innerHTML = html;
    initializeLucideIcons();
}

// 查看记录
function viewRecord(recordId) {
    showToast(`正在加载记录 #${recordId}...`, 'info');
    // 这里可以添加跳转到历史记录页面的逻辑
    setTimeout(() => {
        window.location.href = `history.html?id=${recordId}`;
    }, 500);
}

// 处理快捷操作
function handleQuickAction(actionId, href) {
    if (href === '#') {
        switch(actionId) {
            case 'format':
                showToast('格式调整功能即将上线', 'info');
                break;
            case 'export':
                showToast('请先生成论文内容', 'warning');
                break;
            default:
                showToast('功能开发中...', 'info');
        }
    } else {
        window.location.href = href;
    }
}

// 确保关键函数在全局作用域可用
window.selectEssayType = selectEssayType;
window.generateEssay = generateEssay;
window.generateOutline = generateOutline;
window.renderEssayTypes = renderEssayTypes;
window.renderForm = renderForm;
window.validateForm = validateForm;
window.viewRecord = viewRecord;
window.handleQuickAction = handleQuickAction;
