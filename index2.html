
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博晓文 - 智能AI论文写作平台</title>
    <meta name="description" content="专业的AI论文写作平台，支持毕业论文、期刊论文、文献综述等多种类型，3秒生成大纲，100%原创保证">
    <meta name="keywords" content="AI论文写作,毕业论文,期刊论文,文献综述,智能写作,学术论文">
    
    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://unpkg.com">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            500: '#64748b',
                            600: '#475569'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'slide-in-right': 'slideInRight 0.6s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
                        'gradient-shift': 'gradientShift 3s ease-in-out infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideInRight: {
                            '0%': { opacity: '0', transform: 'translateX(20px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.9)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        },
                        gradientShift: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Meta tags for better SEO -->
    <meta name="author" content="博晓文">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://bxw.com">
    
    <!-- Open Graph -->
    <meta property="og:title" content="博晓文 - 智能AI论文写作平台">
    <meta property="og:description" content="专业的AI论文写作平台，支持多种论文类型，3秒生成大纲，100%原创保证">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://bxw.com">
    
    <!-- Performance optimization -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="preload" href="styles/main.css" as="style">
</head>

<body class="bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-4 sm:p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                
                <!-- Hero Section with enhanced design -->
                <section class="mb-8 lg:mb-12 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 rounded-3xl"></div>
                    <div class="relative z-10 p-6 lg:p-8">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div class="mb-6 lg:mb-0 max-w-3xl">
                                <div class="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4 animate-bounce-gentle">
                                    <i data-lucide="sparkles" class="w-4 h-4"></i>
                                    全新AI写作体验
                                </div>
                                
                                <h1 class="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 leading-tight">
                                    <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-shift bg-300% animate-fade-in-up">
                                        智能AI论文写作平台
                                    </span>
                                </h1>
                                
                                <p class="text-lg lg:text-xl text-gray-600 leading-relaxed mb-6 animate-fade-in-up" style="animation-delay: 0.2s;">
                                    选择您需要的论文类型，AI将为您智能生成专业、原创的学术内容
                                    <span class="inline-block ml-2 text-2xl animate-bounce-gentle">✨</span>
                                </p>

                                <!-- Enhanced feature tags -->
                                <div class="flex flex-wrap gap-3 mb-6 animate-fade-in-up" style="animation-delay: 0.3s;">
                                    <div class="group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 cursor-pointer">
                                        <i data-lucide="zap" class="w-4 h-4 group-hover:scale-110 transition-transform"></i>
                                        🚀 3秒生成大纲
                                    </div>
                                    <div class="group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 cursor-pointer">
                                        <i data-lucide="check-circle" class="w-4 h-4 group-hover:scale-110 transition-transform"></i>
                                        ✅ 100%原创保证
                                    </div>
                                    <div class="group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 cursor-pointer">
                                        <i data-lucide="refresh-cw" class="w-4 h-4 group-hover:scale-110 transition-transform"></i>
                                        🔄 无限次修改
                                    </div>
                                    <div class="group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-100 to-yellow-100 text-orange-800 rounded-full text-sm font-medium hover:shadow-lg transition-all duration-300 cursor-pointer">
                                        <i data-lucide="shield-check" class="w-4 h-4 group-hover:scale-110 transition-transform"></i>
                                        🛡️ 专业保障
                                    </div>
                                </div>

                                <!-- CTA Buttons -->
                                <div class="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style="animation-delay: 0.4s;">
                                    <button class="btn btn-primary btn-lg group px-8 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300" onclick="scrollToEssayTypes()">
                                        <i data-lucide="edit-3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                        开始创作
                                        <i data-lucide="arrow-right" class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"></i>
                                    </button>
                                    <button class="btn btn-outline btn-lg px-8 py-4 text-lg font-semibold border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 transition-all duration-300" onclick="showDemoModal()">
                                        <i data-lucide="play-circle" class="w-5 h-5 mr-2"></i>
                                        观看演示
                                    </button>
                                </div>
                            </div>

                            <!-- Enhanced stats cards -->
                            <div class="hidden lg:grid grid-cols-1 gap-6 ml-8">
                                <div class="group bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20 animate-float">
                                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform">
                                        <i data-lucide="cpu" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div class="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent text-center">AI智能</div>
                                    <div class="text-gray-600 text-center">专业写作助手</div>
                                </div>
                                
                                <div class="group bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20 animate-float" style="animation-delay: 0.2s;">
                                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform">
                                        <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent text-center">3秒</div>
                                    <div class="text-gray-600 text-center">快速生成大纲</div>
                                </div>
                                
                                <div class="group bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20 animate-float" style="animation-delay: 0.4s;">
                                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform">
                                        <i data-lucide="infinity" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent text-center">无限次</div>
                                    <div class="text-gray-600 text-center">免费修改</div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile stats -->
                        <div class="lg:hidden grid grid-cols-3 gap-4 mt-8 animate-fade-in-up" style="animation-delay: 0.5s;">
                            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20">
                                <div class="text-xl font-bold text-green-600">AI智能</div>
                                <div class="text-xs text-gray-600">写作助手</div>
                            </div>
                            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20">
                                <div class="text-xl font-bold text-blue-600">3秒</div>
                                <div class="text-xs text-gray-600">生成大纲</div>
                            </div>
                            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-white/20">
                                <div class="text-xl font-bold text-purple-600">无限次</div>
                                <div class="text-xs text-gray-600">修改</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Main Content Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <!-- Left Column - Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Essay Type Selection -->
                        <section id="essay-types-section" class="mb-8">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="layers" class="w-5 h-5 text-white"></i>
                                    </div>
                                    选择论文类型
                                </h2>
                                <div class="hidden sm:flex items-center gap-2 text-sm text-gray-500">
                                    <i data-lucide="mouse-pointer-click" class="w-4 h-4"></i>
                                    点击选择
                                </div>
                            </div>

                            <!-- Essay Type Grid - Desktop -->
                            <div class="hidden lg:grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8" id="essay-types-desktop">
                                <!-- Essay types will be populated by JavaScript -->
                            </div>

                            <!-- Essay Type Grid - Mobile -->
                            <div class="lg:hidden grid grid-cols-2 gap-4 mb-8" id="essay-types-mobile">
                                <!-- Essay types will be populated by JavaScript -->
                            </div>
                        </section>

                        <!-- Dynamic Form Container -->
                        <section id="form-section" class="hidden">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 overflow-hidden animate-scale-in">
                                <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 text-white p-6 lg:p-8">
                                    <div class="flex items-center gap-4">
                                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <i data-lucide="edit-3" class="w-6 h-6"></i>
                                        </div>
                                        <div>
                                            <h2 class="text-xl lg:text-2xl font-bold" id="form-title">AI论文写作</h2>
                                            <p class="text-blue-100 mt-1 text-sm lg:text-base" id="form-description">填写论文要求，AI将为您生成专业的学术内容</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-6 lg:p-8" id="form-container">
                                    <!-- Form content will be populated by JavaScript -->
                                </div>
                            </div>
                        </section>

                        <!-- Tips for mobile -->
                        <div class="lg:hidden bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 text-white p-6 rounded-2xl text-center mb-8 shadow-xl animate-fade-in" id="mobile-tip">
                            <h3 class="font-bold mb-3 flex items-center justify-center gap-2 text-lg">
                                <i data-lucide="lightbulb" class="w-5 h-5"></i>
                                智能提示
                            </h3>
                            <p class="text-blue-100">选择论文类型后，填写详细信息即可快速生成专业大纲和内容</p>
                        </div>
                    </div>

                    <!-- Right Column - Enhanced Sidebar -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Real-time Stats -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.1s;">
                            <div class="bg-gradient-to-r from-blue-500 to-cyan-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                    </div>
                                    今日统计
                                </h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="flex justify-between items-center group hover:bg-blue-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="file-text" class="w-4 h-4"></i>
                                        生成论文
                                    </span>
                                    <span class="font-bold text-blue-600 text-lg group-hover:scale-110 transition-transform">3篇</span>
                                </div>
                                <div class="flex justify-between items-center group hover:bg-green-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="type" class="w-4 h-4"></i>
                                        总字数
                                    </span>
                                    <span class="font-bold text-green-600 text-lg group-hover:scale-110 transition-transform">15,000字</span>
                                </div>
                                <div class="flex justify-between items-center group hover:bg-purple-50 p-3 rounded-lg transition-colors">
                                    <span class="text-gray-600 flex items-center gap-2">
                                        <i data-lucide="clock" class="w-4 h-4"></i>
                                        剩余次数
                                    </span>
                                    <span class="font-bold text-purple-600 text-lg group-hover:scale-110 transition-transform">47次</span>
                                </div>
                                <div class="pt-4 border-t border-gray-200">
                                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                                        <span>本月使用进度</span>
                                        <span>53/100</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-1000" style="width: 53%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Records -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.2s;">
                            <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="history" class="w-4 h-4"></i>
                                    </div>
                                    最近记录
                                </h3>
                            </div>
                            <div class="p-6 space-y-3" id="recent-records">
                                <!-- Recent records will be populated by JavaScript -->
                            </div>
                            <div class="p-6 border-t border-gray-200">
                                <button class="w-full text-center text-blue-600 hover:text-blue-700 transition-colors font-medium flex items-center justify-center gap-2 group" onclick="window.location.href='history.html'">
                                    查看全部记录
                                    <i data-lucide="arrow-right" class="w-4 h-4 group-hover:translate-x-1 transition-transform"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden animate-fade-in-up" style="animation-delay: 0.3s;">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6">
                                <h3 class="font-bold flex items-center gap-2 text-lg">
                                    <div class="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="zap" class="w-4 h-4"></i>
                                    </div>
                                    快捷功能
                                </h3>
                            </div>
                            <div class="p-6 space-y-3" id="quick-actions">
                                <!-- Quick actions will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Enhanced floating help button -->
    <button class="fixed bottom-20 right-4 lg:bottom-6 lg:right-6 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 z-50 group hover:scale-110 animate-bounce-gentle" id="fab-help" title="获取帮助">
        <i data-lucide="help-circle" class="w-6 h-6 mx-auto group-hover:scale-110 transition-transform"></i>
    </button>

    <!-- Enhanced help modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 hidden animate-fade-in">
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl max-w-lg w-full max-h-[80vh] overflow-y-auto shadow-2xl border border-white/20 animate-scale-in">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold flex items-center gap-3">
                        <i data-lucide="help-circle" class="w-6 h-6"></i>
                        使用帮助
                    </h3>
                    <button onclick="closeHelpModal()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 space-y-6">
                <div class="space-y-4">
                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">1</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">选择论文类型</h4>
                            <p class="text-gray-600 text-sm">从12种专业论文类型中选择最适合您需求的类型，每种类型都有专门优化的AI模板</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">2</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">填写详细信息</h4>
                            <p class="text-gray-600 text-sm">填写论文标题、字数要求、学科领域、参考文献等基本信息，信息越详细，生成效果越好</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">3</div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">AI智能生成</h4>
                            <p class="text-gray-600 text-sm">点击生成按钮，AI将分析您的需求并创建专业的学术内容，通常在30秒内完成</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                    <h5 class="font-medium text-blue-900 mb-3 flex items-center gap-2">
                        <i data-lucide="lightbulb" class="w-5 h-5"></i>
                        智能提示
                    </h5>
                    <ul class="text-sm text-blue-800 space-y-2">
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            详细的内容描述有助于生成更精准的论文
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            建议先生成大纲预览整体结构
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            生成后可以无限次修改和优化
                        </li>
                        <li class="flex items-start gap-2">
                            <i data-lucide="check" class="w-4 h-4 mt-0.5 text-green-600"></i>
                            支持多种格式导出和下载
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Modal -->
    <div id="demo-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 hidden animate-fade-in">
        <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden shadow-2xl">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold flex items-center gap-3">
                        <i data-lucide="play-circle" class="w-6 h-6"></i>
                        产品演示
                    </h3>
                    <button onclick="closeDemoModal()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="aspect-video bg-gray-100 rounded-xl flex items-center justify-center">
                    <div class="text-center">
                        <i data-lucide="play-circle" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <p class="text-gray-600">演示视频即将上线...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced back to top button -->
    <button id="back-to-top" class="fixed bottom-32 right-4 lg:bottom-24 lg:right-6 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 opacity-0 pointer-events-none z-40 hover:scale-110">
        <i data-lucide="arrow-up" class="w-5 h-5 mx-auto"></i>
    </button>

    <!-- Enhanced loading overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="text-center">
            <div class="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-6"></div>
            <div class="space-y-2">
                <p class="text-gray-800 font-semibold text-lg">AI正在为您生成内容...</p>
                <p class="text-gray-600">请稍候，这通常需要几秒钟</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/essay-forms.js"></script>

    <!-- Enhanced initialization script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize page components
            UIComponents.initializePage('index');
            
            // Initialize enhanced features
            initializeEnhancedFeatures();
        });

        // Enhanced features initialization
        function initializeEnhancedFeatures() {
            // Smooth scroll for CTA button
            window.scrollToEssayTypes = function() {
                document.getElementById('essay-types-section').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            };

            // Demo modal functions
            window.showDemoModal = function() {
                document.getElementById('demo-modal').classList.remove('hidden');
            };

            window.closeDemoModal = function() {
                document.getElementById('demo-modal').classList.add('hidden');
            };

            // Enhanced scroll animations
            setupScrollAnimations();
            
            // Performance monitoring
            setupPerformanceMonitoring();
        }

        // Enhanced scroll animations
        function setupScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.lg\\:col-span-1 > div').forEach(el => {
                observer.observe(el);
            });
        }

        // Performance monitoring
        function setupPerformanceMonitoring() {
            // Monitor Core Web Vitals
            if ('web-vitals' in window) {
                // This would be implemented with a proper web-vitals library
                console.log('Performance monitoring enabled');
            }

            // Monitor page load performance
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
            });
        }

        // Enhanced help modal
        document.getElementById('fab-help').addEventListener('click', function() {
            document.getElementById('help-modal').classList.remove('hidden');
        });

        function closeHelpModal() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // Enhanced back to top functionality
        const backToTopBtn = document.getElementById('back-to-top');
        let ticking = false;

        function updateBackToTop() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.add('opacity-100');
            } else {
                backToTopBtn.classList.add('opacity-0', 'pointer-events-none');
                backToTopBtn.classList.remove('opacity-100');
            }
            ticking = false;
        }

        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateBackToTop);
                ticking = true;
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Enhanced keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Esc to close modals
            if (e.key === 'Escape') {
                closeHelpModal();
                closeDemoModal();
            }

            // Ctrl/Cmd + Enter for quick generation
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const generateBtn = document.getElementById('generate-btn');
                if (generateBtn && !generateBtn.disabled) {
                    generateBtn.click();
                }
            }

            // Alt + H for help
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                document.getElementById('fab-help').click();
            }
        });

        // Enhanced page visibility handling
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Pause animations when page is hidden
                document.body.style.setProperty('--animation-play-state', 'paused');
            } else {
                // Resume animations when page is visible
                document.body.style.setProperty('--animation-play-state', 'running');
            }
        });

        // Preload critical resources
        function preloadCriticalResources() {
            const criticalPaths = [
                'js/essay-forms.js',
                'styles/components.css'
            ];

            criticalPaths.forEach(path => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = path;
                link.as = path.endsWith('.js') ? 'script' : 'style';
                document.head.appendChild(link);
            });
        }

        // Initialize preloading
        preloadCriticalResources();
    </script>

    <!-- Analytics and tracking (placeholder) -->
    <script>
        // Google Analytics or other tracking code would go here
        console.log('Analytics tracking initialized');
    </script>
</body>
</html>