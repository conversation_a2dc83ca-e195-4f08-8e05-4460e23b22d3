/**
 * 媒体资源优化器
 * 实现图片懒加载、WebP支持、响应式图片等优化
 */

class MediaOptimizer {
    constructor() {
        this.lazyImages = new Set();
        this.observer = null;
        this.webpSupported = null;
        this.avifSupported = null;
        this.loadedImages = new Map();
        this.imageCache = new Map();
        
        this.init();
    }

    init() {
        this.detectFormatSupport();
        this.setupLazyLoading();
        this.setupResponsiveImages();
        this.setupImageOptimization();
        this.setupPreloading();
    }

    // 检测图片格式支持
    async detectFormatSupport() {
        // 检测WebP支持
        this.webpSupported = await this.checkWebPSupport();
        
        // 检测AVIF支持
        this.avifSupported = await this.checkAVIFSupport();
        
        console.log('📷 Image format support:', {
            webp: this.webpSupported,
            avif: this.avifSupported
        });
    }

    // 检测WebP支持
    checkWebPSupport() {
        return new Promise(resolve => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // 检测AVIF支持
    checkAVIFSupport() {
        return new Promise(resolve => {
            const avif = new Image();
            avif.onload = avif.onerror = () => {
                resolve(avif.height === 2);
            };
            avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
        });
    }

    // 设置懒加载
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: 0.1
            });

            this.observeImages();
        } else {
            // 降级处理：直接加载所有图片
            this.loadAllImages();
        }
    }

    // 观察图片
    observeImages() {
        const images = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        
        images.forEach(img => {
            this.lazyImages.add(img);
            if (this.observer) {
                this.observer.observe(img);
            }
        });
    }

    // 加载图片
    async loadImage(img) {
        try {
            // 显示加载占位符
            this.showLoadingPlaceholder(img);
            
            // 获取优化后的图片URL
            const optimizedSrc = await this.getOptimizedImageSrc(img);
            
            // 预加载图片
            await this.preloadImage(optimizedSrc);
            
            // 设置图片源
            img.src = optimizedSrc;
            img.removeAttribute('data-src');
            
            // 添加淡入效果
            this.addFadeInEffect(img);
            
            // 记录加载完成
            this.loadedImages.set(img, {
                src: optimizedSrc,
                loadTime: performance.now()
            });
            
            console.log('📷 Image loaded:', optimizedSrc);
            
        } catch (error) {
            console.error('❌ Failed to load image:', error);
            this.handleImageError(img);
        }
    }

    // 获取优化后的图片源
    async getOptimizedImageSrc(img) {
        let src = img.dataset.src || img.src;
        
        // 如果支持现代格式，尝试替换
        if (this.avifSupported && !src.includes('.avif')) {
            const avifSrc = this.convertToFormat(src, 'avif');
            if (await this.imageExists(avifSrc)) {
                return avifSrc;
            }
        }
        
        if (this.webpSupported && !src.includes('.webp')) {
            const webpSrc = this.convertToFormat(src, 'webp');
            if (await this.imageExists(webpSrc)) {
                return webpSrc;
            }
        }
        
        // 添加响应式参数
        return this.addResponsiveParams(src, img);
    }

    // 转换图片格式
    convertToFormat(src, format) {
        const lastDotIndex = src.lastIndexOf('.');
        if (lastDotIndex === -1) return src;
        
        return src.substring(0, lastDotIndex) + '.' + format;
    }

    // 检查图片是否存在
    imageExists(src) {
        return new Promise(resolve => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = src;
        });
    }

    // 添加响应式参数
    addResponsiveParams(src, img) {
        const width = img.getAttribute('width') || img.clientWidth;
        const height = img.getAttribute('height') || img.clientHeight;
        const dpr = window.devicePixelRatio || 1;
        
        // 计算实际需要的尺寸
        const actualWidth = Math.ceil(width * dpr);
        const actualHeight = Math.ceil(height * dpr);
        
        // 如果是外部CDN，添加尺寸参数
        if (src.includes('cdn.') || src.includes('cloudinary.com')) {
            const separator = src.includes('?') ? '&' : '?';
            return `${src}${separator}w=${actualWidth}&h=${actualHeight}&q=auto&f=auto`;
        }
        
        return src;
    }

    // 预加载图片
    preloadImage(src) {
        // 检查缓存
        if (this.imageCache.has(src)) {
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageCache.set(src, img);
                resolve();
            };
            img.onerror = reject;
            img.src = src;
        });
    }

    // 显示加载占位符
    showLoadingPlaceholder(img) {
        // 如果没有占位符，创建一个
        if (!img.dataset.placeholder) {
            const placeholder = this.generatePlaceholder(img);
            img.src = placeholder;
        }
        
        // 添加加载类
        img.classList.add('loading');
    }

    // 生成占位符
    generatePlaceholder(img) {
        const width = img.getAttribute('width') || 300;
        const height = img.getAttribute('height') || 200;
        
        // 生成SVG占位符
        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f3f4f6"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="Arial, sans-serif" font-size="14">
                    Loading...
                </text>
            </svg>
        `;
        
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    // 添加淡入效果
    addFadeInEffect(img) {
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease-in-out';
        
        img.onload = () => {
            img.classList.remove('loading');
            img.style.opacity = '1';
            
            // 清理事件监听器
            img.onload = null;
        };
    }

    // 处理图片错误
    handleImageError(img) {
        img.classList.add('error');
        img.src = this.generateErrorPlaceholder(img);
    }

    // 生成错误占位符
    generateErrorPlaceholder(img) {
        const width = img.getAttribute('width') || 300;
        const height = img.getAttribute('height') || 200;
        
        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#fee2e2"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#dc2626" font-family="Arial, sans-serif" font-size="14">
                    Failed to load
                </text>
            </svg>
        `;
        
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    // 设置响应式图片
    setupResponsiveImages() {
        // 处理picture元素
        this.setupPictureElements();
        
        // 处理srcset属性
        this.setupSrcsetImages();
        
        // 监听窗口大小变化
        this.setupResizeHandler();
    }

    // 设置picture元素
    setupPictureElements() {
        const pictures = document.querySelectorAll('picture');
        
        pictures.forEach(picture => {
            const sources = picture.querySelectorAll('source');
            
            sources.forEach(source => {
                // 为每个source添加现代格式支持
                this.optimizeSource(source);
            });
        });
    }

    // 优化source元素
    optimizeSource(source) {
        const srcset = source.getAttribute('srcset');
        if (!srcset) return;
        
        // 添加WebP格式
        if (this.webpSupported) {
            const webpSource = source.cloneNode();
            webpSource.setAttribute('type', 'image/webp');
            webpSource.setAttribute('srcset', this.convertSrcsetToFormat(srcset, 'webp'));
            source.parentNode.insertBefore(webpSource, source);
        }
        
        // 添加AVIF格式
        if (this.avifSupported) {
            const avifSource = source.cloneNode();
            avifSource.setAttribute('type', 'image/avif');
            avifSource.setAttribute('srcset', this.convertSrcsetToFormat(srcset, 'avif'));
            source.parentNode.insertBefore(avifSource, source);
        }
    }

    // 转换srcset格式
    convertSrcsetToFormat(srcset, format) {
        return srcset.split(',').map(src => {
            const [url, descriptor] = src.trim().split(' ');
            const convertedUrl = this.convertToFormat(url, format);
            return descriptor ? `${convertedUrl} ${descriptor}` : convertedUrl;
        }).join(', ');
    }

    // 设置图片优化
    setupImageOptimization() {
        // 压缩图片质量
        this.setupImageCompression();
        
        // 设置图片缓存
        this.setupImageCaching();
        
        // 设置图片预加载
        this.setupImagePreloading();
    }

    // 设置图片压缩
    setupImageCompression() {
        // 根据网络状况调整图片质量
        if ('connection' in navigator) {
            const connection = navigator.connection;
            this.imageQuality = this.getImageQuality(connection.effectiveType);
        } else {
            this.imageQuality = 80; // 默认质量
        }
    }

    // 获取图片质量
    getImageQuality(effectiveType) {
        const qualityMap = {
            'slow-2g': 40,
            '2g': 50,
            '3g': 70,
            '4g': 80
        };
        
        return qualityMap[effectiveType] || 80;
    }

    // 设置预加载
    setupPreloading() {
        // 预加载关键图片
        this.preloadCriticalImages();
        
        // 预加载下一页图片
        this.preloadNextPageImages();
    }

    // 预加载关键图片
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('img[data-critical]');
        
        criticalImages.forEach(img => {
            const src = img.dataset.src || img.src;
            this.preloadImage(src);
        });
    }

    // 预加载下一页图片
    preloadNextPageImages() {
        // 在空闲时预加载
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.preloadImagesForNextPage();
            });
        }
    }

    // 加载所有图片（降级处理）
    loadAllImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.loadImage(img));
    }

    // 设置窗口大小变化处理
    setupResizeHandler() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    // 处理窗口大小变化
    handleResize() {
        // 重新计算响应式图片
        const responsiveImages = document.querySelectorAll('img[data-responsive]');
        
        responsiveImages.forEach(img => {
            const newSrc = this.addResponsiveParams(img.src, img);
            if (newSrc !== img.src) {
                img.src = newSrc;
            }
        });
    }

    // 获取性能报告
    getPerformanceReport() {
        const totalImages = this.lazyImages.size;
        const loadedImages = this.loadedImages.size;
        const loadTimes = Array.from(this.loadedImages.values()).map(data => data.loadTime);
        const avgLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
        
        return {
            totalImages,
            loadedImages,
            loadingProgress: (loadedImages / totalImages * 100).toFixed(2) + '%',
            averageLoadTime: avgLoadTime.toFixed(2) + 'ms',
            formatSupport: {
                webp: this.webpSupported,
                avif: this.avifSupported
            },
            cacheSize: this.imageCache.size
        };
    }

    // 清理资源
    cleanup() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        this.lazyImages.clear();
        this.loadedImages.clear();
        this.imageCache.clear();
    }
}

// 导出单例
export const mediaOptimizer = new MediaOptimizer();
export default MediaOptimizer;
