// Toast 通知系统
class ToastSystem {
    constructor() {
        this.toasts = [];
        this.container = null;
        this.init();
    }

    // 初始化Toast容器
    init() {
        // 创建Toast容器
        this.container = document.createElement('div');
        this.container.id = 'toast-container';
        this.container.className = 'fixed top-4 right-4 z-[9999] space-y-2';
        document.body.appendChild(this.container);
    }

    // 显示Toast
    show(message, type = 'info', duration = 4000) {
        const toast = this.createToast(message, type, duration);
        this.toasts.push(toast);
        this.container.appendChild(toast.element);

        // 触发进入动画
        setTimeout(() => {
            toast.element.classList.remove('translate-x-full', 'opacity-0');
            toast.element.classList.add('translate-x-0', 'opacity-100');
        }, 10);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.remove(toast.id);
            }, duration);
        }

        return toast.id;
    }

    // 创建Toast元素
    createToast(message, type, duration) {
        const id = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        // 获取类型配置
        const config = this.getTypeConfig(type);
        
        // 创建Toast元素
        const element = document.createElement('div');
        element.id = id;
        element.className = `
            flex items-center p-4 mb-4 text-sm rounded-lg shadow-lg backdrop-blur-sm
            transform translate-x-full opacity-0 transition-all duration-300 ease-out
            max-w-xs w-full
            ${config.bgClass} ${config.textClass} ${config.borderClass}
        `.trim().replace(/\s+/g, ' ');

        element.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i data-lucide="${config.icon}" class="w-5 h-5 ${config.iconClass}"></i>
                </div>
                <div class="ml-3 text-sm font-medium flex-1">
                    ${message}
                </div>
                <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 hover:bg-black/10 transition-colors" onclick="toastSystem.remove('${id}')">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
            ${duration > 0 ? `
                <div class="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg animate-toast-progress" style="animation-duration: ${duration}ms;"></div>
            ` : ''}
        `;

        // 重新初始化图标
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 10);

        return { id, element, type, duration };
    }

    // 获取类型配置
    getTypeConfig(type) {
        const configs = {
            success: {
                icon: 'check-circle',
                bgClass: 'bg-green-50 border border-green-200',
                textClass: 'text-green-800',
                borderClass: 'border-green-200',
                iconClass: 'text-green-600'
            },
            error: {
                icon: 'x-circle',
                bgClass: 'bg-red-50 border border-red-200',
                textClass: 'text-red-800',
                borderClass: 'border-red-200',
                iconClass: 'text-red-600'
            },
            warning: {
                icon: 'alert-triangle',
                bgClass: 'bg-yellow-50 border border-yellow-200',
                textClass: 'text-yellow-800',
                borderClass: 'border-yellow-200',
                iconClass: 'text-yellow-600'
            },
            info: {
                icon: 'info',
                bgClass: 'bg-blue-50 border border-blue-200',
                textClass: 'text-blue-800',
                borderClass: 'border-blue-200',
                iconClass: 'text-blue-600'
            }
        };

        return configs[type] || configs.info;
    }

    // 移除Toast
    remove(id) {
        const toastIndex = this.toasts.findIndex(toast => toast.id === id);
        if (toastIndex === -1) return;

        const toast = this.toasts[toastIndex];
        
        // 触发退出动画
        toast.element.classList.remove('translate-x-0', 'opacity-100');
        toast.element.classList.add('translate-x-full', 'opacity-0');

        // 移除元素
        setTimeout(() => {
            if (toast.element.parentNode) {
                toast.element.parentNode.removeChild(toast.element);
            }
            this.toasts.splice(toastIndex, 1);
        }, 300);
    }

    // 清除所有Toast
    clear() {
        this.toasts.forEach(toast => {
            this.remove(toast.id);
        });
    }

    // 显示成功消息
    success(message, duration = 4000) {
        return this.show(message, 'success', duration);
    }

    // 显示错误消息
    error(message, duration = 6000) {
        return this.show(message, 'error', duration);
    }

    // 显示警告消息
    warning(message, duration = 5000) {
        return this.show(message, 'warning', duration);
    }

    // 显示信息消息
    info(message, duration = 4000) {
        return this.show(message, 'info', duration);
    }

    // 显示加载消息
    loading(message = '加载中...') {
        const id = 'toast-' + Date.now() + '-loading';
        
        const element = document.createElement('div');
        element.id = id;
        element.className = `
            flex items-center p-4 mb-4 text-sm rounded-lg shadow-lg backdrop-blur-sm
            transform translate-x-full opacity-0 transition-all duration-300 ease-out
            max-w-xs w-full bg-white border border-gray-200 text-gray-800
        `.trim().replace(/\s+/g, ' ');

        element.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i data-lucide="loader-2" class="w-5 h-5 text-blue-600 animate-spin"></i>
                </div>
                <div class="ml-3 text-sm font-medium flex-1">
                    ${message}
                </div>
            </div>
        `;

        const toast = { id, element, type: 'loading', duration: 0 };
        this.toasts.push(toast);
        this.container.appendChild(element);

        // 触发进入动画
        setTimeout(() => {
            element.classList.remove('translate-x-full', 'opacity-0');
            element.classList.add('translate-x-0', 'opacity-100');
        }, 10);

        // 重新初始化图标
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 10);

        return id;
    }
}

// 创建全局实例
const toastSystem = new ToastSystem();

// 全局函数
function showToast(message, type = 'info', duration = 4000) {
    return toastSystem.show(message, type, duration);
}

// 导出到全局
window.toastSystem = toastSystem;
window.showToast = showToast;

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes toast-progress {
        from {
            width: 100%;
        }
        to {
            width: 0%;
        }
    }
    
    .animate-toast-progress {
        animation: toast-progress linear;
    }
    
    /* 响应式调整 */
    @media (max-width: 640px) {
        #toast-container {
            top: 1rem;
            right: 1rem;
            left: 1rem;
        }
        
        #toast-container > div {
            max-width: none;
        }
    }
`;
document.head.appendChild(style);
