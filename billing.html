<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐管理 - 博晓文</title>
    <meta name="description" content="管理您的订阅和账单信息，选择最适合的服务套餐">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-6xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-pink-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4 leading-tight">
                                套餐管理
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                管理您的订阅和账单信息，选择最适合的服务套餐
                                <span class="inline-block ml-2 text-pink-600">💎</span>
                            </p>
                        </div>
                        
                        <!-- 统计卡片 -->
                        <div class="hidden lg:flex space-x-6 xl:space-x-8">
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-blue-600">专业版</div>
                                <div class="text-sm text-gray-500">当前套餐</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-green-600">15/50</div>
                                <div class="text-sm text-gray-500">本月使用</div>
                            </div>
                            <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="calendar" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-purple-600">15天</div>
                                <div class="text-sm text-gray-500">剩余时间</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能特色 -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">AI智能</h3>
                        <p class="text-xs text-gray-600">高级AI模型</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">安全支付</h3>
                        <p class="text-xs text-gray-600">银行级加密</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">灵活计费</h3>
                        <p class="text-xs text-gray-600">按需付费</p>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.4s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="headphones" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-1 text-sm">专属客服</h3>
                        <p class="text-xs text-gray-600">24/7支持</p>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="bg-white rounded-xl shadow-lg mb-8">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" id="tabs">
                            <button class="py-4 px-1 border-b-2 border-pink-500 text-pink-600 font-medium text-sm tab-button active" data-tab="plans">
                                套餐选择
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="usage">
                                使用情况
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="billing">
                                账单历史
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Tab Contents -->
                <div id="tab-contents">
                    <!-- 套餐选择 -->
                    <div id="plans-tab" class="tab-content">
                        <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 mb-8 fade-in-up">
                            <div class="bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-t-xl p-6">
                                <h2 class="text-2xl font-bold flex items-center gap-3">
                                    <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="crown" class="w-6 h-6"></i>
                                    </div>
                                    选择适合您的套餐
                                </h2>
                                <p class="text-pink-100 mt-2 text-lg">
                                    根据您的需求选择最合适的服务套餐，随时可以升级或降级
                                </p>
                            </div>
                            <div class="p-8">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="plans-container">
                                    <!-- 套餐卡片将由JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用情况 -->
                    <div id="usage-tab" class="tab-content hidden">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 本月使用情况 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="zap" class="w-5 h-5"></i>
                                        </div>
                                        本月使用情况
                                    </h3>
                                    <p class="text-blue-100 mt-2">查看您本月的服务使用统计</p>
                                </div>
                                <div class="p-6 space-y-6" id="usage-stats">
                                    <!-- 使用统计将由JavaScript生成 -->
                                </div>
                            </div>

                            <!-- 订阅信息 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="calendar" class="w-5 h-5"></i>
                                        </div>
                                        订阅信息
                                    </h3>
                                    <p class="text-green-100 mt-2">管理您的订阅状态和续费设置</p>
                                </div>
                                <div class="p-6 space-y-4" id="subscription-info">
                                    <!-- 订阅信息将由JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账单历史 -->
                    <div id="billing-tab" class="tab-content hidden">
                        <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-t-xl p-6">
                                <h3 class="text-xl font-bold flex items-center gap-3">
                                    <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="receipt" class="w-5 h-5"></i>
                                    </div>
                                    账单历史
                                </h3>
                                <p class="text-purple-100 mt-2">查看您的所有账单记录和支付历史</p>
                            </div>
                            <div class="p-6">
                                <div class="overflow-x-auto">
                                    <table class="w-full">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">账单日期</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">套餐</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200" id="billing-table-body">
                                            <!-- 账单记录将由JavaScript生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/global-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/billing.js"></script>

    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('billing');
        });
    </script>
</body>
</html>
