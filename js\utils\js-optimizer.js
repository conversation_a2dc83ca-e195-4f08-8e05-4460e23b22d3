/**
 * JavaScript性能优化器
 * 实现代码分割、懒加载、内存管理等优化策略
 */

class JSOptimizer {
    constructor() {
        this.loadedModules = new Map();
        this.moduleCache = new Map();
        this.observers = new Map();
        this.memoryUsage = new Map();
        this.performanceMetrics = new Map();
        
        this.init();
    }

    init() {
        this.setupModuleLazyLoading();
        this.setupMemoryManagement();
        this.setupPerformanceMonitoring();
        this.optimizeEventListeners();
        this.setupCodeSplitting();
    }

    // 设置模块懒加载
    setupModuleLazyLoading() {
        // 创建模块加载器
        this.moduleLoader = new ModuleLoader();
        
        // 设置Intersection Observer用于懒加载
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadModuleForElement(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '100px',
                threshold: 0.1
            });

            this.observers.set('lazy-modules', observer);
            this.observeLazyModules();
        }
    }

    // 观察需要懒加载的模块
    observeLazyModules() {
        const elements = document.querySelectorAll('[data-lazy-module]');
        const observer = this.observers.get('lazy-modules');
        
        elements.forEach(element => {
            if (observer) {
                observer.observe(element);
            }
        });
    }

    // 为元素加载模块
    async loadModuleForElement(element) {
        const moduleName = element.dataset.lazyModule;
        if (!moduleName) return;

        try {
            await this.loadModule(moduleName);
            element.classList.add('module-loaded');
            
            // 触发模块加载完成事件
            element.dispatchEvent(new CustomEvent('moduleLoaded', {
                detail: { module: moduleName }
            }));
        } catch (error) {
            console.error(`Failed to load module ${moduleName}:`, error);
        }
    }

    // 加载模块
    async loadModule(moduleName) {
        // 检查缓存
        if (this.loadedModules.has(moduleName)) {
            return this.loadedModules.get(moduleName);
        }

        // 检查是否正在加载
        if (this.moduleCache.has(moduleName)) {
            return this.moduleCache.get(moduleName);
        }

        // 开始加载
        const loadPromise = this.dynamicImport(moduleName);
        this.moduleCache.set(moduleName, loadPromise);

        try {
            const module = await loadPromise;
            this.loadedModules.set(moduleName, module);
            this.moduleCache.delete(moduleName);
            
            console.log(`✅ Module loaded: ${moduleName}`);
            return module;
        } catch (error) {
            this.moduleCache.delete(moduleName);
            throw error;
        }
    }

    // 动态导入模块
    async dynamicImport(moduleName) {
        const moduleMap = {
            'essay-forms': () => import('../essay-forms.js'),
            'history': () => import('../history.js'),
            'plagiarism': () => import('../plagiarism.js'),
            'billing': () => import('../billing.js'),
            'profile': () => import('../profile.js'),
            'aigc': () => import('../aigc.js'),
            'charts': () => import('../utils/charts.js'),
            'file-upload': () => import('../utils/file-upload.js'),
            'pdf-generator': () => import('../utils/pdf-generator.js')
        };

        const importFn = moduleMap[moduleName];
        if (!importFn) {
            throw new Error(`Unknown module: ${moduleName}`);
        }

        return await importFn();
    }

    // 设置内存管理
    setupMemoryManagement() {
        // 监控内存使用
        this.monitorMemoryUsage();
        
        // 设置垃圾回收优化
        this.setupGarbageCollection();
        
        // 设置内存泄漏检测
        this.setupMemoryLeakDetection();
    }

    // 监控内存使用
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.memoryUsage.set(Date.now(), {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                });

                // 保持最近100个记录
                if (this.memoryUsage.size > 100) {
                    const oldestKey = this.memoryUsage.keys().next().value;
                    this.memoryUsage.delete(oldestKey);
                }

                // 检查内存使用率
                const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                if (usageRatio > 0.8) {
                    console.warn('⚠️ High memory usage detected:', usageRatio);
                    this.triggerGarbageCollection();
                }
            }, 5000);
        }
    }

    // 设置垃圾回收优化
    setupGarbageCollection() {
        // 页面隐藏时清理资源
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.cleanupResources();
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanupResources();
        });
    }

    // 触发垃圾回收
    triggerGarbageCollection() {
        // 清理未使用的模块
        this.cleanupUnusedModules();
        
        // 清理事件监听器
        this.cleanupEventListeners();
        
        // 清理缓存
        this.cleanupCache();
        
        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }
    }

    // 清理未使用的模块
    cleanupUnusedModules() {
        const currentPage = this.getCurrentPage();
        const pageModules = this.getPageModules(currentPage);
        
        this.loadedModules.forEach((module, name) => {
            if (!pageModules.includes(name)) {
                // 清理模块资源
                if (module.cleanup && typeof module.cleanup === 'function') {
                    module.cleanup();
                }
                this.loadedModules.delete(name);
                console.log(`🗑️ Cleaned up module: ${name}`);
            }
        });
    }

    // 设置内存泄漏检测
    setupMemoryLeakDetection() {
        // 检测DOM节点泄漏
        this.detectDOMLeaks();
        
        // 检测事件监听器泄漏
        this.detectEventListenerLeaks();
        
        // 检测定时器泄漏
        this.detectTimerLeaks();
    }

    // 检测DOM节点泄漏
    detectDOMLeaks() {
        let previousNodeCount = document.querySelectorAll('*').length;
        
        setInterval(() => {
            const currentNodeCount = document.querySelectorAll('*').length;
            const growth = currentNodeCount - previousNodeCount;
            
            if (growth > 100) {
                console.warn('⚠️ Potential DOM leak detected:', {
                    previous: previousNodeCount,
                    current: currentNodeCount,
                    growth: growth
                });
            }
            
            previousNodeCount = currentNodeCount;
        }, 10000);
    }

    // 优化事件监听器
    optimizeEventListeners() {
        // 使用事件委托
        this.setupEventDelegation();
        
        // 使用passive监听器
        this.setupPassiveListeners();
        
        // 防抖和节流
        this.setupDebounceThrottle();
    }

    // 设置事件委托
    setupEventDelegation() {
        // 为常见事件设置委托
        const delegatedEvents = ['click', 'change', 'input', 'submit'];
        
        delegatedEvents.forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                this.handleDelegatedEvent(e);
            }, { passive: eventType !== 'submit' });
        });
    }

    // 处理委托事件
    handleDelegatedEvent(event) {
        const target = event.target;
        const handler = target.dataset[`on${event.type}`];
        
        if (handler && window[handler]) {
            window[handler](event);
        }
    }

    // 设置passive监听器
    setupPassiveListeners() {
        const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove'];
        
        passiveEvents.forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                // 处理passive事件
                this.handlePassiveEvent(e);
            }, { passive: true });
        });
    }

    // 处理passive事件
    handlePassiveEvent(event) {
        // 使用requestAnimationFrame优化滚动事件
        if (event.type === 'scroll') {
            if (!this.scrollRAF) {
                this.scrollRAF = requestAnimationFrame(() => {
                    this.handleScroll(event);
                    this.scrollRAF = null;
                });
            }
        }
    }

    // 设置防抖和节流
    setupDebounceThrottle() {
        // 为输入事件设置防抖
        this.debounceInput = this.debounce((event) => {
            this.handleInput(event);
        }, 300);

        // 为滚动事件设置节流
        this.throttleScroll = this.throttle((event) => {
            this.handleScroll(event);
        }, 16); // 60fps
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 设置代码分割
    setupCodeSplitting() {
        // 按页面分割代码
        this.splitByPage();
        
        // 按功能分割代码
        this.splitByFeature();
        
        // 按优先级分割代码
        this.splitByPriority();
    }

    // 按页面分割代码
    splitByPage() {
        const currentPage = this.getCurrentPage();
        const pageModules = this.getPageModules(currentPage);
        
        // 只加载当前页面需要的模块
        pageModules.forEach(module => {
            this.loadModule(module);
        });
    }

    // 获取当前页面
    getCurrentPage() {
        const path = window.location.pathname;
        return path.split('/').pop().replace('.html', '') || 'index';
    }

    // 获取页面模块
    getPageModules(page) {
        const pageModuleMap = {
            'index': ['essay-forms'],
            'history': ['history', 'charts'],
            'plagiarism': ['plagiarism', 'file-upload'],
            'aigc': ['aigc', 'file-upload'],
            'billing': ['billing', 'charts'],
            'profile': ['profile', 'file-upload']
        };
        
        return pageModuleMap[page] || [];
    }

    // 设置性能监控
    setupPerformanceMonitoring() {
        // 监控JavaScript执行时间
        this.monitorExecutionTime();
        
        // 监控模块加载时间
        this.monitorModuleLoadTime();
        
        // 监控内存使用
        this.monitorMemoryUsage();
    }

    // 监控JavaScript执行时间
    monitorExecutionTime() {
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.setTimeout = (callback, delay, ...args) => {
            const start = performance.now();
            return originalSetTimeout(() => {
                const duration = performance.now() - start;
                if (duration > 16) { // 超过一帧的时间
                    console.warn('⚠️ Long task detected in setTimeout:', duration);
                }
                callback(...args);
            }, delay);
        };
        
        window.setInterval = (callback, delay, ...args) => {
            const start = performance.now();
            return originalSetInterval(() => {
                const duration = performance.now() - start;
                if (duration > 16) {
                    console.warn('⚠️ Long task detected in setInterval:', duration);
                }
                callback(...args);
            }, delay);
        };
    }

    // 清理资源
    cleanupResources() {
        // 清理观察器
        this.observers.forEach(observer => observer.disconnect());
        
        // 清理缓存
        this.moduleCache.clear();
        
        // 清理性能指标
        if (this.performanceMetrics.size > 50) {
            const entries = Array.from(this.performanceMetrics.entries());
            const recent = entries.slice(-25);
            this.performanceMetrics.clear();
            recent.forEach(([key, value]) => {
                this.performanceMetrics.set(key, value);
            });
        }
    }

    // 获取性能报告
    getPerformanceReport() {
        const memoryEntries = Array.from(this.memoryUsage.values());
        const avgMemory = memoryEntries.reduce((sum, entry) => sum + entry.used, 0) / memoryEntries.length;
        
        return {
            loadedModules: Array.from(this.loadedModules.keys()),
            memoryUsage: {
                average: avgMemory,
                current: memoryEntries[memoryEntries.length - 1],
                peak: Math.max(...memoryEntries.map(e => e.used))
            },
            performanceMetrics: Object.fromEntries(this.performanceMetrics)
        };
    }
}

// 模块加载器类
class ModuleLoader {
    constructor() {
        this.loadQueue = [];
        this.isLoading = false;
    }

    async load(moduleName) {
        return new Promise((resolve, reject) => {
            this.loadQueue.push({ moduleName, resolve, reject });
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isLoading || this.loadQueue.length === 0) return;
        
        this.isLoading = true;
        const { moduleName, resolve, reject } = this.loadQueue.shift();
        
        try {
            const module = await this.loadModule(moduleName);
            resolve(module);
        } catch (error) {
            reject(error);
        } finally {
            this.isLoading = false;
            this.processQueue();
        }
    }

    async loadModule(moduleName) {
        // 实际的模块加载逻辑
        const script = document.createElement('script');
        script.type = 'module';
        script.src = `js/${moduleName}.js`;
        
        return new Promise((resolve, reject) => {
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
}

// 导出单例
export const jsOptimizer = new JSOptimizer();
export default JSOptimizer;
