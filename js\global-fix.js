// 全局显示修复脚本 - 确保所有页面内容都正常显示
(function() {
    'use strict';

    // 全局显示修复器
    class GlobalDisplayFixer {
        constructor() {
            this.isFixed = false;
            this.retryCount = 0;
            this.maxRetries = 3;
        }

        // 强制显示所有内容
        forceShowAllContent() {
            console.log('🔧 全局显示修复器启动...');

            // 1. 确保主要容器可见
            const mainContainers = [
                'main', 'header', 'nav', '.container', '.sidebar',
                '#header-container', '#sidebar-container', '#mobile-nav-container'
            ];

            mainContainers.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.opacity = '1';
                    el.style.visibility = 'visible';
                    if (el.style.display === 'none' && !el.classList.contains('hidden')) {
                        el.style.display = '';
                    }
                });
            });

            // 2. 确保所有文字内容可见
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, button, .btn, label');
            textElements.forEach(el => {
                if (el.style.opacity === '0' && !el.classList.contains('opacity-0')) {
                    el.style.opacity = '1';
                }
                if (el.style.visibility === 'hidden' && !el.classList.contains('invisible')) {
                    el.style.visibility = 'visible';
                }
                if (el.style.display === 'none' && !el.classList.contains('hidden')) {
                    el.style.display = '';
                }
            });

            // 3. 特别处理卡片标题
            document.querySelectorAll('.essay-type-card h3').forEach(h3 => {
                h3.style.cssText = `
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: block !important;
                    color: #111827 !important;
                    font-size: 0.875rem !important;
                    line-height: 1.25rem !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    margin-bottom: 0.25rem !important;
                `;
            });

            // 4. 确保表单元素可见
            document.querySelectorAll('input, textarea, select, .form-input, .form-textarea').forEach(el => {
                if (el.style.opacity === '0') el.style.opacity = '1';
                if (el.style.visibility === 'hidden') el.style.visibility = 'visible';
            });

            console.log('✅ 全局显示修复完成');
            this.isFixed = true;
        }

        // 检查并修复
        checkAndFix() {
            // 检查是否有隐藏的重要元素
            const hiddenElements = Array.from(document.querySelectorAll('main, header, nav, h1, h2, h3')).filter(el => {
                const computed = window.getComputedStyle(el);
                return computed.opacity === '0' || computed.visibility === 'hidden' || computed.display === 'none';
            });

            if (hiddenElements.length > 0 && this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`🔄 发现 ${hiddenElements.length} 个隐藏元素，第 ${this.retryCount} 次修复...`);
                this.forceShowAllContent();
                
                // 递归检查
                setTimeout(() => this.checkAndFix(), 1000);
            } else if (hiddenElements.length === 0) {
                console.log('🎉 所有内容都正常显示');
            }
        }

        // 初始化
        init() {
            // 立即执行一次
            this.forceShowAllContent();
            
            // 延迟检查
            setTimeout(() => this.checkAndFix(), 500);
            setTimeout(() => this.checkAndFix(), 1500);
        }
    }

    // 创建全局实例
    const globalFixer = new GlobalDisplayFixer();

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            globalFixer.init();
        });
    } else {
        globalFixer.init();
    }

    // 页面完全加载后再次检查
    window.addEventListener('load', () => {
        setTimeout(() => globalFixer.forceShowAllContent(), 1000);
    });

    // 页面可见性变化时修复
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            setTimeout(() => globalFixer.forceShowAllContent(), 100);
        }
    });

    // 最终保险 - 3秒后强制显示
    setTimeout(() => {
        console.log('🛡️ 执行最终显示保险...');
        globalFixer.forceShowAllContent();
    }, 3000);

    // 导出到全局
    window.GlobalDisplayFixer = globalFixer;

    console.log('🌟 全局显示修复器已加载');
})();
