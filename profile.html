<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 博晓文</title>
    <meta name="description" content="管理您的个人信息、账户设置和偏好配置">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-6xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8 particles-bg">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-6 lg:mb-0 fade-in-up">
                            <h1 class="text-3xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-gray-600 via-gray-800 to-black bg-clip-text text-transparent mb-4 leading-tight">
                                个人中心
                            </h1>
                            <p class="text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
                                管理您的个人信息、账户设置和偏好配置
                                <span class="inline-block ml-2 text-gray-600">👤</span>
                            </p>
                        </div>
                        
                        <!-- 用户头像和基本信息 -->
                        <div class="hidden lg:flex items-center space-x-6">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                                    <i data-lucide="user" class="w-10 h-10 text-white"></i>
                                </div>
                                <h3 class="font-bold text-gray-900">张三</h3>
                                <p class="text-sm text-gray-500">专业版用户</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速统计 -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.1s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-blue-600">24</div>
                        <div class="text-sm text-gray-500">总论文数</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.2s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-green-600">18</div>
                        <div class="text-sm text-gray-500">已完成</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.3s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-purple-600">156</div>
                        <div class="text-sm text-gray-500">使用天数</div>
                    </div>
                    <div class="text-center p-4 bg-white rounded-xl shadow-lg hover-lift fade-in-up" style="animation-delay: 0.4s;">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                            <i data-lucide="star" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-orange-600">4.9</div>
                        <div class="text-sm text-gray-500">满意度</div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="bg-white rounded-xl shadow-lg mb-8">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" id="tabs">
                            <button class="py-4 px-1 border-b-2 border-gray-500 text-gray-600 font-medium text-sm tab-button active" data-tab="profile">
                                个人信息
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="settings">
                                账户设置
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="preferences">
                                偏好设置
                            </button>
                            <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm tab-button" data-tab="security">
                                安全设置
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Tab Contents -->
                <div id="tab-contents">
                    <!-- 个人信息 -->
                    <div id="profile-tab" class="tab-content">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- 头像和基本信息 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="user" class="w-5 h-5"></i>
                                        </div>
                                        用户头像
                                    </h3>
                                    <p class="text-blue-100 mt-2">上传和管理您的头像</p>
                                </div>
                                <div class="p-6 text-center">
                                    <div class="w-32 h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                                        <i data-lucide="user" class="w-16 h-16 text-white"></i>
                                    </div>
                                    <h4 class="text-xl font-bold text-gray-900 mb-2">张三</h4>
                                    <p class="text-gray-600 mb-4"><EMAIL></p>
                                    <button class="btn btn-primary w-full mb-3">
                                        <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                                        上传头像
                                    </button>
                                    <button class="btn btn-secondary w-full">
                                        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                        删除头像
                                    </button>
                                </div>
                            </div>

                            <!-- 个人信息表单 -->
                            <div class="lg:col-span-2 bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="edit" class="w-5 h-5"></i>
                                        </div>
                                        个人信息
                                    </h3>
                                    <p class="text-green-100 mt-2">编辑和更新您的个人资料</p>
                                </div>
                                <div class="p-6">
                                    <form id="profile-form" class="space-y-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="form-group">
                                                <label class="form-label">姓名 *</label>
                                                <input type="text" name="name" value="张三" class="form-input" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">邮箱 *</label>
                                                <input type="email" name="email" value="<EMAIL>" class="form-input" required>
                                            </div>
                                        </div>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="form-group">
                                                <label class="form-label">手机号码</label>
                                                <input type="tel" name="phone" value="138****8888" class="form-input">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">学校/机构</label>
                                                <input type="text" name="organization" value="清华大学" class="form-input">
                                            </div>
                                        </div>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="form-group">
                                                <label class="form-label">专业领域</label>
                                                <select name="field" class="form-input form-select">
                                                    <option value="computer">计算机科学</option>
                                                    <option value="engineering">工程技术</option>
                                                    <option value="business">商业管理</option>
                                                    <option value="other">其他</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">学历</label>
                                                <select name="education" class="form-input form-select">
                                                    <option value="undergraduate">本科</option>
                                                    <option value="master">硕士</option>
                                                    <option value="phd">博士</option>
                                                    <option value="other">其他</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">个人简介</label>
                                            <textarea name="bio" class="form-input form-textarea" placeholder="简单介绍一下自己...">计算机科学专业研究生，专注于人工智能和机器学习领域的研究。</textarea>
                                        </div>
                                        
                                        <div class="flex justify-end space-x-4">
                                            <button type="button" class="btn btn-secondary">
                                                <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                                                取消
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                                                保存更改
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户设置 -->
                    <div id="settings-tab" class="tab-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 账户信息 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="settings" class="w-5 h-5"></i>
                                        </div>
                                        账户信息
                                    </h3>
                                    <p class="text-purple-100 mt-2">管理您的账户基本信息</p>
                                </div>
                                <div class="p-6 space-y-4">
                                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span class="text-gray-700 font-medium">用户ID</span>
                                        <span class="font-mono text-gray-900">USR-2024-001</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span class="text-gray-700 font-medium">注册时间</span>
                                        <span class="text-gray-900">2023-08-15</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span class="text-gray-700 font-medium">最后登录</span>
                                        <span class="text-gray-900">2024-01-15 14:30</span>
                                    </div>
                                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span class="text-gray-700 font-medium">账户状态</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                            正常
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 密码设置 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="lock" class="w-5 h-5"></i>
                                        </div>
                                        密码设置
                                    </h3>
                                    <p class="text-red-100 mt-2">修改您的登录密码</p>
                                </div>
                                <div class="p-6">
                                    <form id="password-form" class="space-y-4">
                                        <div class="form-group">
                                            <label class="form-label">当前密码</label>
                                            <input type="password" name="current_password" class="form-input" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">新密码</label>
                                            <input type="password" name="new_password" class="form-input" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">确认新密码</label>
                                            <input type="password" name="confirm_password" class="form-input" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary w-full">
                                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                                            更新密码
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 偏好设置 -->
                    <div id="preferences-tab" class="tab-content hidden">
                        <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                            <div class="bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-t-xl p-6">
                                <h3 class="text-xl font-bold flex items-center gap-3">
                                    <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                        <i data-lucide="sliders" class="w-5 h-5"></i>
                                    </div>
                                    偏好设置
                                </h3>
                                <p class="text-indigo-100 mt-2">自定义您的使用体验</p>
                            </div>
                            <div class="p-6 space-y-6" id="preferences-content">
                                <!-- 偏好设置内容将由JavaScript生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 安全设置 -->
                    <div id="security-tab" class="tab-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 两步验证 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="shield" class="w-5 h-5"></i>
                                        </div>
                                        两步验证
                                    </h3>
                                    <p class="text-green-100 mt-2">增强您的账户安全性</p>
                                </div>
                                <div class="p-6 space-y-4">
                                    <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                                        <div>
                                            <h4 class="font-medium text-green-900">短信验证</h4>
                                            <p class="text-sm text-green-700">通过手机短信接收验证码</p>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer" checked>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <h4 class="font-medium text-gray-900">邮箱验证</h4>
                                            <p class="text-sm text-gray-600">通过邮箱接收验证码</p>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 登录记录 -->
                            <div class="bg-white rounded-xl shadow-xl border-0 bg-gradient-to-br from-white to-gray-50 fade-in-up">
                                <div class="bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-t-xl p-6">
                                    <h3 class="text-xl font-bold flex items-center gap-3">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                            <i data-lucide="activity" class="w-5 h-5"></i>
                                        </div>
                                        登录记录
                                    </h3>
                                    <p class="text-orange-100 mt-2">查看最近的登录活动</p>
                                </div>
                                <div class="p-6 space-y-3" id="login-history">
                                    <!-- 登录记录将由JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Scripts -->
    <script src="js/global-fix.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/components.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/profile.js"></script>

    <!-- 初始化页面组件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面组件
            UIComponents.initializePage('profile');
        });
    </script>
</body>
</html>
