<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复测试页面</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- 紧急修复CSS -->
    <style>
        /* 强制显示所有内容 */
        * {
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .hidden.lg\:grid {
            display: grid !important;
        }
        
        #header-container,
        #sidebar-container,
        #mobile-nav-container,
        main,
        .container,
        .essay-type-card,
        .essay-type-card h3 {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }
        
        #essay-types-desktop {
            display: grid !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .essay-type-card h3 {
            color: #111827 !important;
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            margin-bottom: 0.25rem !important;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Container -->
    <div id="header-container"></div>

    <!-- Sidebar Container -->
    <div id="sidebar-container"></div>

    <!-- Mobile Navigation Container -->
    <div id="mobile-nav-container"></div>

    <!-- Main Content -->
    <main class="lg:ml-64 pb-16 lg:pb-0 pt-20">
        <div class="p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <!-- Page Title -->
                <div class="mb-8">
                    <h1 class="text-3xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-4">
                        紧急修复测试页面
                    </h1>
                    <p class="text-lg text-gray-600">
                        测试所有内容是否正常显示
                    </p>
                </div>

                <!-- Essay Types Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">论文类型选择</h2>

                    <!-- Desktop Grid -->
                    <div class="hidden lg:grid grid-cols-4 gap-6" id="essay-types-desktop">
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1 transition-all duration-300" data-type="literature">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600">
                                    <i data-lucide="book-open" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>文献综述</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1 transition-all duration-300" data-type="proposal">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600">
                                    <i data-lucide="lightbulb" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>开题报告</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1 transition-all duration-300" data-type="task">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-cyan-500 to-cyan-600">
                                    <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>任务书</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:-translate-y-1 transition-all duration-300" data-type="journal">
                            <div class="text-center">
                                <div class="w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-green-500 to-green-600">
                                    <i data-lucide="newspaper" class="w-6 h-6 text-white"></i>
                                </div>
                                <h3>期刊论文</h3>
                                <div class="text-xs text-gray-500">专业定制</div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Grid -->
                    <div class="lg:hidden grid grid-cols-2 gap-4" id="essay-types-mobile">
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-3" data-type="literature">
                            <div class="text-center">
                                <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-red-500 to-red-600">
                                    <i data-lucide="book-open" class="w-5 h-5 text-white"></i>
                                </div>
                                <h3>文献综述</h3>
                            </div>
                        </div>
                        
                        <div class="essay-type-card cursor-pointer bg-white rounded-xl shadow-lg border border-gray-200 p-3" data-type="proposal">
                            <div class="text-center">
                                <div class="w-10 h-10 mx-auto mb-2 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-r from-blue-500 to-blue-600">
                                    <i data-lucide="lightbulb" class="w-5 h-5 text-white"></i>
                                </div>
                                <h3>开题报告</h3>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Test Content -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">测试内容</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white p-6 rounded-xl shadow-lg">
                            <h3 class="text-lg font-bold text-gray-900 mb-2">测试卡片 1</h3>
                            <p class="text-gray-600">这是一个测试卡片，用于验证内容是否正常显示。</p>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-lg">
                            <h3 class="text-lg font-bold text-gray-900 mb-2">测试卡片 2</h3>
                            <p class="text-gray-600">这是另一个测试卡片，包含图标和文字。</p>
                            <div class="mt-4">
                                <i data-lucide="check-circle" class="w-5 h-5 text-green-500 inline-block mr-2"></i>
                                <span class="text-sm text-gray-500">图标测试</span>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-lg">
                            <h3 class="text-lg font-bold text-gray-900 mb-2">测试卡片 3</h3>
                            <p class="text-gray-600">最后一个测试卡片，验证布局是否正常。</p>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/emergency-fix.js"></script>
    <script src="js/components.js"></script>
    
    <script>
        // 立即修复
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 测试页面加载完成');
            
            // 初始化组件
            if (typeof UIComponents !== 'undefined') {
                UIComponents.initializePage('test');
            }
            
            // 初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            
            // 强制显示所有内容
            setTimeout(() => {
                document.querySelectorAll('.hidden.lg\\:grid').forEach(el => {
                    el.classList.remove('hidden');
                    el.style.display = 'grid';
                });
                
                document.querySelectorAll('.essay-type-card h3').forEach(h3 => {
                    h3.style.cssText = `
                        opacity: 1 !important;
                        visibility: visible !important;
                        display: block !important;
                        color: #111827 !important;
                        font-weight: 700 !important;
                    `;
                });
                
                console.log('✅ 测试页面修复完成');
            }, 100);
        });
    </script>
</body>
</html>
