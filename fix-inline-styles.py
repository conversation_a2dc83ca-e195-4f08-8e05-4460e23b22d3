#!/usr/bin/env python3
"""
脚本用于移除index.html中所有h3标签的内联!important样式
"""

import re

def fix_inline_styles():
    # 读取文件
    with open('index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除h3标签中的内联样式
    pattern = r'<h3 style="opacity: 1 !important; visibility: visible !important; display: block !important; color: #111827 !important;">'
    replacement = '<h3>'
    
    content = content.replace(pattern, replacement)
    
    # 写回文件
    with open('index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已移除所有h3标签的内联!important样式")

if __name__ == '__main__':
    fix_inline_styles()
