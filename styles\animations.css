/**
 * 优化的动画样式
 * 使用GPU加速和高性能动画技术
 */

/* 性能优化的基础动画类 */
.animate-element {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 页面切换动画 */
.page-transition-enter {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateY(0) translateZ(0);
}

.page-transition-exit {
    opacity: 1;
    transform: translateY(0) translateZ(0);
    transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateY(-10px) translateZ(0);
}

/* 卡片悬停动画 - GPU加速 */
.card-hover {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform: translateZ(0);
}

.card-hover:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 按钮动画 - 优化版 */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform: translateZ(0);
}

.btn-animated:hover {
    transform: translateY(-1px) translateZ(0);
}

.btn-animated:active {
    transform: translateY(0) translateZ(0);
}

/* 水波纹效果 */
.btn-animated::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-animated:active::before {
    width: 300px;
    height: 300px;
}

/* 滚动动画 */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px) translateZ(0);
    transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0) translateZ(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px) translateZ(0);
    transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0) translateZ(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px) translateZ(0);
    transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0) translateZ(0);
}

.zoom-in {
    opacity: 0;
    transform: scale(0.8) translateZ(0);
    transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.zoom-in.animate {
    opacity: 1;
    transform: scale(1) translateZ(0);
}

/* 交错动画 */
.stagger-animation {
    animation-delay: calc(var(--stagger-delay, 0) * 100ms);
}

/* 加载动画 - 优化版 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    will-change: transform;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 弹跳动画 */
.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* 摇摆动画 */
.shake {
    animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

/* 进度条动画 */
.progress-bar {
    position: relative;
    overflow: hidden;
    background-color: #e5e7eb;
    border-radius: 9999px;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: inherit;
    transform: translateX(-100%);
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar.animate::before {
    transform: translateX(0);
}

/* 滑动条动画 */
.slide-in-bottom {
    transform: translateY(100%) translateZ(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-bottom.animate {
    transform: translateY(0) translateZ(0);
}

.slide-in-top {
    transform: translateY(-100%) translateZ(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-top.animate {
    transform: translateY(0) translateZ(0);
}

/* 视差滚动 */
.parallax {
    will-change: transform;
    transform: translateZ(0);
}

/* 粘性导航动画 */
.sticky-nav {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, background-color;
}

.sticky-nav.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 模态框动画 */
.modal-overlay {
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
    opacity: 1;
}

.modal-content {
    opacity: 0;
    transform: scale(0.95) translateZ(0);
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content.show {
    opacity: 1;
    transform: scale(1) translateZ(0);
}

/* 通知动画 */
.notification {
    transform: translateX(100%) translateZ(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification.show {
    transform: translateX(0) translateZ(0);
}

.notification.hide {
    transform: translateX(100%) translateZ(0);
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: none;
    }
    
    .btn-animated:hover {
        transform: none;
    }
    
    /* 减少移动端动画以节省电池 */
    .fade-in-up,
    .fade-in-left,
    .fade-in-right,
    .zoom-in {
        transition-duration: 0.3s;
    }
}

/* 高性能模式 */
@media (max-width: 480px) {
    .parallax {
        transform: none !important;
    }
    
    .card-hover,
    .btn-animated {
        will-change: auto;
    }
}

/* 暗色模式动画调整 */
@media (prefers-color-scheme: dark) {
    .loading-spinner {
        border-color: #374151;
        border-top-color: #60a5fa;
    }
    
    .progress-bar {
        background-color: #374151;
    }
}
