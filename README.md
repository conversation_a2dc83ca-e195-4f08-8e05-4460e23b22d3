# AI论文写作平台 - HTML版本

这是一个使用纯HTML、CSS和JavaScript重构的AI论文写作平台，保持了原有Next.js版本的所有功能和设计风格。

## 📁 项目结构

```
html/
├── index.html              # 主页 - AI论文写作
├── history.html            # 历史记录页面
├── plagiarism.html         # 论文查重页面
├── aigc.html              # AIGC智能降重页面
├── billing.html           # 套餐管理页面
├── profile.html           # 个人中心页面
├── styles/
│   └── main.css           # 主要样式文件
├── js/
│   ├── main.js            # 主要JavaScript功能
│   ├── essay-forms.js     # 表单处理和验证
│   ├── history.js         # 历史记录页面功能
│   ├── plagiarism.js      # 查重页面功能
│   ├── aigc.js            # AIGC降重页面功能
│   ├── billing.js         # 套餐管理页面功能
│   └── profile.js         # 个人中心页面功能
└── README.md              # 说明文档
```

## 🚀 功能特性

### 主页功能 (index.html)
- ✅ 响应式设计，支持桌面端和移动端
- ✅ 12种论文类型选择（毕业论文、文献综述、开题报告、任务书、期刊论文、专升本论文、期末论文、课程论文、实习报告、创业策划、研究论文、职业规划）
- ✅ 默认选中毕业论文类型，符合主要用户需求
- ✅ 动态表单生成，根据论文类型显示不同字段
- ✅ 完全参照原项目的论文类型和表单配置
- ✅ 实时表单验证和字符计数
- ✅ 本地数据持久化
- ✅ AI论文生成模拟（带进度条）
- ✅ 大纲生成功能

### 历史记录页面 (history.html)
- ✅ 论文列表展示和管理
- ✅ 搜索和筛选功能
- ✅ 分页显示
- ✅ 标签页切换（全部、已完成、进行中、草稿）
- ✅ 论文操作（查看、编辑、下载、删除）
- ✅ 数据导出功能

### 论文查重页面 (plagiarism.html)
- ✅ 文本输入和文件上传两种方式
- ✅ 拖拽上传支持
- ✅ 文件格式和大小验证
- ✅ 查重进度显示
- ✅ 详细查重结果展示
- ✅ 数据库对比结果
- ✅ 重复内容详情

### AIGC智能降重页面 (aigc.html)
- ✅ 多级降重选择（轻度/中度/深度）
- ✅ 文本输入和文件上传支持
- ✅ 实时字符计数和预估时间
- ✅ 智能改写进度显示
- ✅ 降重结果对比展示
- ✅ 一键复制、重新生成、下载功能
- ✅ 语义保持度显示

### 套餐管理页面 (billing.html)
- ✅ 三种套餐选择（基础版/专业版/企业版）
- ✅ 使用情况统计和可视化
- ✅ 订阅信息管理
- ✅ 账单历史记录
- ✅ 发票下载和查看
- ✅ 套餐升级和降级
- ✅ 自动续费设置

### 个人中心页面 (profile.html)
- ✅ 个人信息编辑和头像上传
- ✅ 账户设置和密码修改
- ✅ 偏好设置（语言/主题/通知）
- ✅ 安全设置和两步验证
- ✅ 登录记录和设备管理
- ✅ 隐私设置和权限控制
- ✅ 数据导出和账户注销

### 通用功能
- ✅ 响应式导航栏和侧边栏
- ✅ 移动端底部导航
- ✅ Toast通知系统
- ✅ 加载动画和进度条
- ✅ 图标系统（Lucide Icons）
- ✅ 优雅的渐变和动画效果

## 🛠️ 技术栈

- **HTML5**: 语义化标记
- **CSS3**: 
  - Tailwind CSS (CDN)
  - 自定义CSS变量和动画
  - 响应式设计
  - 移动端优化
- **JavaScript (ES6+)**:
  - 模块化代码组织
  - 事件驱动架构
  - 本地存储管理
  - 异步操作处理
- **外部依赖**:
  - Tailwind CSS CDN
  - Lucide Icons CDN

## 📱 响应式设计

### 桌面端 (≥1024px)
- 侧边栏导航
- 多列布局
- 悬停效果
- 完整功能展示

### 平板端 (768px-1023px)
- 自适应布局
- 触摸优化
- 简化界面元素

### 移动端 (<768px)
- 底部导航栏
- 单列布局
- 触摸友好的按钮尺寸
- 滑动菜单

## 🎨 设计特色

### 视觉效果
- 🌈 **现代化渐变背景** - 多层次渐变和粒子效果
- 💳 **高级卡片设计** - 玻璃态效果和悬浮阴影
- ✨ **流畅动画过渡** - 60fps丝滑动画体验
- 🎭 **交错动画** - 元素依次出现的视觉层次
- 🔮 **霓虹灯效果** - 特殊场景下的发光文字
- 🌊 **波纹交互** - 按钮点击的水波纹反馈

### 交互体验
- ⚡ **即时反馈** - 实时表单验证和状态更新
- 🎯 **磁性按钮** - 悬浮时的光泽扫过效果
- 📱 **触摸优化** - 移动端44px最小触摸目标
- ⌨️ **键盘快捷键** - Ctrl+Enter快速生成，Esc关闭弹窗
- 🔄 **智能预加载** - 关键页面资源预加载
- 🎪 **滚动动画** - 基于Intersection Observer的滚动触发

### 高级功能
- 🆘 **浮动帮助按钮** - 随时获取使用指导
- 🔝 **返回顶部** - 智能显示/隐藏的回顶按钮
- 🎨 **主题适配** - 支持高对比度和减少动画模式
- 📊 **性能监控** - 防抖节流和资源优化
- 🔍 **无障碍支持** - 完整的ARIA标签和键盘导航

### 颜色系统
- 🎨 **主色调**：蓝色到紫色渐变 (#3b82f6 → #8b5cf6)
- ✅ **成功色**：绿色渐变 (#10b981 → #059669)
- ⚠️ **警告色**：橙色渐变 (#f59e0b → #d97706)
- ❌ **错误色**：红色渐变 (#ef4444 → #dc2626)
- ℹ️ **信息色**：青色渐变 (#06b6d4 → #0891b2)
- 🌫️ **中性色**：灰色系列 (#f8fafc → #0f172a)



