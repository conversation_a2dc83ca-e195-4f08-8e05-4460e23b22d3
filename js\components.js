// 共用组件 - 页头和侧边栏（性能优化版）
class UIComponents {
    // 生成页头HTML
    static generateHeader(currentPage = '') {
        return `
            <header class="bg-white/95 backdrop-blur-sm fixed top-0 left-0 right-0 z-50 border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between h-20">
                        <!-- Logo -->
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="pen-tool" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-bold text-gray-900">博晓文</h1>
                                <p class="text-xs text-gray-500">高质量原创AI论文写作平台</p>
                            </div>
                        </div>
                        
                        <!-- Desktop Navigation - 优化的页面切换 -->
                        <nav class="hidden lg:flex items-center space-x-8">
                            <a href="index.html" class="${currentPage === 'index' ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'} transition-colors smooth-link" data-preload="true">首页</a>
                            <a href="history.html" class="${currentPage === 'history' ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'} transition-colors smooth-link" data-preload="true">历史记录</a>
                            <a href="plagiarism.html" class="${currentPage === 'plagiarism' ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'} transition-colors smooth-link" data-preload="true">论文查重</a>
                            <a href="aigc.html" class="${currentPage === 'aigc' ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'} transition-colors smooth-link" data-preload="true">AIGC降重</a>
                            <a href="billing.html" class="${currentPage === 'billing' ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'} transition-colors smooth-link" data-preload="true">套餐管理</a>
                        </nav>
                        
                        <!-- User Menu -->
                        <div class="flex items-center space-x-4">
                            <button class="p-2 text-gray-500 hover:text-gray-700 transition-colors" onclick="showNotifications()">
                                <i data-lucide="bell" class="w-5 h-5"></i>
                            </button>
                            <button class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:scale-110 transition-transform" onclick="showUserMenu()">
                                <i data-lucide="user" class="w-4 h-4 text-white"></i>
                            </button>
                        </div>
                        
                        <!-- Mobile Menu Button -->
                        <button class="lg:hidden p-2 text-gray-500 hover:text-gray-700" id="mobile-menu-btn">
                            <i data-lucide="menu" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
            </header>
        `;
    }

    // 生成侧边栏HTML
    static generateSidebar(currentPage = '') {
        const menuItems = [
            {
                id: 'index',
                href: 'index.html',
                icon: 'pen-tool',
                label: 'AI论文写作',
                color: 'blue'
            },
            {
                id: 'history',
                href: 'history.html',
                icon: 'history',
                label: '历史记录',
                color: 'green'
            },
            {
                id: 'plagiarism',
                href: 'plagiarism.html',
                icon: 'file-check',
                label: '论文查重',
                color: 'red'
            },
            {
                id: 'aigc',
                href: 'aigc.html',
                icon: 'refresh-cw',
                label: 'AIGC降重',
                color: 'cyan'
            },
            {
                id: 'billing',
                href: 'billing.html',
                icon: 'credit-card',
                label: '套餐管理',
                color: 'pink'
            },
            {
                id: 'profile',
                href: 'profile.html',
                icon: 'user',
                label: '个人中心',
                color: 'gray'
            }
        ];

        const menuHTML = menuItems.map(item => {
            const isActive = currentPage === item.id;
            const activeClasses = isActive 
                ? `bg-${item.color}-50 text-${item.color}-600 border border-${item.color}-200`
                : 'text-gray-700 hover:bg-gray-50 transition-colors';
            
            return `
                <a href="${item.href}" class="flex items-center space-x-3 p-3 rounded-lg ${activeClasses}">
                    <div class="w-8 h-8 bg-${item.color}-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="${item.icon}" class="w-4 h-4 text-${item.color}-600"></i>
                    </div>
                    <span ${isActive ? 'class="font-medium"' : ''}>${item.label}</span>
                </a>
            `;
        }).join('');

        return `
            <aside class="fixed left-0 top-20 h-[calc(100vh-5rem)] w-64 bg-white border-r border-gray-200 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 z-40 overflow-y-auto" id="sidebar">
                <div class="p-6">
                    <nav class="space-y-2">
                        ${menuHTML}
                    </nav>
                </div>
            </aside>
        `;
    }

    // 生成移动端底部导航
    static generateMobileNav(currentPage = '') {
        const navItems = [
            { id: 'index', href: 'index.html', icon: 'pen-tool', label: '写作' },
            { id: 'history', href: 'history.html', icon: 'history', label: '历史' },
            { id: 'plagiarism', href: 'plagiarism.html', icon: 'file-check', label: '查重' },
            { id: 'aigc', href: 'aigc.html', icon: 'refresh-cw', label: '降重' },
            { id: 'profile', href: 'profile.html', icon: 'user', label: '我的' }
        ];

        const navHTML = navItems.map(item => {
            const isActive = currentPage === item.id;
            const activeClass = isActive ? 'text-blue-600' : 'text-gray-500';
            
            return `
                <a href="${item.href}" class="flex flex-col items-center justify-center ${activeClass}">
                    <i data-lucide="${item.icon}" class="w-5 h-5"></i>
                    <span class="text-xs mt-1">${item.label}</span>
                </a>
            `;
        }).join('');

        return `
            <nav class="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
                <div class="grid grid-cols-5 h-16">
                    ${navHTML}
                </div>
            </nav>
        `;
    }

    // 初始化页面组件 - 增强版
    static initializePage(currentPage = '') {
        console.log('🚀 初始化页面组件:', currentPage);

        try {
            // 插入页头
            const headerContainer = document.getElementById('header-container');
            if (headerContainer) {
                headerContainer.innerHTML = this.generateHeader(currentPage);
                console.log('✅ 页头组件已加载');
            } else {
                console.warn('⚠️ 未找到header-container元素');
            }

            // 插入侧边栏
            const sidebarContainer = document.getElementById('sidebar-container');
            if (sidebarContainer) {
                sidebarContainer.innerHTML = this.generateSidebar(currentPage);
                console.log('✅ 侧边栏组件已加载');
            } else {
                console.warn('⚠️ 未找到sidebar-container元素');
            }

            // 插入移动端导航
            const mobileNavContainer = document.getElementById('mobile-nav-container');
            if (mobileNavContainer) {
                mobileNavContainer.innerHTML = this.generateMobileNav(currentPage);
                console.log('✅ 移动端导航组件已加载');
            } else {
                console.warn('⚠️ 未找到mobile-nav-container元素');
            }

            // 重新初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('✅ 图标已初始化');
            } else {
                console.warn('⚠️ Lucide图标库未加载');
            }

            // 绑定移动端菜单事件
            this.bindMobileMenuEvents();

            // 确保所有内容可见
            this.ensureContentVisible();

            console.log('🎉 页面组件初始化完成');
        } catch (error) {
            console.error('❌ 页面组件初始化失败:', error);
        }
    }

    // 确保内容可见
    static ensureContentVisible() {
        setTimeout(() => {
            // 确保主要容器可见
            const containers = ['main', '.container', 'header', 'nav', '.sidebar'];
            containers.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    if (el.style.opacity === '0') el.style.opacity = '1';
                    if (el.style.visibility === 'hidden') el.style.visibility = 'visible';
                    if (el.style.display === 'none') el.style.display = '';
                });
            });

            // 确保所有文字内容可见
            document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, button').forEach(el => {
                if (el.style.opacity === '0' && !el.classList.contains('opacity-0')) {
                    el.style.opacity = '1';
                }
                if (el.style.visibility === 'hidden' && !el.classList.contains('invisible')) {
                    el.style.visibility = 'visible';
                }
            });

            console.log('✅ 内容可见性检查完成');
        }, 100);
    }

    // 绑定移动端菜单事件
    static bindMobileMenuEvents() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn && sidebar) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('-translate-x-full');
            });

            // 点击外部关闭侧边栏
            document.addEventListener('click', (e) => {
                if (window.innerWidth < 1024 && 
                    sidebar && 
                    !sidebar.contains(e.target) && 
                    !mobileMenuBtn.contains(e.target)) {
                    sidebar.classList.add('-translate-x-full');
                }
            });
        }
    }
}

// 用户菜单功能
function showUserMenu() {
    // 创建用户菜单下拉
    const existingMenu = document.getElementById('user-menu-dropdown');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    const userMenu = document.createElement('div');
    userMenu.id = 'user-menu-dropdown';
    userMenu.className = 'absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50';
    userMenu.innerHTML = `
        <a href="profile.html" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
            <i data-lucide="user" class="w-4 h-4 mr-3"></i>
            个人中心
        </a>
        <a href="billing.html" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
            <i data-lucide="credit-card" class="w-4 h-4 mr-3"></i>
            套餐管理
        </a>
        <hr class="my-2">
        <button onclick="showLoginModal()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
            <i data-lucide="log-in" class="w-4 h-4 mr-3"></i>
            登录
        </button>
        <button onclick="showRegisterModal()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
            <i data-lucide="user-plus" class="w-4 h-4 mr-3"></i>
            注册
        </button>
        <hr class="my-2">
        <button onclick="logout()" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
            <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
            退出登录
        </button>
    `;

    // 找到用户按钮并添加菜单
    const userButton = document.querySelector('[onclick="showUserMenu()"]');
    if (userButton) {
        const container = userButton.parentElement;
        container.style.position = 'relative';
        container.appendChild(userMenu);
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // 点击外部关闭菜单
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!userMenu.contains(e.target) && !userButton.contains(e.target)) {
                    userMenu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }
}

// 通知功能
function showNotifications() {
    alert('暂无新通知');
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除本地存储的用户数据
        localStorage.removeItem('userToken');
        localStorage.removeItem('userInfo');
        
        // 显示退出成功消息
        showToast('已成功退出登录', 'success');
        
        // 关闭用户菜单
        const userMenu = document.getElementById('user-menu-dropdown');
        if (userMenu) {
            userMenu.remove();
        }
    }
}

// 全局导出
window.UIComponents = UIComponents;
window.showUserMenu = showUserMenu;
window.showNotifications = showNotifications;
window.logout = logout;
