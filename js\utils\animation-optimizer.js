/**
 * 动画性能优化器
 * 优化CSS和JavaScript动画，使用GPU加速，减少重排重绘
 */

class AnimationOptimizer {
    constructor() {
        this.activeAnimations = new Set();
        this.animationQueue = [];
        this.rafId = null;
        this.isReducedMotion = false;
        this.performanceMode = 'auto';
        
        this.init();
    }

    init() {
        this.detectMotionPreferences();
        this.setupPerformanceMode();
        this.optimizeExistingAnimations();
        this.setupAnimationMonitoring();
        this.addOptimizedAnimationStyles();
    }

    // 检测动画偏好
    detectMotionPreferences() {
        if (window.matchMedia) {
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            this.isReducedMotion = prefersReducedMotion.matches;
            
            prefersReducedMotion.addEventListener('change', (e) => {
                this.isReducedMotion = e.matches;
                this.updateAnimationSettings();
            });
        }
    }

    // 设置性能模式
    setupPerformanceMode() {
        // 根据设备性能自动调整
        this.detectDevicePerformance();
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAllAnimations();
            } else {
                this.resumeAllAnimations();
            }
        });
    }

    // 检测设备性能
    detectDevicePerformance() {
        // 检测硬件并发数
        const cores = navigator.hardwareConcurrency || 4;
        
        // 检测内存
        const memory = navigator.deviceMemory || 4;
        
        // 检测连接速度
        const connection = navigator.connection;
        const effectiveType = connection ? connection.effectiveType : '4g';
        
        // 根据设备能力设置性能模式
        if (cores <= 2 || memory <= 2 || effectiveType === 'slow-2g' || effectiveType === '2g') {
            this.performanceMode = 'low';
        } else if (cores >= 8 && memory >= 8) {
            this.performanceMode = 'high';
        } else {
            this.performanceMode = 'medium';
        }
        
        console.log('🎭 Animation performance mode:', this.performanceMode);
        this.updateAnimationSettings();
    }

    // 更新动画设置
    updateAnimationSettings() {
        const root = document.documentElement;
        
        if (this.isReducedMotion) {
            root.style.setProperty('--animation-duration', '0.01ms');
            root.style.setProperty('--transition-duration', '0.01ms');
            return;
        }
        
        switch (this.performanceMode) {
            case 'low':
                root.style.setProperty('--animation-duration', '0.2s');
                root.style.setProperty('--transition-duration', '0.15s');
                root.style.setProperty('--animation-complexity', 'simple');
                break;
            case 'medium':
                root.style.setProperty('--animation-duration', '0.3s');
                root.style.setProperty('--transition-duration', '0.2s');
                root.style.setProperty('--animation-complexity', 'medium');
                break;
            case 'high':
                root.style.setProperty('--animation-duration', '0.5s');
                root.style.setProperty('--transition-duration', '0.3s');
                root.style.setProperty('--animation-complexity', 'complex');
                break;
        }
    }

    // 优化现有动画
    optimizeExistingAnimations() {
        // 优化CSS动画
        this.optimizeCSSAnimations();
        
        // 优化JavaScript动画
        this.optimizeJSAnimations();
        
        // 添加GPU加速
        this.addGPUAcceleration();
    }

    // 优化CSS动画
    optimizeCSSAnimations() {
        const animatedElements = document.querySelectorAll('[class*="animate"], [style*="animation"], [style*="transition"]');
        
        animatedElements.forEach(element => {
            // 添加GPU加速
            this.enableGPUAcceleration(element);
            
            // 优化动画属性
            this.optimizeAnimationProperties(element);
            
            // 设置合成层
            this.setupCompositeLayer(element);
        });
    }

    // 启用GPU加速
    enableGPUAcceleration(element) {
        const style = element.style;
        
        // 添加transform: translateZ(0)来触发GPU加速
        if (!style.transform || !style.transform.includes('translateZ')) {
            const currentTransform = style.transform || '';
            style.transform = currentTransform ? `${currentTransform} translateZ(0)` : 'translateZ(0)';
        }
        
        // 设置will-change属性
        if (!style.willChange) {
            style.willChange = 'transform, opacity';
        }
        
        // 设置backface-visibility
        if (!style.backfaceVisibility) {
            style.backfaceVisibility = 'hidden';
        }
    }

    // 优化动画属性
    optimizeAnimationProperties(element) {
        const computedStyle = getComputedStyle(element);
        const animationName = computedStyle.animationName;
        
        if (animationName && animationName !== 'none') {
            // 检查动画是否使用了性能友好的属性
            this.validateAnimationProperties(element, animationName);
        }
    }

    // 验证动画属性
    validateAnimationProperties(element, animationName) {
        // 获取关键帧规则
        const keyframes = this.getKeyframesRule(animationName);
        if (!keyframes) return;
        
        const expensiveProperties = ['width', 'height', 'top', 'left', 'margin', 'padding', 'border'];
        const cheapProperties = ['transform', 'opacity', 'filter'];
        
        // 检查是否使用了昂贵的属性
        for (let i = 0; i < keyframes.cssRules.length; i++) {
            const rule = keyframes.cssRules[i];
            const cssText = rule.style.cssText;
            
            const usesExpensiveProps = expensiveProperties.some(prop => cssText.includes(prop));
            if (usesExpensiveProps) {
                console.warn('⚠️ Animation uses expensive properties:', animationName, element);
                this.suggestOptimization(element, animationName);
            }
        }
    }

    // 获取关键帧规则
    getKeyframesRule(animationName) {
        for (const stylesheet of document.styleSheets) {
            try {
                for (const rule of stylesheet.cssRules) {
                    if (rule.type === CSSRule.KEYFRAMES_RULE && rule.name === animationName) {
                        return rule;
                    }
                }
            } catch (e) {
                // 跨域样式表无法访问
                continue;
            }
        }
        return null;
    }

    // 建议优化
    suggestOptimization(element, animationName) {
        // 为元素添加优化建议
        element.dataset.animationOptimization = 'suggested';
        
        // 如果是低性能模式，自动应用优化
        if (this.performanceMode === 'low') {
            this.applyOptimization(element);
        }
    }

    // 应用优化
    applyOptimization(element) {
        // 简化动画
        element.style.animationDuration = '0.2s';
        element.style.animationTimingFunction = 'ease-out';
        
        // 减少动画复杂度
        if (element.classList.contains('complex-animation')) {
            element.classList.remove('complex-animation');
            element.classList.add('simple-animation');
        }
    }

    // 设置合成层
    setupCompositeLayer(element) {
        // 为动画元素创建独立的合成层
        element.style.isolation = 'isolate';
        element.style.contain = 'layout style paint';
    }

    // 优化JavaScript动画
    optimizeJSAnimations() {
        // 替换setInterval/setTimeout动画为requestAnimationFrame
        this.replaceTimerAnimations();
        
        // 批量处理DOM操作
        this.batchDOMOperations();
    }

    // 替换定时器动画
    replaceTimerAnimations() {
        // 监听定时器创建
        const originalSetInterval = window.setInterval;
        const originalSetTimeout = window.setTimeout;
        
        window.setInterval = (callback, delay) => {
            if (delay <= 16) { // 可能是动画
                console.warn('⚠️ Consider using requestAnimationFrame instead of setInterval for animations');
            }
            return originalSetInterval(callback, delay);
        };
        
        window.setTimeout = (callback, delay) => {
            if (delay <= 16) { // 可能是动画
                console.warn('⚠️ Consider using requestAnimationFrame instead of setTimeout for animations');
            }
            return originalSetTimeout(callback, delay);
        };
    }

    // 批量处理DOM操作
    batchDOMOperations() {
        let pendingOperations = [];
        let rafScheduled = false;
        
        const flushOperations = () => {
            pendingOperations.forEach(operation => operation());
            pendingOperations = [];
            rafScheduled = false;
        };
        
        // 提供批量操作接口
        window.batchDOMUpdate = (operation) => {
            pendingOperations.push(operation);
            
            if (!rafScheduled) {
                rafScheduled = true;
                requestAnimationFrame(flushOperations);
            }
        };
    }

    // 创建优化的动画
    createOptimizedAnimation(element, keyframes, options = {}) {
        // 检查是否支持Web Animations API
        if (!element.animate) {
            return this.fallbackAnimation(element, keyframes, options);
        }
        
        // 优化关键帧
        const optimizedKeyframes = this.optimizeKeyframes(keyframes);
        
        // 优化选项
        const optimizedOptions = this.optimizeAnimationOptions(options);
        
        // 创建动画
        const animation = element.animate(optimizedKeyframes, optimizedOptions);
        
        // 监控动画性能
        this.monitorAnimation(animation);
        
        return animation;
    }

    // 优化关键帧
    optimizeKeyframes(keyframes) {
        return keyframes.map(frame => {
            const optimizedFrame = {};
            
            // 只保留性能友好的属性
            Object.keys(frame).forEach(property => {
                if (this.isPerformantProperty(property)) {
                    optimizedFrame[property] = frame[property];
                } else {
                    console.warn(`⚠️ Property ${property} may cause performance issues`);
                    // 尝试转换为transform
                    const transformEquivalent = this.getTransformEquivalent(property, frame[property]);
                    if (transformEquivalent) {
                        optimizedFrame.transform = transformEquivalent;
                    }
                }
            });
            
            return optimizedFrame;
        });
    }

    // 检查属性是否性能友好
    isPerformantProperty(property) {
        const performantProperties = [
            'transform', 'opacity', 'filter', 'clip-path',
            'mask', 'mix-blend-mode', 'isolation'
        ];
        
        return performantProperties.includes(property);
    }

    // 获取transform等效属性
    getTransformEquivalent(property, value) {
        const transformMap = {
            'left': `translateX(${value})`,
            'top': `translateY(${value})`,
            'width': `scaleX(${parseFloat(value) / 100})`, // 简化处理
            'height': `scaleY(${parseFloat(value) / 100})` // 简化处理
        };
        
        return transformMap[property];
    }

    // 优化动画选项
    optimizeAnimationOptions(options) {
        const optimized = { ...options };
        
        // 根据性能模式调整持续时间
        if (this.performanceMode === 'low' && optimized.duration > 300) {
            optimized.duration = 300;
        }
        
        // 设置合适的缓动函数
        if (!optimized.easing) {
            optimized.easing = 'ease-out'; // 通常比ease-in-out性能更好
        }
        
        return optimized;
    }

    // 监控动画
    monitorAnimation(animation) {
        this.activeAnimations.add(animation);
        
        animation.addEventListener('finish', () => {
            this.activeAnimations.delete(animation);
        });
        
        animation.addEventListener('cancel', () => {
            this.activeAnimations.delete(animation);
        });
    }

    // 暂停所有动画
    pauseAllAnimations() {
        this.activeAnimations.forEach(animation => {
            if (animation.playState === 'running') {
                animation.pause();
            }
        });
        
        // 暂停CSS动画
        document.body.style.animationPlayState = 'paused';
    }

    // 恢复所有动画
    resumeAllAnimations() {
        this.activeAnimations.forEach(animation => {
            if (animation.playState === 'paused') {
                animation.play();
            }
        });
        
        // 恢复CSS动画
        document.body.style.animationPlayState = 'running';
    }

    // 设置动画监控
    setupAnimationMonitoring() {
        // 监控长时间运行的动画
        setInterval(() => {
            this.checkLongRunningAnimations();
        }, 5000);
        
        // 监控动画性能
        this.monitorAnimationPerformance();
    }

    // 检查长时间运行的动画
    checkLongRunningAnimations() {
        this.activeAnimations.forEach(animation => {
            if (animation.currentTime > 10000) { // 超过10秒
                console.warn('⚠️ Long running animation detected:', animation);
            }
        });
    }

    // 监控动画性能
    monitorAnimationPerformance() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.duration > 16) { // 超过一帧的时间
                        console.warn('⚠️ Animation frame drop detected:', entry);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure'] });
        }
    }

    // 添加优化的动画样式
    addOptimizedAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 优化的动画基类 */
            .optimized-animation {
                will-change: transform, opacity;
                transform: translateZ(0);
                backface-visibility: hidden;
                perspective: 1000px;
            }
            
            /* 性能友好的动画 */
            .fade-optimized {
                transition: opacity var(--transition-duration, 0.3s) ease-out;
            }
            
            .slide-optimized {
                transition: transform var(--transition-duration, 0.3s) ease-out;
            }
            
            .scale-optimized {
                transition: transform var(--transition-duration, 0.3s) ease-out;
            }
            
            /* 减少动画模式 */
            @media (prefers-reduced-motion: reduce) {
                .optimized-animation {
                    animation-duration: 0.01ms !important;
                    transition-duration: 0.01ms !important;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    // 获取性能报告
    getPerformanceReport() {
        return {
            activeAnimations: this.activeAnimations.size,
            performanceMode: this.performanceMode,
            reducedMotion: this.isReducedMotion,
            optimizationsSuggested: document.querySelectorAll('[data-animation-optimization="suggested"]').length
        };
    }
}

// 导出单例
export const animationOptimizer = new AnimationOptimizer();
export default AnimationOptimizer;
