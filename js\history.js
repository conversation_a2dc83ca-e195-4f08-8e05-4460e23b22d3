// 历史记录数据
const historyData = [
    {
        id: 1,
        title: "基于深度学习的图像识别技术研究",
        type: "academic",
        typeName: "学术论文",
        status: "completed",
        statusName: "已完成",
        wordCount: 15000,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-16",
    },
    {
        id: 2,
        title: "人工智能在教育领域的应用与发展",
        type: "academic",
        typeName: "学术论文",
        status: "processing",
        statusName: "生成中",
        wordCount: 8000,
        createdAt: "2024-01-14",
        updatedAt: "2024-01-14",
    },
    {
        id: 3,
        title: "区块链技术在供应链管理中的应用研究",
        type: "review",
        typeName: "文献综述",
        status: "completed",
        statusName: "已完成",
        wordCount: 12000,
        createdAt: "2024-01-13",
        updatedAt: "2024-01-13",
    },
    {
        id: 4,
        title: "大数据时代下的隐私保护机制研究",
        type: "academic",
        typeName: "学术论文",
        status: "draft",
        statusName: "草稿",
        wordCount: 5000,
        createdAt: "2024-01-12",
        updatedAt: "2024-01-12",
    },
    {
        id: 5,
        title: "企业数字化转型战略研究",
        type: "business",
        typeName: "商业计划",
        status: "completed",
        statusName: "已完成",
        wordCount: 18000,
        createdAt: "2024-01-11",
        updatedAt: "2024-01-11",
    },
    {
        id: 6,
        title: "移动互联网用户行为分析报告",
        type: "practice",
        typeName: "实习报告",
        status: "processing",
        statusName: "生成中",
        wordCount: 7500,
        createdAt: "2024-01-10",
        updatedAt: "2024-01-10",
    },
    {
        id: 7,
        title: "新能源汽车市场发展趋势分析",
        type: "review",
        typeName: "文献综述",
        status: "completed",
        statusName: "已完成",
        wordCount: 14000,
        createdAt: "2024-01-09",
        updatedAt: "2024-01-09",
    },
    {
        id: 8,
        title: "智能制造技术在传统制造业中的应用",
        type: "academic",
        typeName: "学术论文",
        status: "draft",
        statusName: "草稿",
        wordCount: 3000,
        createdAt: "2024-01-08",
        updatedAt: "2024-01-08",
    }
];

// 分页配置
let currentPage = 1;
const itemsPerPage = 10;
let currentTab = 'all';
let filteredData = [...historyData];

// DOM 元素
let historyElements = {};

// 初始化历史记录页面
document.addEventListener('DOMContentLoaded', function() {
    initializeHistoryElements();
    initializeLucideIcons();
    bindHistoryEvents();
    renderHistoryTable();
    renderPagination();
});

// 初始化DOM元素引用
function initializeHistoryElements() {
    historyElements = {
        searchInput: document.getElementById('search-input'),
        typeFilter: document.getElementById('type-filter'),
        statusFilter: document.getElementById('status-filter'),
        tabButtons: document.querySelectorAll('.tab-button'),
        tableBody: document.getElementById('history-table-body'),
        pagination: document.getElementById('pagination'),
        startItem: document.getElementById('start-item'),
        endItem: document.getElementById('end-item'),
        totalItems: document.getElementById('total-items'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn')
    };
}

// 绑定历史记录页面事件
function bindHistoryEvents() {
    // 搜索功能
    if (historyElements.searchInput) {
        historyElements.searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
    
    // 筛选功能
    if (historyElements.typeFilter) {
        historyElements.typeFilter.addEventListener('change', handleFilter);
    }
    
    if (historyElements.statusFilter) {
        historyElements.statusFilter.addEventListener('change', handleFilter);
    }
    
    // 标签页切换
    historyElements.tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 移动端菜单
    if (historyElements.mobileMenuBtn) {
        historyElements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            historyElements.sidebar && 
            !historyElements.sidebar.contains(e.target) && 
            historyElements.mobileMenuBtn &&
            !historyElements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 处理搜索
function handleSearch() {
    const searchTerm = historyElements.searchInput.value.toLowerCase();
    applyFilters();
}

// 处理筛选
function handleFilter() {
    applyFilters();
}

// 应用筛选条件
function applyFilters() {
    const searchTerm = historyElements.searchInput.value.toLowerCase();
    const typeFilter = historyElements.typeFilter.value;
    const statusFilter = historyElements.statusFilter.value;
    
    filteredData = historyData.filter(item => {
        const matchesSearch = item.title.toLowerCase().includes(searchTerm);
        const matchesType = !typeFilter || item.type === typeFilter;
        const matchesStatus = !statusFilter || item.status === statusFilter;
        const matchesTab = currentTab === 'all' || item.status === currentTab;
        
        return matchesSearch && matchesType && matchesStatus && matchesTab;
    });
    
    currentPage = 1;
    renderHistoryTable();
    renderPagination();
}

// 切换标签页
function switchTab(tab) {
    currentTab = tab;
    
    // 更新标签页样式
    historyElements.tabButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('border-blue-500', 'text-blue-600', 'active');
            button.classList.remove('border-transparent', 'text-gray-500');
        } else {
            button.classList.remove('border-blue-500', 'text-blue-600', 'active');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });
    
    applyFilters();
}

// 渲染历史记录表格
function renderHistoryTable() {
    if (!historyElements.tableBody) return;
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        historyElements.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i data-lucide="file-x" class="w-12 h-12 text-gray-300 mb-4"></i>
                        <p class="text-lg font-medium">暂无数据</p>
                        <p class="text-sm">没有找到符合条件的论文记录</p>
                    </div>
                </td>
            </tr>
        `;
        initializeLucideIcons();
        return;
    }
    
    const html = pageData.map(item => `
        <tr class="hover:bg-gray-50 transition-colors">
            <td class="px-6 py-4">
                <div class="max-w-xs">
                    <div class="font-medium text-gray-900 truncate">${item.title}</div>
                    <div class="text-sm text-gray-500">ID: ${item.id}</div>
                </div>
            </td>
            <td class="px-6 py-4">
                <span class="badge badge-primary">${item.typeName}</span>
            </td>
            <td class="px-6 py-4">
                <span class="badge ${getStatusBadgeClass(item.status)}">${item.statusName}</span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900">
                ${item.wordCount.toLocaleString()}字
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
                ${item.createdAt}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
                ${item.updatedAt}
            </td>
            <td class="px-6 py-4 text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                    <button onclick="viewEssay(${item.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded touch-target" title="查看">
                        <i data-lucide="eye" class="w-4 h-4"></i>
                    </button>
                    <button onclick="editEssay(${item.id})" class="text-green-600 hover:text-green-900 p-1 rounded touch-target" title="编辑">
                        <i data-lucide="edit" class="w-4 h-4"></i>
                    </button>
                    <button onclick="downloadEssay(${item.id})" class="text-purple-600 hover:text-purple-900 p-1 rounded touch-target" title="下载">
                        <i data-lucide="download" class="w-4 h-4"></i>
                    </button>
                    <button onclick="deleteEssay(${item.id})" class="text-red-600 hover:text-red-900 p-1 rounded touch-target" title="删除">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    historyElements.tableBody.innerHTML = html;
    initializeLucideIcons();
    updatePaginationInfo();
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
    switch (status) {
        case 'completed':
            return 'badge-success';
        case 'processing':
            return 'badge-warning';
        case 'draft':
            return 'badge-primary';
        default:
            return 'badge-primary';
    }
}

// 渲染分页
function renderPagination() {
    if (!historyElements.pagination) return;
    
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    
    if (totalPages <= 1) {
        historyElements.pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页按钮
    html += `
        <button onclick="changePage(${currentPage - 1})" 
                ${currentPage === 1 ? 'disabled' : ''} 
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <i data-lucide="chevron-left" class="w-4 h-4"></i>
        </button>
    `;
    
    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            html += `
                <button onclick="changePage(${i})" 
                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            i === currentPage 
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            html += `
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    ...
                </span>
            `;
        }
    }
    
    // 下一页按钮
    html += `
        <button onclick="changePage(${currentPage + 1})" 
                ${currentPage === totalPages ? 'disabled' : ''} 
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <i data-lucide="chevron-right" class="w-4 h-4"></i>
        </button>
    `;
    
    historyElements.pagination.innerHTML = html;
    initializeLucideIcons();
}

// 更新分页信息
function updatePaginationInfo() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredData.length);
    
    if (historyElements.startItem) {
        historyElements.startItem.textContent = filteredData.length > 0 ? startIndex + 1 : 0;
    }
    if (historyElements.endItem) {
        historyElements.endItem.textContent = endIndex;
    }
    if (historyElements.totalItems) {
        historyElements.totalItems.textContent = filteredData.length;
    }
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderHistoryTable();
    renderPagination();
}

// 论文操作函数
function viewEssay(id) {
    const essay = historyData.find(item => item.id === id);
    if (essay) {
        showToast(`查看论文：${essay.title}`, 'info');
        // 这里可以跳转到论文详情页面
    }
}

function editEssay(id) {
    const essay = historyData.find(item => item.id === id);
    if (essay) {
        showToast(`编辑论文：${essay.title}`, 'info');
        // 这里可以跳转到编辑页面
    }
}

function downloadEssay(id) {
    const essay = historyData.find(item => item.id === id);
    if (essay) {
        showToast(`下载论文：${essay.title}`, 'success');
        // 这里可以实现下载功能
    }
}

function deleteEssay(id) {
    const essay = historyData.find(item => item.id === id);
    if (essay && confirm(`确定要删除论文"${essay.title}"吗？`)) {
        const index = historyData.findIndex(item => item.id === id);
        if (index > -1) {
            historyData.splice(index, 1);
            applyFilters();
            showToast('论文已删除', 'success');
        }
    }
}

// 导出历史记录
function exportHistory() {
    showToast('正在导出历史记录...', 'info');
    
    // 模拟导出过程
    setTimeout(() => {
        const csvContent = generateCSV(filteredData);
        downloadCSV(csvContent, 'history.csv');
        showToast('导出成功', 'success');
    }, 1000);
}

// 生成CSV内容
function generateCSV(data) {
    const headers = ['ID', '标题', '类型', '状态', '字数', '创建时间', '更新时间'];
    const rows = data.map(item => [
        item.id,
        `"${item.title}"`,
        item.typeName,
        item.statusName,
        item.wordCount,
        item.createdAt,
        item.updatedAt
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// 下载CSV文件
function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 移动端菜单控制
function toggleMobileMenu() {
    if (historyElements.sidebar) {
        historyElements.sidebar.classList.toggle('-translate-x-full');
    }
}

function closeMobileMenu() {
    if (historyElements.sidebar) {
        historyElements.sidebar.classList.add('-translate-x-full');
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
