// 个人中心页面状态
let profileState = {
    currentTab: 'profile',
    userInfo: {
        name: '张三',
        email: 'z<PERSON><PERSON>@example.com',
        phone: '138****8888',
        organization: '清华大学',
        field: 'computer',
        education: 'master',
        bio: '计算机科学专业研究生，专注于人工智能和机器学习领域的研究。'
    },
    preferences: {
        language: 'zh-CN',
        theme: 'light',
        notifications: {
            email: true,
            sms: false,
            push: true
        },
        privacy: {
            profileVisible: true,
            showEmail: false,
            showPhone: false
        }
    }
};

// 偏好设置配置
const PREFERENCE_SETTINGS = [
    {
        category: '界面设置',
        icon: 'monitor',
        color: 'from-blue-500 to-blue-600',
        settings: [
            {
                key: 'language',
                label: '界面语言',
                type: 'select',
                options: {
                    'zh-CN': '简体中文',
                    'en-US': 'English',
                    'ja-JP': '日本語'
                },
                value: 'zh-CN'
            },
            {
                key: 'theme',
                label: '主题模式',
                type: 'select',
                options: {
                    'light': '浅色模式',
                    'dark': '深色模式',
                    'auto': '跟随系统'
                },
                value: 'light'
            }
        ]
    },
    {
        category: '通知设置',
        icon: 'bell',
        color: 'from-green-500 to-green-600',
        settings: [
            {
                key: 'email_notifications',
                label: '邮件通知',
                type: 'toggle',
                value: true,
                desc: '接收重要更新和账单信息'
            },
            {
                key: 'sms_notifications',
                label: '短信通知',
                type: 'toggle',
                value: false,
                desc: '接收验证码和紧急通知'
            },
            {
                key: 'push_notifications',
                label: '推送通知',
                type: 'toggle',
                value: true,
                desc: '接收浏览器推送消息'
            }
        ]
    },
    {
        category: '隐私设置',
        icon: 'shield',
        color: 'from-purple-500 to-purple-600',
        settings: [
            {
                key: 'profile_visible',
                label: '公开个人资料',
                type: 'toggle',
                value: true,
                desc: '允许其他用户查看您的基本信息'
            },
            {
                key: 'show_email',
                label: '显示邮箱地址',
                type: 'toggle',
                value: false,
                desc: '在个人资料中显示邮箱'
            },
            {
                key: 'show_phone',
                label: '显示手机号码',
                type: 'toggle',
                value: false,
                desc: '在个人资料中显示手机号'
            }
        ]
    }
];

// 登录记录数据
const LOGIN_HISTORY = [
    {
        time: '2024-01-15 14:30:25',
        location: '北京市',
        device: 'Chrome 浏览器',
        ip: '*************',
        status: 'success'
    },
    {
        time: '2024-01-14 09:15:42',
        location: '北京市',
        device: 'Safari 浏览器',
        ip: '*************',
        status: 'success'
    },
    {
        time: '2024-01-13 16:22:18',
        location: '上海市',
        device: 'Chrome 浏览器',
        ip: '*********',
        status: 'failed'
    },
    {
        time: '2024-01-12 11:45:33',
        location: '北京市',
        device: 'Firefox 浏览器',
        ip: '*************',
        status: 'success'
    }
];

// DOM 元素
let profileElements = {};

// 初始化个人中心页面
document.addEventListener('DOMContentLoaded', function() {
    initializeProfileElements();
    initializeLucideIcons();
    renderPreferences();
    renderLoginHistory();
    bindProfileEvents();
    loadProfileData();
});

// 初始化DOM元素引用
function initializeProfileElements() {
    profileElements = {
        tabButtons: document.querySelectorAll('.tab-button'),
        tabContents: document.querySelectorAll('.tab-content'),
        profileForm: document.getElementById('profile-form'),
        passwordForm: document.getElementById('password-form'),
        preferencesContent: document.getElementById('preferences-content'),
        loginHistory: document.getElementById('login-history'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuBtn: document.getElementById('mobile-menu-btn')
    };
}

// 渲染偏好设置
function renderPreferences() {
    if (!profileElements.preferencesContent) return;
    
    const html = PREFERENCE_SETTINGS.map((category, categoryIndex) => `
        <div class="preference-category mb-8 fade-in-up" style="animation-delay: ${categoryIndex * 0.1}s;">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center">
                    <i data-lucide="${category.icon}" class="w-4 h-4 text-white"></i>
                </div>
                <h4 class="text-lg font-bold text-gray-900">${category.category}</h4>
            </div>
            <div class="space-y-4">
                ${category.settings.map(setting => `
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="flex-1">
                            <h5 class="font-medium text-gray-900">${setting.label}</h5>
                            ${setting.desc ? `<p class="text-sm text-gray-600 mt-1">${setting.desc}</p>` : ''}
                        </div>
                        <div class="ml-4">
                            ${setting.type === 'toggle' ? `
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" ${setting.value ? 'checked' : ''} 
                                           onchange="updatePreference('${setting.key}', this.checked)">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            ` : `
                                <select class="form-input form-select min-w-[120px]" 
                                        onchange="updatePreference('${setting.key}', this.value)">
                                    ${Object.entries(setting.options).map(([value, label]) => `
                                        <option value="${value}" ${setting.value === value ? 'selected' : ''}>${label}</option>
                                    `).join('')}
                                </select>
                            `}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');
    
    profileElements.preferencesContent.innerHTML = html;
    initializeLucideIcons();
}

// 渲染登录记录
function renderLoginHistory() {
    if (!profileElements.loginHistory) return;
    
    const html = LOGIN_HISTORY.map(record => `
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 ${
                    record.status === 'success' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-red-100 text-red-600'
                } rounded-lg flex items-center justify-center">
                    <i data-lucide="${record.status === 'success' ? 'check' : 'x'}" class="w-4 h-4"></i>
                </div>
                <div>
                    <div class="font-medium text-gray-900">${record.time}</div>
                    <div class="text-sm text-gray-600">${record.location} · ${record.device}</div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-500">${record.ip}</div>
                <div class="text-xs ${
                    record.status === 'success' ? 'text-green-600' : 'text-red-600'
                }">${record.status === 'success' ? '成功' : '失败'}</div>
            </div>
        </div>
    `).join('');
    
    profileElements.loginHistory.innerHTML = html;
    initializeLucideIcons();
}

// 绑定个人中心页面事件
function bindProfileEvents() {
    // 标签页切换
    profileElements.tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 个人信息表单提交
    if (profileElements.profileForm) {
        profileElements.profileForm.addEventListener('submit', handleProfileSubmit);
    }
    
    // 密码表单提交
    if (profileElements.passwordForm) {
        profileElements.passwordForm.addEventListener('submit', handlePasswordSubmit);
    }
    
    // 移动端菜单
    if (profileElements.mobileMenuBtn) {
        profileElements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 点击外部关闭侧边栏
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024 && 
            profileElements.sidebar && 
            !profileElements.sidebar.contains(e.target) && 
            profileElements.mobileMenuBtn &&
            !profileElements.mobileMenuBtn.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

// 切换标签页
function switchTab(tab) {
    profileState.currentTab = tab;
    
    // 更新标签页样式
    profileElements.tabButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('border-gray-500', 'text-gray-600', 'active');
            button.classList.remove('border-transparent', 'text-gray-500');
        } else {
            button.classList.remove('border-gray-500', 'text-gray-600', 'active');
            button.classList.add('border-transparent', 'text-gray-500');
        }
    });
    
    // 切换内容区域
    profileElements.tabContents.forEach(content => {
        if (content.id === `${tab}-tab`) {
            content.classList.remove('hidden');
        } else {
            content.classList.add('hidden');
        }
    });
}

// 处理个人信息表单提交
function handleProfileSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const updatedInfo = {};
    
    for (const [key, value] of formData.entries()) {
        updatedInfo[key] = value;
    }
    
    // 更新状态
    profileState.userInfo = { ...profileState.userInfo, ...updatedInfo };
    
    // 保存数据
    saveProfileData();
    
    showToast('个人信息已更新', 'success');
}

// 处理密码表单提交
function handlePasswordSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const currentPassword = formData.get('current_password');
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    // 验证密码
    if (newPassword !== confirmPassword) {
        showToast('新密码和确认密码不匹配', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showToast('密码长度至少6位', 'error');
        return;
    }
    
    // 模拟密码更新
    showToast('密码已更新', 'success');
    e.target.reset();
}

// 更新偏好设置
function updatePreference(key, value) {
    // 更新状态
    if (key.includes('_')) {
        const [category, setting] = key.split('_');
        if (!profileState.preferences[category]) {
            profileState.preferences[category] = {};
        }
        profileState.preferences[category][setting] = value;
    } else {
        profileState.preferences[key] = value;
    }
    
    // 保存数据
    saveProfileData();
    
    showToast(`${key}设置已更新`, 'success');
}

// 数据持久化
function saveProfileData() {
    const data = {
        currentTab: profileState.currentTab,
        userInfo: profileState.userInfo,
        preferences: profileState.preferences
    };
    
    try {
        localStorage.setItem('profileData', JSON.stringify(data));
    } catch (error) {
        console.error('Failed to save profile data:', error);
    }
}

function loadProfileData() {
    try {
        const savedData = localStorage.getItem('profileData');
        if (savedData) {
            const data = JSON.parse(savedData);
            
            if (data.userInfo) {
                profileState.userInfo = { ...profileState.userInfo, ...data.userInfo };
                
                // 更新表单值
                if (profileElements.profileForm) {
                    Object.entries(data.userInfo).forEach(([key, value]) => {
                        const field = profileElements.profileForm.querySelector(`[name="${key}"]`);
                        if (field) {
                            field.value = value;
                        }
                    });
                }
            }
            
            if (data.preferences) {
                profileState.preferences = { ...profileState.preferences, ...data.preferences };
            }
            
            if (data.currentTab) {
                switchTab(data.currentTab);
            }
        }
    } catch (error) {
        console.error('Failed to load profile data:', error);
    }
}

// 移动端菜单控制
function toggleMobileMenu() {
    if (profileElements.sidebar) {
        profileElements.sidebar.classList.toggle('-translate-x-full');
    }
}

function closeMobileMenu() {
    if (profileElements.sidebar) {
        profileElements.sidebar.classList.add('-translate-x-full');
    }
}

// 工具函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
