/**
 * 简化的性能监控
 * 轻量级性能监控，不影响原有功能
 */

class SimplePerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.startTime = performance.now();
        this.init();
    }

    init() {
        // 监控Core Web Vitals
        this.observeCoreWebVitals();
        
        // 监控资源加载
        this.observeResourceTiming();
        
        // 定期报告
        this.startReporting();
    }

    observeCoreWebVitals() {
        if (!('PerformanceObserver' in window)) return;

        try {
            // LCP监控
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.recordMetric('lcp', lastEntry.startTime);
            }).observe({ entryTypes: ['largest-contentful-paint'] });

            // FID监控
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.recordMetric('fid', entry.processingStart - entry.startTime);
                });
            }).observe({ entryTypes: ['first-input'] });

            // CLS监控
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        this.recordMetric('cls', entry.value);
                    }
                });
            }).observe({ entryTypes: ['layout-shift'] });

        } catch (error) {
            console.warn('Performance monitoring setup failed:', error);
        }
    }

    observeResourceTiming() {
        if (!('PerformanceObserver' in window)) return;

        try {
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    const duration = entry.responseEnd - entry.startTime;
                    if (duration > 1000) { // 超过1秒的慢资源
                        this.recordMetric('slow_resource', {
                            name: entry.name,
                            duration: duration
                        });
                    }
                });
            }).observe({ entryTypes: ['resource'] });
        } catch (error) {
            console.warn('Resource timing monitoring failed:', error);
        }
    }

    recordMetric(name, value) {
        if (!this.metrics[name]) {
            this.metrics[name] = [];
        }
        
        this.metrics[name].push({
            value: value,
            timestamp: Date.now()
        });

        // 限制存储数量
        if (this.metrics[name].length > 10) {
            this.metrics[name].shift();
        }
    }

    getMetrics() {
        return this.metrics;
    }

    generateReport() {
        const report = {
            timestamp: Date.now(),
            sessionDuration: Date.now() - this.startTime,
            metrics: this.getMetrics(),
            memory: this.getMemoryInfo(),
            connection: this.getConnectionInfo()
        };

        // 发送到控制台（可以扩展为发送到服务器）
        console.log('Performance Report:', report);
        return report;
    }

    getMemoryInfo() {
        if ('memory' in performance) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    getConnectionInfo() {
        if ('connection' in navigator) {
            return {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }
        return null;
    }

    startReporting() {
        // 页面加载完成后报告
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.generateReport();
            }, 2000);
        });

        // 页面卸载前报告
        window.addEventListener('beforeunload', () => {
            this.generateReport();
        });

        // 定期报告（每5分钟）
        setInterval(() => {
            this.generateReport();
        }, 5 * 60 * 1000);
    }
}

// 全局实例
window.performanceMonitor = new SimplePerformanceMonitor();
