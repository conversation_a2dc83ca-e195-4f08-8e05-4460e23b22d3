/**
 * 性能优化配置
 * 管理资源加载、缓存、懒加载等性能相关配置
 */

export const PerformanceConfig = {
    // 资源加载配置
    loading: {
        // 预加载配置
        preload: {
            enabled: true,
            resources: [
                { href: 'styles/main.css', as: 'style' },
                { href: 'js/components.js', as: 'script' },
                { href: 'js/auth.js', as: 'script' }
            ],
            criticalPages: ['history.html', 'plagiarism.html'],
            maxConcurrent: 3
        },
        
        // 懒加载配置
        lazyLoad: {
            enabled: true,
            threshold: 0.1, // 10% 可见时触发
            rootMargin: '50px',
            images: {
                placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PC9zdmc+',
                fadeInDuration: 300
            },
            modules: {
                threshold: 0.2,
                timeout: 5000
            }
        },
        
        // 代码分割配置
        codeSplitting: {
            enabled: true,
            chunks: {
                vendor: ['lucide', 'tailwindcss'],
                common: ['components', 'auth', 'utils'],
                pages: {
                    home: ['main', 'essay-forms'],
                    history: ['history'],
                    plagiarism: ['plagiarism'],
                    billing: ['billing'],
                    profile: ['profile']
                }
            }
        }
    },

    // 缓存策略配置
    cache: {
        // Service Worker 缓存
        serviceWorker: {
            enabled: true,
            version: '1.0.0',
            strategies: {
                static: 'cache-first', // CSS, JS, 图片
                api: 'network-first',  // API 请求
                pages: 'stale-while-revalidate' // HTML 页面
            },
            maxAge: {
                static: 7 * 24 * 60 * 60 * 1000, // 7天
                api: 5 * 60 * 1000, // 5分钟
                pages: 24 * 60 * 60 * 1000 // 1天
            }
        },
        
        // 内存缓存
        memory: {
            enabled: true,
            maxSize: 50, // 最大缓存项数
            ttl: 10 * 60 * 1000, // 10分钟
            strategies: {
                lru: true, // 最近最少使用
                compression: false
            }
        },
        
        // 浏览器缓存
        browser: {
            localStorage: {
                maxSize: 5 * 1024 * 1024, // 5MB
                compression: true,
                encryption: false
            },
            sessionStorage: {
                maxSize: 2 * 1024 * 1024, // 2MB
                autoCleanup: true
            }
        }
    },

    // 网络优化配置
    network: {
        // 请求优化
        requests: {
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000,
            maxConcurrent: 6,
            priority: {
                critical: 1,
                high: 2,
                normal: 3,
                low: 4
            }
        },
        
        // 连接优化
        connection: {
            dns: {
                prefetch: ['cdn.tailwindcss.com', 'unpkg.com'],
                preconnect: ['https://fonts.googleapis.com']
            },
            http2: {
                serverPush: true,
                multiplexing: true
            }
        },
        
        // 压缩配置
        compression: {
            gzip: true,
            brotli: true,
            threshold: 1024 // 1KB以上才压缩
        }
    },

    // 渲染优化配置
    rendering: {
        // 虚拟滚动
        virtualScroll: {
            enabled: true,
            itemHeight: 80,
            bufferSize: 5,
            threshold: 100 // 超过100项启用
        },
        
        // 防抖节流
        debounce: {
            search: 300,
            resize: 250,
            scroll: 16, // ~60fps
            input: 300
        },
        
        // 动画优化
        animations: {
            reducedMotion: true, // 尊重用户偏好
            gpuAcceleration: true,
            willChange: true,
            transform3d: true
        },
        
        // 图片优化
        images: {
            webp: true,
            avif: false,
            responsive: true,
            quality: 85,
            progressive: true
        }
    },

    // 监控配置
    monitoring: {
        // 性能指标
        metrics: {
            enabled: true,
            fcp: true, // First Contentful Paint
            lcp: true, // Largest Contentful Paint
            fid: true, // First Input Delay
            cls: true, // Cumulative Layout Shift
            ttfb: true // Time to First Byte
        },
        
        // 错误监控
        errors: {
            enabled: true,
            maxErrors: 50,
            sampling: 0.1, // 10% 采样率
            ignorePatterns: [
                /Script error/,
                /Non-Error promise rejection/
            ]
        },
        
        // 用户体验监控
        ux: {
            enabled: true,
            trackClicks: true,
            trackScrolls: true,
            trackFormSubmissions: true,
            heatmap: false
        }
    },

    // 优化阈值
    thresholds: {
        // 性能预算
        budget: {
            totalSize: 2 * 1024 * 1024, // 2MB
            jsSize: 500 * 1024, // 500KB
            cssSize: 200 * 1024, // 200KB
            imageSize: 1 * 1024 * 1024, // 1MB
            fontSize: 100 * 1024 // 100KB
        },
        
        // 性能指标阈值
        performance: {
            fcp: 1800, // 1.8s
            lcp: 2500, // 2.5s
            fid: 100,  // 100ms
            cls: 0.1,  // 0.1
            ttfb: 600  // 600ms
        }
    }
};

// 性能监控类
export class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.init();
    }

    init() {
        if (!PerformanceConfig.monitoring.metrics.enabled) return;

        this.setupPerformanceObserver();
        this.setupErrorMonitoring();
        this.setupUserExperienceTracking();
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric(entry.name, entry.value || entry.duration);
                }
            });

            observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
            this.observers.set('performance', observer);
        }
    }

    setupErrorMonitoring() {
        if (!PerformanceConfig.monitoring.errors.enabled) return;

        window.addEventListener('error', (event) => {
            this.recordError({
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                message: 'Unhandled Promise Rejection',
                reason: event.reason
            });
        });
    }

    setupUserExperienceTracking() {
        if (!PerformanceConfig.monitoring.ux.enabled) return;

        // 实现用户体验跟踪逻辑
        this.trackPageVisibility();
        this.trackUserInteractions();
    }

    recordMetric(name, value) {
        this.metrics.set(name, {
            value,
            timestamp: Date.now()
        });

        // 检查是否超过阈值
        this.checkThreshold(name, value);
    }

    recordError(error) {
        const errors = this.metrics.get('errors') || [];
        errors.push({
            ...error,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });

        // 限制错误数量
        if (errors.length > PerformanceConfig.monitoring.errors.maxErrors) {
            errors.shift();
        }

        this.metrics.set('errors', errors);
    }

    checkThreshold(name, value) {
        const threshold = PerformanceConfig.thresholds.performance[name];
        if (threshold && value > threshold) {
            console.warn(`Performance threshold exceeded for ${name}: ${value}ms > ${threshold}ms`);
        }
    }

    getMetrics() {
        return Object.fromEntries(this.metrics);
    }

    trackPageVisibility() {
        document.addEventListener('visibilitychange', () => {
            this.recordMetric('page_visibility', document.hidden ? 'hidden' : 'visible');
        });
    }

    trackUserInteractions() {
        ['click', 'scroll', 'keydown'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                this.recordMetric(`user_${eventType}`, Date.now());
            }, { passive: true });
        });
    }
}

// 初始化性能配置
export function initializePerformanceConfig() {
    // 设置全局性能配置
    window.PERFORMANCE_CONFIG = PerformanceConfig;
    
    // 启动性能监控
    if (PerformanceConfig.monitoring.metrics.enabled) {
        window.performanceMonitor = new PerformanceMonitor();
    }
    
    // 发送性能配置就绪事件
    window.dispatchEvent(new CustomEvent('performanceConfigReady', { 
        detail: PerformanceConfig 
    }));
}
